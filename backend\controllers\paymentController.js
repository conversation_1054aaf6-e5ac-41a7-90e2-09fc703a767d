// ProChat Payment Controller
// Complete controller for all payment operations

const paymentGatewayService = require('../services/paymentGatewayService');
const Transaction = require('../models/Transaction');
const User = require('../models/User');
const { SecurityLogger } = require('../config/security');
const { sendNotification } = require('../services/notificationService');
const { validatePaymentLimits } = require('../utils/paymentValidation');

class PaymentController {
  // ===========================================
  // GENERAL PAYMENT OPERATIONS
  // ===========================================

  async processPayment(req, res) {
    try {
      const { amount, currency, provider, description, metadata } = req.body;
      const userId = req.user.id;

      // Validate payment limits
      const limitValidation = await validatePaymentLimits(userId, amount, currency);
      if (!limitValidation.valid) {
        return res.status(400).json({
          success: false,
          message: limitValidation.message,
          code: 'LIMIT_EXCEEDED',
        });
      }

      // Process payment through gateway service
      const paymentData = {
        ...req.body,
        metadata: { ...metadata, userId },
      };

      const result = await paymentGatewayService.processPayment(paymentData);

      // Log payment attempt
      SecurityLogger.logSecurityEvent('PAYMENT_ATTEMPT', {
        userId,
        amount,
        currency,
        provider,
        success: result.success,
        transactionId: result.transactionId,
      });

      // Send notification to user
      if (result.success) {
        await sendNotification(userId, {
          type: 'payment_initiated',
          title: 'Malipo Yameanza',
          message: `Malipo ya ${currency} ${amount.toLocaleString()} yameanza kwa ${provider}`,
          data: { transactionId: result.transactionId },
        });
      }

      res.json(result);
    } catch (error) {
      console.error('Payment processing error:', error);
      res.status(500).json({
        success: false,
        message: 'Kuna tatizo la kiufundi. Jaribu tena.',
        code: 'INTERNAL_ERROR',
      });
    }
  }

  async processWithdrawal(req, res) {
    try {
      const { amount, currency, provider, customerPhone, customerAccount, description } = req.body;
      const userId = req.user.id;

      // Check user balance
      const user = await User.findById(userId);
      if (!user || user.balance < amount) {
        return res.status(400).json({
          success: false,
          message: 'Huna pesa za kutosha',
          code: 'INSUFFICIENT_BALANCE',
        });
      }

      // Process withdrawal
      const withdrawalData = {
        ...req.body,
        metadata: { userId },
      };

      const result = await paymentGatewayService.processWithdrawal(withdrawalData);

      // Log withdrawal attempt
      SecurityLogger.logSecurityEvent('WITHDRAWAL_ATTEMPT', {
        userId,
        amount,
        currency,
        provider,
        success: result.success,
        transactionId: result.transactionId,
      });

      // Send notification
      if (result.success) {
        await sendNotification(userId, {
          type: 'withdrawal_initiated',
          title: 'Uondoaji wa Pesa Umeanza',
          message: `Uondoaji wa ${currency} ${amount.toLocaleString()} umeanza`,
          data: { transactionId: result.transactionId },
        });
      }

      res.json(result);
    } catch (error) {
      console.error('Withdrawal processing error:', error);
      res.status(500).json({
        success: false,
        message: 'Kuna tatizo la kiufundi. Jaribu tena.',
        code: 'INTERNAL_ERROR',
      });
    }
  }

  async getTransactionStatus(req, res) {
    try {
      const { transactionId } = req.params;
      const userId = req.user.id;

      // Get transaction from database
      const transaction = await Transaction.findOne({
        $or: [
          { id: transactionId },
          { providerTransactionId: transactionId }
        ],
        userId: userId, // Ensure user can only see their own transactions
      });

      if (!transaction) {
        return res.status(404).json({
          success: false,
          message: 'Muamala haujapatikana',
          code: 'TRANSACTION_NOT_FOUND',
        });
      }

      // Get latest status from gateway service
      const gatewayStatus = paymentGatewayService.getTransactionStatus(transactionId);
      
      res.json({
        success: true,
        transaction: {
          id: transaction.id,
          amount: transaction.amount,
          currency: transaction.currency,
          provider: transaction.provider,
          status: gatewayStatus?.status || transaction.status,
          createdAt: transaction.createdAt,
          updatedAt: transaction.updatedAt,
          description: transaction.description,
        },
      });
    } catch (error) {
      console.error('Get transaction status error:', error);
      res.status(500).json({
        success: false,
        message: 'Imeshindwa kupata hali ya muamala',
        code: 'INTERNAL_ERROR',
      });
    }
  }

  async getPaymentHistory(req, res) {
    try {
      const userId = req.user.id;
      const { page = 1, limit = 20, provider, status, startDate, endDate } = req.query;

      // Build query
      const query = { userId };
      
      if (provider) query.provider = provider;
      if (status) query.status = status;
      if (startDate || endDate) {
        query.createdAt = {};
        if (startDate) query.createdAt.$gte = new Date(startDate);
        if (endDate) query.createdAt.$lte = new Date(endDate);
      }

      // Get transactions with pagination
      const transactions = await Transaction.find(query)
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit)
        .select('-metadata.userId'); // Don't expose user ID in metadata

      const total = await Transaction.countDocuments(query);

      res.json({
        success: true,
        transactions,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      });
    } catch (error) {
      console.error('Get payment history error:', error);
      res.status(500).json({
        success: false,
        message: 'Imeshindwa kupata historia ya malipo',
        code: 'INTERNAL_ERROR',
      });
    }
  }

  // ===========================================
  // MOBILE MONEY OPERATIONS
  // ===========================================

  async processMpesaPayment(req, res) {
    try {
      const paymentData = { ...req.body, provider: 'mpesa' };
      const result = await paymentGatewayService.processMpesaPayment(paymentData);
      res.json(result);
    } catch (error) {
      console.error('M-Pesa payment error:', error);
      res.status(500).json({
        success: false,
        message: 'Malipo ya M-Pesa yameshindwa',
        code: 'MPESA_ERROR',
      });
    }
  }

  async processMpesaWithdrawal(req, res) {
    try {
      const withdrawalData = { ...req.body, provider: 'mpesa' };
      const result = await paymentGatewayService.processMpesaWithdrawal(withdrawalData);
      res.json(result);
    } catch (error) {
      console.error('M-Pesa withdrawal error:', error);
      res.status(500).json({
        success: false,
        message: 'Uondoaji wa pesa kwa M-Pesa umeshindwa',
        code: 'MPESA_WITHDRAWAL_ERROR',
      });
    }
  }

  async processAirtelPayment(req, res) {
    try {
      const paymentData = { ...req.body, provider: 'airtel_money' };
      const result = await paymentGatewayService.processAirtelPayment(paymentData);
      res.json(result);
    } catch (error) {
      console.error('Airtel Money payment error:', error);
      res.status(500).json({
        success: false,
        message: 'Malipo ya Airtel Money yameshindwa',
        code: 'AIRTEL_ERROR',
      });
    }
  }

  async processAirtelWithdrawal(req, res) {
    try {
      const withdrawalData = { ...req.body, provider: 'airtel_money' };
      const result = await paymentGatewayService.processAirtelWithdrawal(withdrawalData);
      res.json(result);
    } catch (error) {
      console.error('Airtel Money withdrawal error:', error);
      res.status(500).json({
        success: false,
        message: 'Uondoaji wa pesa kwa Airtel Money umeshindwa',
        code: 'AIRTEL_WITHDRAWAL_ERROR',
      });
    }
  }

  async processTigoPayment(req, res) {
    try {
      const paymentData = { ...req.body, provider: 'tigo_pesa' };
      const result = await paymentGatewayService.processTigoPayment(paymentData);
      res.json(result);
    } catch (error) {
      console.error('Tigo Pesa payment error:', error);
      res.status(500).json({
        success: false,
        message: 'Malipo ya Tigo Pesa yameshindwa',
        code: 'TIGO_ERROR',
      });
    }
  }

  async processTigoWithdrawal(req, res) {
    try {
      const withdrawalData = { ...req.body, provider: 'tigo_pesa' };
      const result = await paymentGatewayService.processTigoWithdrawal(withdrawalData);
      res.json(result);
    } catch (error) {
      console.error('Tigo Pesa withdrawal error:', error);
      res.status(500).json({
        success: false,
        message: 'Uondoaji wa pesa kwa Tigo Pesa umeshindwa',
        code: 'TIGO_WITHDRAWAL_ERROR',
      });
    }
  }

  async processHaloPayment(req, res) {
    try {
      const paymentData = { ...req.body, provider: 'halopesa' };
      const result = await paymentGatewayService.processHaloPayment(paymentData);
      res.json(result);
    } catch (error) {
      console.error('HaloPesa payment error:', error);
      res.status(500).json({
        success: false,
        message: 'Malipo ya HaloPesa yameshindwa',
        code: 'HALOPESA_ERROR',
      });
    }
  }

  async processAzamPayment(req, res) {
    try {
      const paymentData = { ...req.body, provider: 'azampesa' };
      const result = await paymentGatewayService.processAzamPayment(paymentData);
      res.json(result);
    } catch (error) {
      console.error('AzamPesa payment error:', error);
      res.status(500).json({
        success: false,
        message: 'Malipo ya AzamPesa yameshindwa',
        code: 'AZAMPESA_ERROR',
      });
    }
  }

  async processNaraPayment(req, res) {
    try {
      const paymentData = { ...req.body, provider: 'nara' };
      const result = await paymentGatewayService.processNaraPayment(paymentData);
      res.json(result);
    } catch (error) {
      console.error('NARA payment error:', error);
      res.status(500).json({
        success: false,
        message: 'Malipo ya NARA yameshindwa',
        code: 'NARA_ERROR',
      });
    }
  }

  // ===========================================
  // BANKING OPERATIONS
  // ===========================================

  async processCrdbPayment(req, res) {
    try {
      const paymentData = { ...req.body, provider: 'crdb_bank' };
      const result = await paymentGatewayService.processCrdbPayment(paymentData);
      res.json(result);
    } catch (error) {
      console.error('CRDB Bank payment error:', error);
      res.status(500).json({
        success: false,
        message: 'Malipo ya CRDB Bank yameshindwa',
        code: 'CRDB_ERROR',
      });
    }
  }

  async processNmbPayment(req, res) {
    try {
      const paymentData = { ...req.body, provider: 'nmb_bank' };
      const result = await paymentGatewayService.processNmbPayment(paymentData);
      res.json(result);
    } catch (error) {
      console.error('NMB Bank payment error:', error);
      res.status(500).json({
        success: false,
        message: 'Malipo ya NMB Bank yameshindwa',
        code: 'NMB_ERROR',
      });
    }
  }

  async processNbcPayment(req, res) {
    try {
      const paymentData = { ...req.body, provider: 'nbc_bank' };
      const result = await paymentGatewayService.processNbcPayment(paymentData);
      res.json(result);
    } catch (error) {
      console.error('NBC Bank payment error:', error);
      res.status(500).json({
        success: false,
        message: 'Malipo ya NBC Bank yameshindwa',
        code: 'NBC_ERROR',
      });
    }
  }

  async verifyBankAccount(req, res) {
    try {
      const { bankCode, accountNumber } = req.body;
      
      // Mock bank account verification
      // In real implementation, this would call the bank's API
      const isValid = accountNumber.length >= 10 && accountNumber.length <= 20;
      
      res.json({
        success: true,
        valid: isValid,
        accountInfo: isValid ? {
          accountNumber,
          accountName: 'John Doe', // This would come from bank API
          bankName: this.getBankName(bankCode),
        } : null,
      });
    } catch (error) {
      console.error('Bank account verification error:', error);
      res.status(500).json({
        success: false,
        message: 'Imeshindwa kuthibitisha akaunti ya benki',
        code: 'VERIFICATION_ERROR',
      });
    }
  }

  getBankName(bankCode) {
    const bankNames = {
      'CRDB': 'CRDB Bank',
      'NMB': 'NMB Bank',
      'NBC': 'NBC Bank',
    };
    return bankNames[bankCode] || 'Unknown Bank';
  }

  // ===========================================
  // CARD PAYMENT OPERATIONS
  // ===========================================

  async processVisaPayment(req, res) {
    try {
      const paymentData = { ...req.body, provider: 'visa' };
      const result = await paymentGatewayService.processVisaPayment(paymentData);
      res.json(result);
    } catch (error) {
      console.error('Visa payment error:', error);
      res.status(500).json({
        success: false,
        message: 'Malipo ya Visa yameshindwa',
        code: 'VISA_ERROR',
      });
    }
  }

  async processMastercardPayment(req, res) {
    try {
      const paymentData = { ...req.body, provider: 'mastercard' };
      const result = await paymentGatewayService.processMastercardPayment(paymentData);
      res.json(result);
    } catch (error) {
      console.error('Mastercard payment error:', error);
      res.status(500).json({
        success: false,
        message: 'Malipo ya Mastercard yameshindwa',
        code: 'MASTERCARD_ERROR',
      });
    }
  }

  async processStripePayment(req, res) {
    try {
      const paymentData = { ...req.body, provider: 'stripe' };
      const result = await paymentGatewayService.processStripePayment(paymentData);
      res.json(result);
    } catch (error) {
      console.error('Stripe payment error:', error);
      res.status(500).json({
        success: false,
        message: 'Malipo ya kadi yameshindwa',
        code: 'STRIPE_ERROR',
      });
    }
  }

  async createPaymentIntent(req, res) {
    try {
      const { amount, currency, paymentMethod } = req.body;
      const userId = req.user.id;

      // Create payment intent
      const intent = {
        id: `pi_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        amount,
        currency,
        paymentMethod,
        status: 'requires_confirmation',
        clientSecret: `pi_${Date.now()}_secret_${Math.random().toString(36).substr(2, 9)}`,
        userId,
        createdAt: Date.now(),
      };

      res.json({
        success: true,
        paymentIntent: intent,
      });
    } catch (error) {
      console.error('Create payment intent error:', error);
      res.status(500).json({
        success: false,
        message: 'Imeshindwa kutengeneza nia ya malipo',
        code: 'INTENT_CREATION_ERROR',
      });
    }
  }

  async confirmPaymentIntent(req, res) {
    try {
      const { intentId } = req.params;
      const { paymentMethod } = req.body;

      // Mock payment intent confirmation
      const result = {
        success: true,
        paymentIntent: {
          id: intentId,
          status: 'succeeded',
          amount: 50000,
          currency: 'TZS',
          paymentMethod,
        },
        transactionId: `txn_${Date.now()}`,
      };

      res.json(result);
    } catch (error) {
      console.error('Confirm payment intent error:', error);
      res.status(500).json({
        success: false,
        message: 'Imeshindwa kuthibitisha nia ya malipo',
        code: 'INTENT_CONFIRMATION_ERROR',
      });
    }
  }

  // ===========================================
  // CRYPTOCURRENCY OPERATIONS
  // ===========================================

  async processBitcoinPayment(req, res) {
    try {
      const paymentData = { ...req.body, provider: 'bitcoin' };
      const result = await paymentGatewayService.processBitcoinPayment(paymentData);
      res.json(result);
    } catch (error) {
      console.error('Bitcoin payment error:', error);
      res.status(500).json({
        success: false,
        message: 'Malipo ya Bitcoin yameshindwa',
        code: 'BITCOIN_ERROR',
      });
    }
  }

  async processUsdtPayment(req, res) {
    try {
      const paymentData = { ...req.body, provider: 'usdt' };
      const result = await paymentGatewayService.processUsdtPayment(paymentData);
      res.json(result);
    } catch (error) {
      console.error('USDT payment error:', error);
      res.status(500).json({
        success: false,
        message: 'Malipo ya USDT yameshindwa',
        code: 'USDT_ERROR',
      });
    }
  }

  async getCryptoWalletAddress(req, res) {
    try {
      const { currency } = req.params;
      const userId = req.user.id;

      // Mock crypto wallet address generation
      const addresses = {
        bitcoin: `1A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa_${userId}`,
        usdt: `0x742d35Cc6634C0532925a3b8D4C0532925a3b8D4_${userId}`,
      };

      const address = addresses[currency.toLowerCase()];
      if (!address) {
        return res.status(400).json({
          success: false,
          message: 'Sarafu isiyotambuliwa',
          code: 'UNSUPPORTED_CURRENCY',
        });
      }

      res.json({
        success: true,
        currency: currency.toUpperCase(),
        address,
        qrCode: `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`, // Mock QR code
      });
    } catch (error) {
      console.error('Get crypto wallet address error:', error);
      res.status(500).json({
        success: false,
        message: 'Imeshindwa kupata anwani ya wallet',
        code: 'WALLET_ADDRESS_ERROR',
      });
    }
  }

  // ===========================================
  // UTILITY OPERATIONS
  // ===========================================

  async getAvailableProviders(req, res) {
    try {
      const providers = paymentGatewayService.getAvailableProviders();
      res.json({
        success: true,
        providers,
      });
    } catch (error) {
      console.error('Get available providers error:', error);
      res.status(500).json({
        success: false,
        message: 'Imeshindwa kupata watoa huduma wa malipo',
        code: 'PROVIDERS_ERROR',
      });
    }
  }

  async getProviderInfo(req, res) {
    try {
      const { provider } = req.params;
      const info = paymentGatewayService.getProviderInfo(provider);

      if (!info) {
        return res.status(404).json({
          success: false,
          message: 'Mtoa huduma hajapatikana',
          code: 'PROVIDER_NOT_FOUND',
        });
      }

      res.json({
        success: true,
        provider: info,
      });
    } catch (error) {
      console.error('Get provider info error:', error);
      res.status(500).json({
        success: false,
        message: 'Imeshindwa kupata taarifa za mtoa huduma',
        code: 'PROVIDER_INFO_ERROR',
      });
    }
  }

  async calculateFees(req, res) {
    try {
      const { amount, currency, provider } = req.body;

      // Mock fee calculation
      const providerInfo = paymentGatewayService.getProviderInfo(provider);
      if (!providerInfo) {
        return res.status(400).json({
          success: false,
          message: 'Mtoa huduma hajapatikana',
          code: 'PROVIDER_NOT_FOUND',
        });
      }

      const fees = providerInfo.fees || {};
      const percentageFee = (amount * (fees.percentage || 0.02));
      const fixedFee = fees.fixed || 0;
      const total = percentageFee + fixedFee;

      res.json({
        success: true,
        fees: {
          amount,
          currency,
          provider,
          percentage: percentageFee,
          fixed: fixedFee,
          total,
          breakdown: {
            percentageRate: (fees.percentage || 0.02) * 100 + '%',
            fixedAmount: fixedFee,
          },
        },
      });
    } catch (error) {
      console.error('Calculate fees error:', error);
      res.status(500).json({
        success: false,
        message: 'Imeshindwa kukokotoa ada',
        code: 'FEE_CALCULATION_ERROR',
      });
    }
  }

  async getExchangeRates(req, res) {
    try {
      const { from, to } = req.query;

      // Mock exchange rates
      const rates = {
        'TZS-USD': 0.00043,
        'USD-TZS': 2325,
        'TZS-EUR': 0.00039,
        'EUR-TZS': 2564,
        'TZS-KES': 0.041,
        'KES-TZS': 24.39,
      };

      const rateKey = `${from}-${to}`;
      const rate = rates[rateKey];

      if (!rate) {
        return res.status(400).json({
          success: false,
          message: 'Kiwango cha kubadilishana hakijapatikana',
          code: 'RATE_NOT_FOUND',
        });
      }

      res.json({
        success: true,
        exchangeRate: {
          from,
          to,
          rate,
          timestamp: Date.now(),
          source: 'ProChat Exchange',
        },
      });
    } catch (error) {
      console.error('Get exchange rates error:', error);
      res.status(500).json({
        success: false,
        message: 'Imeshindwa kupata viwango vya kubadilishana',
        code: 'EXCHANGE_RATE_ERROR',
      });
    }
  }

  async validatePaymentData(req, res) {
    try {
      const { amount, currency, provider, customerPhone, customerAccount } = req.body;

      const validation = {
        valid: true,
        errors: [],
      };

      // Validate amount
      if (!amount || amount < 100) {
        validation.valid = false;
        validation.errors.push('Kiasi cha chini ni TZS 100');
      }

      if (amount > ********) {
        validation.valid = false;
        validation.errors.push('Kiasi cha juu ni TZS 10M');
      }

      // Validate phone number for mobile money
      if (['mpesa', 'airtel_money', 'tigo_pesa', 'halopesa', 'azampesa', 'nara'].includes(provider)) {
        if (!customerPhone || !/^(\+255|0)[67]\d{8}$/.test(customerPhone)) {
          validation.valid = false;
          validation.errors.push('Nambari ya simu si sahihi');
        }
      }

      // Validate account number for banks
      if (['crdb_bank', 'nmb_bank', 'nbc_bank'].includes(provider)) {
        if (!customerAccount || customerAccount.length < 10) {
          validation.valid = false;
          validation.errors.push('Nambari ya akaunti si sahihi');
        }
      }

      res.json({
        success: true,
        validation,
      });
    } catch (error) {
      console.error('Validate payment data error:', error);
      res.status(500).json({
        success: false,
        message: 'Imeshindwa kuthibitisha data ya malipo',
        code: 'VALIDATION_ERROR',
      });
    }
  }
}

module.exports = new PaymentController();
