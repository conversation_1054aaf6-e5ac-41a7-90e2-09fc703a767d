// ProChat Admin Route Protection Component
// Military-grade route protection for admin panel

import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import {
  Box,
  CircularProgress,
  Typography,
  Al<PERSON>,
  But<PERSON>,
  Card,
  CardContent,
} from '@mui/material';
import { Security, Warning, Block } from '@mui/icons-material';

import adminSecurityService from '../../services/adminSecurity';
import { 
  ipWhitelistManager, 
  deviceAuthManager, 
  emergencyModeManager,
  SECURITY_LEVELS 
} from '../../config/bankingSecurity';
import { SecurityLogger } from '../../config/security';

const AdminRoute = ({ children, requiredPermissions = [], securityLevel = SECURITY_LEVELS.ADMIN }) => {
  const location = useLocation();
  const [authState, setAuthState] = useState({
    loading: true,
    authenticated: false,
    authorized: false,
    errors: [],
    warnings: [],
    sessionInfo: null,
  });

  useEffect(() => {
    validateAdminAccess();
    
    // Set up session monitoring
    const interval = setInterval(validateAdminAccess, 60000); // Check every minute
    return () => clearInterval(interval);
  }, [location.pathname]);

  const validateAdminAccess = async () => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, errors: [], warnings: [] }));

      // 1. Check if user has admin session
      const sessionId = localStorage.getItem('admin_session_id');
      const username = localStorage.getItem('admin_username');

      if (!sessionId || !username) {
        setAuthState({
          loading: false,
          authenticated: false,
          authorized: false,
          errors: ['No admin session found'],
          warnings: [],
          sessionInfo: null,
        });
        return;
      }

      // 2. Validate session with security service
      const sessionInfo = adminSecurityService.getAdminSessionInfo(sessionId);
      if (!sessionInfo || !sessionInfo.active) {
        // Clear invalid session
        localStorage.removeItem('admin_session_id');
        localStorage.removeItem('admin_username');
        
        setAuthState({
          loading: false,
          authenticated: false,
          authorized: false,
          errors: ['Admin session expired or invalid'],
          warnings: [],
          sessionInfo: null,
        });
        return;
      }

      // 3. Update session activity
      adminSecurityService.updateSessionActivity(sessionId);

      // 4. Perform security checks
      const securityChecks = await performSecurityChecks(sessionInfo);
      
      if (securityChecks.blocked) {
        setAuthState({
          loading: false,
          authenticated: true,
          authorized: false,
          errors: securityChecks.errors,
          warnings: securityChecks.warnings,
          sessionInfo,
        });
        return;
      }

      // 5. Check permissions for current route
      const permissionCheck = checkRoutePermissions(sessionInfo, requiredPermissions);
      
      if (!permissionCheck.authorized) {
        setAuthState({
          loading: false,
          authenticated: true,
          authorized: false,
          errors: permissionCheck.errors,
          warnings: [],
          sessionInfo,
        });
        return;
      }

      // 6. All checks passed
      setAuthState({
        loading: false,
        authenticated: true,
        authorized: true,
        errors: [],
        warnings: securityChecks.warnings,
        sessionInfo,
      });

      // Log successful access
      SecurityLogger.logSecurityEvent('ADMIN_ROUTE_ACCESS', {
        username: sessionInfo.username,
        route: location.pathname,
        sessionId,
        securityLevel,
        requiredPermissions,
      });

    } catch (error) {
      console.error('Admin route validation error:', error);
      setAuthState({
        loading: false,
        authenticated: false,
        authorized: false,
        errors: ['Security validation failed'],
        warnings: [],
        sessionInfo: null,
      });
    }
  };

  const performSecurityChecks = async (sessionInfo) => {
    const checks = {
      blocked: false,
      errors: [],
      warnings: [],
    };

    try {
      // 1. Check emergency mode
      const emergencyStatus = emergencyModeManager.getEmergencyStatus();
      if (emergencyStatus.active) {
        // Allow only emergency operations
        const emergencyRoutes = ['/admin/emergency', '/admin/security'];
        if (!emergencyRoutes.includes(location.pathname)) {
          checks.blocked = true;
          checks.errors.push('System in emergency mode - limited access only');
          return checks;
        }
        checks.warnings.push('System is in emergency mode');
      }

      // 2. Check IP whitelist
      const ipCheck = await ipWhitelistManager.checkIPAccess(securityLevel);
      if (!ipCheck.allowed) {
        checks.blocked = true;
        checks.errors.push('IP address not authorized for admin access');
        
        // Log security violation
        SecurityLogger.logSecurityEvent('ADMIN_IP_VIOLATION', {
          username: sessionInfo.username,
          ip: ipCheck.ip,
          route: location.pathname,
        });
        return checks;
      }

      // 3. Check device authorization
      const deviceCheck = await deviceAuthManager.authorizeDevice(securityLevel);
      if (!deviceCheck.authorized) {
        if (deviceCheck.requiresApproval) {
          checks.warnings.push('Device requires manual approval for this security level');
        } else {
          checks.blocked = true;
          checks.errors.push('Device not authorized for admin access');
          return checks;
        }
      }

      // 4. Check session security level
      if (sessionInfo.securityLevel < securityLevel) {
        checks.blocked = true;
        checks.errors.push('Insufficient security level for this operation');
        return checks;
      }

      // 5. Check for suspicious activity
      const suspiciousActivity = await checkSuspiciousActivity(sessionInfo);
      if (suspiciousActivity.detected) {
        checks.warnings.push('Suspicious activity detected - enhanced monitoring active');
      }

      // 6. Check business hours (for certain operations)
      if (securityLevel >= SECURITY_LEVELS.CRITICAL) {
        const isBusinessHours = checkBusinessHours();
        if (!isBusinessHours) {
          checks.warnings.push('Critical operations outside business hours require additional approval');
        }
      }

    } catch (error) {
      checks.blocked = true;
      checks.errors.push('Security check failed: ' + error.message);
    }

    return checks;
  };

  const checkRoutePermissions = (sessionInfo, requiredPermissions) => {
    const result = {
      authorized: true,
      errors: [],
    };

    // If no specific permissions required, check general admin access
    if (requiredPermissions.length === 0) {
      return result;
    }

    // Check each required permission
    for (const permission of requiredPermissions) {
      if (!sessionInfo.permissions.includes(permission) && !sessionInfo.permissions.includes('all_permissions')) {
        result.authorized = false;
        result.errors.push(`Missing required permission: ${permission}`);
      }
    }

    return result;
  };

  const checkSuspiciousActivity = async (sessionInfo) => {
    // Check for patterns that might indicate compromised admin account
    const recentAudit = adminSecurityService.getAdminAuditTrail(sessionInfo.username, 50);
    
    // Check for unusual activity patterns
    const recentActions = recentAudit.filter(entry => 
      Date.now() - entry.timestamp < 3600000 // Last hour
    );

    const suspiciousPatterns = {
      highVolumeActions: recentActions.length > 100,
      rapidPrivilegedOps: recentActions.filter(a => a.type === 'privileged_operation').length > 10,
      unusualTimeAccess: new Date().getHours() < 6 || new Date().getHours() > 22,
    };

    const detected = Object.values(suspiciousPatterns).some(pattern => pattern);

    if (detected) {
      SecurityLogger.logSecurityEvent('ADMIN_SUSPICIOUS_ACTIVITY', {
        username: sessionInfo.username,
        patterns: suspiciousPatterns,
        route: location.pathname,
      });
    }

    return { detected, patterns: suspiciousPatterns };
  };

  const checkBusinessHours = () => {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay();
    
    // Monday to Friday, 8 AM to 6 PM
    return day >= 1 && day <= 5 && hour >= 8 && hour < 18;
  };

  const handleLogout = () => {
    const sessionId = localStorage.getItem('admin_session_id');
    if (sessionId) {
      adminSecurityService.terminateAdminSession(sessionId, 'User logout');
    }
    
    localStorage.removeItem('admin_session_id');
    localStorage.removeItem('admin_username');
    
    // Redirect to admin login
    window.location.href = '/admin/login';
  };

  const handleRetry = () => {
    validateAdminAccess();
  };

  // Loading state
  if (authState.loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          bgcolor: 'background.default',
        }}
      >
        <CircularProgress size={60} sx={{ mb: 2 }} />
        <Typography variant="h6" color="text.secondary">
          Validating Admin Access...
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Performing security checks
        </Typography>
      </Box>
    );
  }

  // Not authenticated - redirect to admin login
  if (!authState.authenticated) {
    return <Navigate to="/admin/login" state={{ from: location }} replace />;
  }

  // Authenticated but not authorized - show access denied
  if (!authState.authorized) {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          bgcolor: 'background.default',
          p: 3,
        }}
      >
        <Card sx={{ maxWidth: 500, width: '100%' }}>
          <CardContent sx={{ textAlign: 'center', p: 4 }}>
            <Block sx={{ fontSize: 64, color: 'error.main', mb: 2 }} />
            
            <Typography variant="h5" sx={{ mb: 2, fontWeight: 'bold' }}>
              Access Denied
            </Typography>
            
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              You don't have sufficient permissions to access this resource.
            </Typography>

            {/* Error Messages */}
            {authState.errors.length > 0 && (
              <Alert severity="error" sx={{ mb: 2, textAlign: 'left' }}>
                {authState.errors.map((error, index) => (
                  <div key={index}>• {error}</div>
                ))}
              </Alert>
            )}

            {/* Session Info */}
            {authState.sessionInfo && (
              <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="caption" color="text.secondary">
                  Logged in as: {authState.sessionInfo.username}<br />
                  Security Level: {authState.sessionInfo.securityLevel}<br />
                  Session: {authState.sessionInfo.sessionId.substring(0, 12)}...
                </Typography>
              </Box>
            )}

            {/* Action Buttons */}
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
              <Button variant="outlined" onClick={handleRetry}>
                Retry
              </Button>
              <Button variant="contained" onClick={() => window.history.back()}>
                Go Back
              </Button>
              <Button variant="text" onClick={handleLogout}>
                Logout
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Box>
    );
  }

  // Warning messages (but still authorized)
  const WarningBanner = () => {
    if (authState.warnings.length === 0) return null;

    return (
      <Alert 
        severity="warning" 
        sx={{ 
          position: 'fixed', 
          top: 0, 
          left: 0, 
          right: 0, 
          zIndex: 9999,
          borderRadius: 0,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Warning sx={{ mr: 1 }} />
          <Box>
            {authState.warnings.map((warning, index) => (
              <div key={index}>{warning}</div>
            ))}
          </Box>
        </Box>
      </Alert>
    );
  };

  // Authorized - render children with warning banner if needed
  return (
    <>
      <WarningBanner />
      <Box sx={{ pt: authState.warnings.length > 0 ? 8 : 0 }}>
        {children}
      </Box>
    </>
  );
};

// Higher-order component for specific admin routes
export const withAdminRoute = (Component, options = {}) => {
  return (props) => (
    <AdminRoute 
      requiredPermissions={options.requiredPermissions || []}
      securityLevel={options.securityLevel || SECURITY_LEVELS.ADMIN}
    >
      <Component {...props} />
    </AdminRoute>
  );
};

// Specific route protection components
export const AdminDashboardRoute = ({ children }) => (
  <AdminRoute requiredPermissions={['admin.dashboard.view']}>
    {children}
  </AdminRoute>
);

export const AdminUsersRoute = ({ children }) => (
  <AdminRoute requiredPermissions={['admin.users.view']}>
    {children}
  </AdminRoute>
);

export const AdminSecurityRoute = ({ children }) => (
  <AdminRoute 
    requiredPermissions={['admin.security.view']}
    securityLevel={SECURITY_LEVELS.CRITICAL}
  >
    {children}
  </AdminRoute>
);

export const AdminFinancialRoute = ({ children }) => (
  <AdminRoute 
    requiredPermissions={['admin.financial.view']}
    securityLevel={SECURITY_LEVELS.CRITICAL}
  >
    {children}
  </AdminRoute>
);

export const AdminSystemRoute = ({ children }) => (
  <AdminRoute 
    requiredPermissions={['admin.system.config']}
    securityLevel={SECURITY_LEVELS.CRITICAL}
  >
    {children}
  </AdminRoute>
);

export default AdminRoute;
