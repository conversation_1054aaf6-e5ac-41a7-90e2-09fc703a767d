// ProChat Simple Backend Server
const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 8080;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Mock data
const users = [
  { id: 1, username: 'john_doe', email: '<EMAIL>', balance: 250000 },
  { id: 2, username: 'jane_smith', email: '<EMAIL>', balance: 180000 },
  { id: 3, username: 'mike_wilson', email: '<EMAIL>', balance: 320000 }
];

const posts = [
  { id: 1, userId: 1, content: 'Habari za asubuhi! Leo ni siku nzuri ya kufanya biashara.', likes: 15, comments: 3 },
  { id: 2, userId: 2, content: 'Nimepata kazi mpya kupitia ProChat Jobs! Asante sana.', likes: 28, comments: 7 },
  { id: 3, userId: 3, content: 'Event ya muziki leo jioni Mlimani City. Tiketi zinapatikana ProChat.', likes: 42, comments: 12 }
];

const transactions = [
  { id: 1, userId: 1, type: 'SEND_MONEY', amount: 50000, status: 'COMPLETED', description: 'Malipo ya bidhaa' },
  { id: 2, userId: 2, type: 'RECEIVE_MONEY', amount: 75000, status: 'COMPLETED', description: 'Mshahara' },
  { id: 3, userId: 3, type: 'BILL_PAYMENT', amount: 25000, status: 'COMPLETED', description: 'LUKU - Umeme' }
];

// API Routes
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'ProChat Backend is running!',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Auth endpoints
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;
  const user = users.find(u => u.username === username);
  
  if (user && password === 'password123') {
    res.json({
      success: true,
      token: 'mock-jwt-token-' + user.id,
      user: user
    });
  } else {
    res.status(401).json({ success: false, message: 'Invalid credentials' });
  }
});

// User endpoints
app.get('/api/users/profile', (req, res) => {
  const userId = 1; // Mock authenticated user
  const user = users.find(u => u.id === userId);
  res.json(user);
});

app.get('/api/users', (req, res) => {
  res.json(users);
});

// Posts endpoints
app.get('/api/posts', (req, res) => {
  const postsWithUsers = posts.map(post => ({
    ...post,
    user: users.find(u => u.id === post.userId)
  }));
  res.json(postsWithUsers);
});

app.post('/api/posts', (req, res) => {
  const { content } = req.body;
  const newPost = {
    id: posts.length + 1,
    userId: 1, // Mock authenticated user
    content: content,
    likes: 0,
    comments: 0,
    createdAt: new Date().toISOString()
  };
  posts.push(newPost);
  res.json(newPost);
});

// Wallet endpoints
app.get('/api/wallet/balance', (req, res) => {
  const userId = 1; // Mock authenticated user
  const user = users.find(u => u.id === userId);
  res.json({ 
    balance: user.balance,
    currency: 'TZS',
    formatted: `TZS ${user.balance.toLocaleString()}`
  });
});

app.get('/api/wallet/transactions', (req, res) => {
  const userId = 1; // Mock authenticated user
  const userTransactions = transactions.filter(t => t.userId === userId);
  res.json(userTransactions);
});

app.post('/api/wallet/send', (req, res) => {
  const { amount, recipient, description } = req.body;
  const newTransaction = {
    id: transactions.length + 1,
    userId: 1,
    type: 'SEND_MONEY',
    amount: amount,
    status: 'COMPLETED',
    description: description || 'Money transfer',
    recipient: recipient,
    createdAt: new Date().toISOString()
  };
  transactions.push(newTransaction);
  
  // Update user balance
  const user = users.find(u => u.id === 1);
  user.balance -= amount;
  
  res.json({ 
    success: true, 
    transaction: newTransaction,
    newBalance: user.balance
  });
});

// Events endpoints
app.get('/api/events', (req, res) => {
  const events = [
    { id: 1, title: 'Muziki wa Bongo Flava', date: '2024-12-15', location: 'Mlimani City', price: 15000 },
    { id: 2, title: 'Tech Conference Dar', date: '2024-12-20', location: 'UDSM', price: 25000 },
    { id: 3, title: 'Food Festival', date: '2024-12-25', location: 'Coco Beach', price: 10000 }
  ];
  res.json(events);
});

// Jobs endpoints
app.get('/api/jobs', (req, res) => {
  const jobs = [
    { id: 1, title: 'Software Developer', company: 'TechCorp TZ', salary: '800,000 - 1,200,000', location: 'Dar es Salaam' },
    { id: 2, title: 'Marketing Manager', company: 'BrandCo', salary: '600,000 - 900,000', location: 'Arusha' },
    { id: 3, title: 'Data Analyst', company: 'DataTech', salary: '700,000 - 1,000,000', location: 'Mwanza' }
  ];
  res.json(jobs);
});

// Chat endpoints
app.get('/api/chats', (req, res) => {
  const chats = [
    { id: 1, name: 'Family Group', lastMessage: 'Habari za leo?', unreadCount: 2, type: 'group' },
    { id: 2, name: 'John Doe', lastMessage: 'Tutaonana kesho', unreadCount: 0, type: 'private' },
    { id: 3, name: 'Work Team', lastMessage: 'Meeting at 2 PM', unreadCount: 5, type: 'group' }
  ];
  res.json(chats);
});

// Notifications endpoints
app.get('/api/notifications', (req, res) => {
  const notifications = [
    { id: 1, title: 'Pesa Imefika', message: 'Umepokea TZS 50,000 kutoka John', type: 'payment', read: false },
    { id: 2, title: 'Post Mpya', message: 'Jane amepost kitu kipya', type: 'social', read: false },
    { id: 3, title: 'Event Reminder', message: 'Event ya muziki ni kesho', type: 'event', read: true }
  ];
  res.json(notifications);
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 ProChat Backend running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`📱 API endpoints available at /api/*`);
  console.log(`✅ Ready to serve ProChat applications!`);
});

module.exports = app;
