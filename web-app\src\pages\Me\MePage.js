import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Avatar,
  Typography,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Card,
  CardContent,
  Grid,
  IconButton,
  Badge,
  Chip,
} from '@mui/material';
import {
  Edit,
  AccountBalance,
  Store,
  Notifications,
  Settings,
  Help,
  Share,
  ExitToApp,
  ChevronRight,
  Verified,
  TrendingUp,
  People,
  Event,
  Work,
  Gift,
  Favorite,
  Star,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useDeviceDetection } from '../../hooks/useDeviceDetection';

const MePage = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { isMobile } = useDeviceDetection();

  // Mock user stats
  const userStats = {
    posts: 45,
    followers: 1250,
    following: 890,
    events: 8,
    walletBalance: 125000,
    proZoneRating: 4.8,
  };

  const handleLogout = async () => {
    const result = await logout();
    if (result.success) {
      navigate('/login');
    }
  };

  const menuItems = [
    {
      section: 'Akaunti',
      items: [
        {
          icon: <Edit />,
          title: 'Hariri Wasifu',
          subtitle: 'Badilisha taarifa zako za kibinafsi',
          path: '/edit-profile',
          badge: null,
        },
        {
          icon: <Notifications />,
          title: 'Arifa',
          subtitle: 'Dhibiti arifa zako',
          path: '/notifications',
          badge: 3,
        },
        {
          icon: <Settings />,
          title: 'Mipangilio',
          subtitle: 'Mipangilio ya programu',
          path: '/settings',
          badge: null,
        },
      ],
    },
    {
      section: 'ProPay & ProZone',
      items: [
        {
          icon: <AccountBalance />,
          title: 'ProPay Wallet',
          subtitle: `Salio: TSh ${userStats.walletBalance.toLocaleString()}`,
          path: '/propay',
          badge: null,
        },
        {
          icon: <Store />,
          title: 'ProZone',
          subtitle: `Ukadiriaji: ${userStats.proZoneRating} ⭐`,
          path: '/prozone',
          badge: 'Beta',
        },
      ],
    },
    {
      section: 'Huduma za Kijamii',
      items: [
        {
          icon: <Gift />,
          title: 'Zawadi',
          subtitle: 'Tuma na pokea zawadi',
          path: '/gift',
          badge: null,
        },
        {
          icon: <Favorite />,
          title: 'Michango',
          subtitle: 'Changia jamii',
          path: '/donation',
          badge: null,
        },
        {
          icon: <Work />,
          title: 'Kazi za Kufanya',
          subtitle: 'Pata mapato ya ziada',
          path: '/tasks',
          badge: 'Mpya',
        },
      ],
    },
    {
      section: 'Msaada na Maelezo',
      items: [
        {
          icon: <Share />,
          title: 'Alika Marafiki',
          subtitle: 'Shiriki ProChat na marafiki',
          path: '/invite-friends',
          badge: null,
        },
        {
          icon: <Help />,
          title: 'Msaada na Maswali',
          subtitle: 'Pata msaada wa haraka',
          path: '/help-support',
          badge: null,
        },
      ],
    },
  ];

  const StatCard = ({ icon, title, value, color }) => (
    <Card sx={{ textAlign: 'center', height: '100%' }}>
      <CardContent sx={{ py: 2 }}>
        <Box sx={{ color: color, mb: 1 }}>
          {icon}
        </Box>
        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 0.5 }}>
          {typeof value === 'number' ? value.toLocaleString() : value}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {title}
        </Typography>
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ pb: isMobile ? 2 : 0 }}>
      {/* Profile Header */}
      <Card sx={{ mb: 2, mx: isMobile ? 1 : 2, mt: isMobile ? 1 : 2 }}>
        <CardContent sx={{ textAlign: 'center', py: 3 }}>
          <Avatar
            src={user?.profilePicture}
            sx={{
              width: 80,
              height: 80,
              mx: 'auto',
              mb: 2,
              border: '3px solid',
              borderColor: 'primary.main',
            }}
          >
            {user?.name?.charAt(0)}
          </Avatar>
          
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
            <Typography variant="h5" sx={{ fontWeight: 'bold', mr: 1 }}>
              {user?.name || 'Mtumiaji'}
            </Typography>
            <Verified sx={{ color: 'primary.main', fontSize: 20 }} />
          </Box>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            {user?.email || '<EMAIL>'}
          </Typography>
          
          <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
            Umejiunga {new Date().getFullYear() - 1}
          </Typography>

          <Button
            variant="outlined"
            startIcon={<Edit />}
            onClick={() => navigate('/edit-profile')}
            sx={{ borderRadius: 3 }}
          >
            Hariri Wasifu
          </Button>
        </CardContent>
      </Card>

      {/* Stats Grid */}
      <Box sx={{ px: isMobile ? 1 : 2, mb: 2 }}>
        <Grid container spacing={1}>
          <Grid item xs={4}>
            <StatCard
              icon={<TrendingUp />}
              title="Machapisho"
              value={userStats.posts}
              color="primary.main"
            />
          </Grid>
          <Grid item xs={4}>
            <StatCard
              icon={<People />}
              title="Wafuasi"
              value={userStats.followers}
              color="secondary.main"
            />
          </Grid>
          <Grid item xs={4}>
            <StatCard
              icon={<Event />}
              title="Matukio"
              value={userStats.events}
              color="warning.main"
            />
          </Grid>
        </Grid>
      </Box>

      {/* Menu Sections */}
      <Box sx={{ px: isMobile ? 1 : 2 }}>
        {menuItems.map((section, sectionIndex) => (
          <Card key={sectionIndex} sx={{ mb: 2 }}>
            <CardContent sx={{ py: 1 }}>
              <Typography
                variant="overline"
                sx={{
                  display: 'block',
                  color: 'text.secondary',
                  fontWeight: 'bold',
                  mb: 1,
                  px: 1,
                }}
              >
                {section.section}
              </Typography>
              
              <List sx={{ py: 0 }}>
                {section.items.map((item, index) => (
                  <React.Fragment key={index}>
                    <ListItem
                      button
                      onClick={() => navigate(item.path)}
                      sx={{
                        borderRadius: 2,
                        mb: 0.5,
                        '&:hover': {
                          bgcolor: 'action.hover',
                        },
                      }}
                    >
                      <ListItemIcon sx={{ color: 'primary.main' }}>
                        {item.icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={item.title}
                        secondary={item.subtitle}
                        primaryTypographyProps={{
                          fontWeight: 'medium',
                        }}
                      />
                      <ListItemSecondaryAction>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {item.badge && (
                            <Chip
                              label={item.badge}
                              size="small"
                              color={typeof item.badge === 'number' ? 'error' : 'primary'}
                              sx={{ mr: 1, height: 20, fontSize: '0.7rem' }}
                            />
                          )}
                          <IconButton edge="end" size="small">
                            <ChevronRight />
                          </IconButton>
                        </Box>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < section.items.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        ))}

        {/* Logout Button */}
        <Card sx={{ mb: 2 }}>
          <CardContent sx={{ py: 1 }}>
            <List sx={{ py: 0 }}>
              <ListItem
                button
                onClick={handleLogout}
                sx={{
                  borderRadius: 2,
                  '&:hover': {
                    bgcolor: 'error.light',
                    color: 'error.contrastText',
                  },
                }}
              >
                <ListItemIcon sx={{ color: 'error.main' }}>
                  <ExitToApp />
                </ListItemIcon>
                <ListItemText
                  primary="Toka"
                  secondary="Toka kwenye akaunti yako"
                  primaryTypographyProps={{
                    fontWeight: 'medium',
                    color: 'error.main',
                  }}
                />
                <ListItemSecondaryAction>
                  <IconButton edge="end" size="small" sx={{ color: 'error.main' }}>
                    <ChevronRight />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            </List>
          </CardContent>
        </Card>
      </Box>

      {/* App Version */}
      <Box sx={{ textAlign: 'center', py: 2 }}>
        <Typography variant="caption" color="text.secondary">
          ProChat Web v1.0.0
        </Typography>
      </Box>
    </Box>
  );
};

export default MePage;
