// ProChat AI-Powered Fraud Detection System
// Real-time monitoring and threat detection

import { SecurityLogger } from '../config/security';
import { emergencyModeManager, rbacManager } from '../config/bankingSecurity';
import offlineStorage from './offlineStorage';

class FraudDetectionService {
  constructor() {
    this.isEnabled = true;
    this.riskThresholds = {
      LOW: 0.3,
      MEDIUM: 0.6,
      HIGH: 0.8,
      CRITICAL: 0.95,
    };
    
    this.userBehaviorProfiles = new Map();
    this.suspiciousActivities = new Map();
    this.blockedUsers = new Set();
    this.monitoringRules = new Map();
    
    this.initializeRules();
    this.startMonitoring();
  }

  initializeRules() {
    // Define fraud detection rules
    const rules = {
      'rapid_likes': {
        name: 'Rapid Liking Pattern',
        description: 'User liking posts too quickly',
        threshold: 10, // likes per minute
        timeWindow: 60000, // 1 minute
        riskScore: 0.7,
        action: 'throttle',
      },
      'multiple_accounts': {
        name: 'Multiple Account Access',
        description: 'Same device accessing multiple accounts',
        threshold: 3, // accounts per device
        timeWindow: 3600000, // 1 hour
        riskScore: 0.8,
        action: 'flag',
      },
      'unusual_transaction_pattern': {
        name: 'Unusual Transaction Pattern',
        description: 'Transaction pattern differs from user history',
        threshold: 0.8, // deviation score
        riskScore: 0.9,
        action: 'block',
      },
      'geographic_anomaly': {
        name: 'Geographic Anomaly',
        description: 'Login from unusual location',
        threshold: 1000, // km from usual location
        riskScore: 0.6,
        action: 'verify',
      },
      'velocity_check': {
        name: 'Transaction Velocity',
        description: 'Too many transactions in short time',
        threshold: 5, // transactions per hour
        timeWindow: 3600000, // 1 hour
        riskScore: 0.8,
        action: 'limit',
      },
      'reward_farming': {
        name: 'Reward Farming Detection',
        description: 'Artificial engagement for rewards',
        threshold: 50, // actions per hour
        timeWindow: 3600000, // 1 hour
        riskScore: 0.9,
        action: 'suspend_rewards',
      },
    };

    rules.forEach((rule, ruleId) => {
      this.monitoringRules.set(ruleId, rule);
    });
  }

  startMonitoring() {
    // Start real-time monitoring
    setInterval(() => {
      this.analyzeUserBehavior();
      this.detectAnomalies();
      this.cleanupOldData();
    }, 30000); // Every 30 seconds

    console.log('Fraud Detection Service: Monitoring started');
  }

  // Track user actions for behavior analysis
  trackUserAction(userId, action, metadata = {}) {
    if (!this.isEnabled) return;

    const timestamp = Date.now();
    const actionData = {
      action,
      timestamp,
      metadata: {
        ...metadata,
        ip: metadata.ip || 'unknown',
        userAgent: navigator.userAgent,
        deviceId: metadata.deviceId,
      },
    };

    // Get or create user profile
    if (!this.userBehaviorProfiles.has(userId)) {
      this.userBehaviorProfiles.set(userId, {
        userId,
        actions: [],
        riskScore: 0,
        flags: [],
        createdAt: timestamp,
        lastActivity: timestamp,
      });
    }

    const profile = this.userBehaviorProfiles.get(userId);
    profile.actions.push(actionData);
    profile.lastActivity = timestamp;

    // Keep only recent actions (last 24 hours)
    const dayAgo = timestamp - 24 * 60 * 60 * 1000;
    profile.actions = profile.actions.filter(a => a.timestamp > dayAgo);

    // Analyze this action
    this.analyzeAction(userId, actionData);
  }

  analyzeAction(userId, actionData) {
    const { action, timestamp, metadata } = actionData;
    let riskScore = 0;
    const flags = [];

    // Check against each monitoring rule
    this.monitoringRules.forEach((rule, ruleId) => {
      const violation = this.checkRule(userId, rule, actionData);
      if (violation) {
        riskScore = Math.max(riskScore, rule.riskScore);
        flags.push({
          rule: ruleId,
          violation,
          timestamp,
          severity: this.getRiskLevel(rule.riskScore),
        });
      }
    });

    // Update user risk score
    const profile = this.userBehaviorProfiles.get(userId);
    profile.riskScore = Math.max(profile.riskScore * 0.9, riskScore); // Decay over time
    
    if (flags.length > 0) {
      profile.flags.push(...flags);
      this.handleSuspiciousActivity(userId, flags);
    }
  }

  checkRule(userId, rule, actionData) {
    const profile = this.userBehaviorProfiles.get(userId);
    const { action, timestamp, metadata } = actionData;

    switch (rule.name) {
      case 'Rapid Liking Pattern':
        return this.checkRapidLiking(profile, rule, actionData);
      
      case 'Multiple Account Access':
        return this.checkMultipleAccounts(rule, actionData);
      
      case 'Unusual Transaction Pattern':
        return this.checkTransactionPattern(profile, rule, actionData);
      
      case 'Geographic Anomaly':
        return this.checkGeographicAnomaly(profile, rule, actionData);
      
      case 'Transaction Velocity':
        return this.checkTransactionVelocity(profile, rule, actionData);
      
      case 'Reward Farming Detection':
        return this.checkRewardFarming(profile, rule, actionData);
      
      default:
        return null;
    }
  }

  checkRapidLiking(profile, rule, actionData) {
    if (actionData.action !== 'like') return null;

    const recentLikes = profile.actions.filter(a => 
      a.action === 'like' && 
      actionData.timestamp - a.timestamp < rule.timeWindow
    );

    if (recentLikes.length > rule.threshold) {
      return {
        count: recentLikes.length,
        threshold: rule.threshold,
        timeWindow: rule.timeWindow,
      };
    }
    return null;
  }

  checkMultipleAccounts(rule, actionData) {
    const deviceId = actionData.metadata.deviceId;
    if (!deviceId) return null;

    // Count unique users from this device in the time window
    const recentUsers = new Set();
    this.userBehaviorProfiles.forEach((profile, userId) => {
      const recentActions = profile.actions.filter(a => 
        a.metadata.deviceId === deviceId &&
        actionData.timestamp - a.timestamp < rule.timeWindow
      );
      if (recentActions.length > 0) {
        recentUsers.add(userId);
      }
    });

    if (recentUsers.size > rule.threshold) {
      return {
        userCount: recentUsers.size,
        threshold: rule.threshold,
        deviceId,
      };
    }
    return null;
  }

  checkTransactionPattern(profile, rule, actionData) {
    if (!actionData.action.startsWith('wallet_')) return null;

    const recentTransactions = profile.actions.filter(a => 
      a.action.startsWith('wallet_') &&
      actionData.timestamp - a.timestamp < 7 * 24 * 60 * 60 * 1000 // 7 days
    );

    if (recentTransactions.length < 5) return null; // Need history

    // Simple pattern analysis (in production, use ML)
    const amounts = recentTransactions
      .map(t => t.metadata.amount)
      .filter(a => a !== undefined);
    
    if (amounts.length === 0) return null;

    const avgAmount = amounts.reduce((a, b) => a + b, 0) / amounts.length;
    const currentAmount = actionData.metadata.amount || 0;
    
    const deviation = Math.abs(currentAmount - avgAmount) / avgAmount;
    
    if (deviation > rule.threshold) {
      return {
        deviation,
        threshold: rule.threshold,
        currentAmount,
        avgAmount,
      };
    }
    return null;
  }

  checkGeographicAnomaly(profile, rule, actionData) {
    const currentLocation = actionData.metadata.location;
    if (!currentLocation) return null;

    // Get user's usual locations
    const recentLocations = profile.actions
      .filter(a => a.metadata.location)
      .map(a => a.metadata.location)
      .slice(-10); // Last 10 locations

    if (recentLocations.length === 0) return null;

    // Simple distance check (in production, use proper geolocation)
    const isUnusual = !recentLocations.some(loc => 
      this.calculateDistance(currentLocation, loc) < rule.threshold
    );

    if (isUnusual) {
      return {
        currentLocation,
        usualLocations: recentLocations,
        threshold: rule.threshold,
      };
    }
    return null;
  }

  checkTransactionVelocity(profile, rule, actionData) {
    if (!actionData.action.startsWith('wallet_')) return null;

    const recentTransactions = profile.actions.filter(a => 
      a.action.startsWith('wallet_') &&
      actionData.timestamp - a.timestamp < rule.timeWindow
    );

    if (recentTransactions.length > rule.threshold) {
      return {
        count: recentTransactions.length,
        threshold: rule.threshold,
        timeWindow: rule.timeWindow,
      };
    }
    return null;
  }

  checkRewardFarming(profile, rule, actionData) {
    const rewardActions = ['like', 'comment', 'share', 'download'];
    if (!rewardActions.includes(actionData.action)) return null;

    const recentRewardActions = profile.actions.filter(a => 
      rewardActions.includes(a.action) &&
      actionData.timestamp - a.timestamp < rule.timeWindow
    );

    if (recentRewardActions.length > rule.threshold) {
      return {
        count: recentRewardActions.length,
        threshold: rule.threshold,
        timeWindow: rule.timeWindow,
        actions: recentRewardActions.map(a => a.action),
      };
    }
    return null;
  }

  handleSuspiciousActivity(userId, flags) {
    const profile = this.userBehaviorProfiles.get(userId);
    const riskLevel = this.getRiskLevel(profile.riskScore);

    // Log security event
    SecurityLogger.logSecurityEvent('SUSPICIOUS_ACTIVITY_DETECTED', {
      userId,
      riskScore: profile.riskScore,
      riskLevel,
      flags,
      timestamp: new Date().toISOString(),
    });

    // Take action based on risk level
    flags.forEach(flag => {
      const rule = this.monitoringRules.get(flag.rule);
      this.executeAction(userId, rule.action, flag);
    });

    // Store suspicious activity
    this.suspiciousActivities.set(`${userId}_${Date.now()}`, {
      userId,
      flags,
      riskScore: profile.riskScore,
      timestamp: Date.now(),
    });

    // Critical risk - activate emergency measures
    if (riskLevel === 'CRITICAL') {
      this.handleCriticalRisk(userId, flags);
    }
  }

  executeAction(userId, action, flag) {
    switch (action) {
      case 'throttle':
        this.throttleUser(userId, 60000); // 1 minute
        break;
      
      case 'flag':
        this.flagUser(userId, flag);
        break;
      
      case 'block':
        this.blockUser(userId, 'Suspicious activity detected');
        break;
      
      case 'verify':
        this.requireVerification(userId);
        break;
      
      case 'limit':
        this.limitUserActions(userId, 3600000); // 1 hour
        break;
      
      case 'suspend_rewards':
        this.suspendRewards(userId, 24 * 3600000); // 24 hours
        break;
    }
  }

  throttleUser(userId, duration) {
    // Implement user throttling
    console.log(`Throttling user ${userId} for ${duration}ms`);
  }

  flagUser(userId, flag) {
    // Flag user for manual review
    console.log(`Flagging user ${userId}:`, flag);
  }

  blockUser(userId, reason) {
    this.blockedUsers.add(userId);
    SecurityLogger.logSecurityEvent('USER_BLOCKED', {
      userId,
      reason,
      timestamp: new Date().toISOString(),
    });
    console.log(`Blocking user ${userId}: ${reason}`);
  }

  requireVerification(userId) {
    // Require additional verification
    console.log(`Requiring verification for user ${userId}`);
  }

  limitUserActions(userId, duration) {
    // Limit user actions
    console.log(`Limiting actions for user ${userId} for ${duration}ms`);
  }

  suspendRewards(userId, duration) {
    // Suspend reward earning
    console.log(`Suspending rewards for user ${userId} for ${duration}ms`);
  }

  handleCriticalRisk(userId, flags) {
    // Activate emergency mode for critical risks
    emergencyModeManager.activateEmergencyMode(
      `Critical fraud risk detected for user ${userId}`,
      ['view_only', 'logout']
    );

    // Notify administrators
    this.notifyAdministrators('CRITICAL_FRAUD_RISK', {
      userId,
      flags,
      timestamp: new Date().toISOString(),
    });
  }

  notifyAdministrators(type, data) {
    // Send notification to administrators
    console.log('Admin notification:', type, data);
    
    // In production, send email/SMS to admins
    // this.sendAdminAlert(type, data);
  }

  calculateDistance(loc1, loc2) {
    // Simple distance calculation (in production, use proper geolocation library)
    if (!loc1 || !loc2) return 0;
    
    const lat1 = loc1.latitude || 0;
    const lon1 = loc1.longitude || 0;
    const lat2 = loc2.latitude || 0;
    const lon2 = loc2.longitude || 0;
    
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLon/2) * Math.sin(dLon/2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  getRiskLevel(riskScore) {
    if (riskScore >= this.riskThresholds.CRITICAL) return 'CRITICAL';
    if (riskScore >= this.riskThresholds.HIGH) return 'HIGH';
    if (riskScore >= this.riskThresholds.MEDIUM) return 'MEDIUM';
    if (riskScore >= this.riskThresholds.LOW) return 'LOW';
    return 'MINIMAL';
  }

  analyzeUserBehavior() {
    // Periodic analysis of all user behaviors
    this.userBehaviorProfiles.forEach((profile, userId) => {
      if (this.blockedUsers.has(userId)) return;

      // Calculate overall risk score
      const recentFlags = profile.flags.filter(f => 
        Date.now() - f.timestamp < 24 * 60 * 60 * 1000 // Last 24 hours
      );

      if (recentFlags.length > 0) {
        const avgRisk = recentFlags.reduce((sum, f) => sum + f.severity, 0) / recentFlags.length;
        profile.riskScore = Math.max(profile.riskScore * 0.95, avgRisk); // Gradual decay
      } else {
        profile.riskScore *= 0.98; // Decay risk score over time
      }
    });
  }

  detectAnomalies() {
    // ML-based anomaly detection (simplified)
    this.userBehaviorProfiles.forEach((profile, userId) => {
      if (this.blockedUsers.has(userId)) return;

      const recentActions = profile.actions.filter(a => 
        Date.now() - a.timestamp < 60 * 60 * 1000 // Last hour
      );

      // Detect unusual patterns
      if (recentActions.length > 100) { // Too many actions
        this.handleSuspiciousActivity(userId, [{
          rule: 'anomaly_detection',
          violation: { actionCount: recentActions.length },
          timestamp: Date.now(),
          severity: 'HIGH',
        }]);
      }
    });
  }

  cleanupOldData() {
    // Clean up old data to prevent memory leaks
    const dayAgo = Date.now() - 24 * 60 * 60 * 1000;
    
    // Clean user profiles
    this.userBehaviorProfiles.forEach((profile, userId) => {
      profile.actions = profile.actions.filter(a => a.timestamp > dayAgo);
      profile.flags = profile.flags.filter(f => f.timestamp > dayAgo);
      
      // Remove inactive profiles
      if (profile.actions.length === 0 && Date.now() - profile.lastActivity > 7 * 24 * 60 * 60 * 1000) {
        this.userBehaviorProfiles.delete(userId);
      }
    });

    // Clean suspicious activities
    this.suspiciousActivities.forEach((activity, key) => {
      if (Date.now() - activity.timestamp > 7 * 24 * 60 * 60 * 1000) {
        this.suspiciousActivities.delete(key);
      }
    });
  }

  // Public methods
  isUserBlocked(userId) {
    return this.blockedUsers.has(userId);
  }

  getUserRiskScore(userId) {
    const profile = this.userBehaviorProfiles.get(userId);
    return profile ? profile.riskScore : 0;
  }

  getUserRiskLevel(userId) {
    return this.getRiskLevel(this.getUserRiskScore(userId));
  }

  unblockUser(userId) {
    this.blockedUsers.delete(userId);
    SecurityLogger.logSecurityEvent('USER_UNBLOCKED', {
      userId,
      timestamp: new Date().toISOString(),
    });
  }

  getSuspiciousActivities(limit = 50) {
    return Array.from(this.suspiciousActivities.values())
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  getSystemStats() {
    return {
      totalUsers: this.userBehaviorProfiles.size,
      blockedUsers: this.blockedUsers.size,
      suspiciousActivities: this.suspiciousActivities.size,
      activeRules: this.monitoringRules.size,
      isEnabled: this.isEnabled,
    };
  }

  enable() {
    this.isEnabled = true;
    console.log('Fraud Detection Service: Enabled');
  }

  disable() {
    this.isEnabled = false;
    console.log('Fraud Detection Service: Disabled');
  }
}

// Create singleton instance
const fraudDetectionService = new FraudDetectionService();

export default fraudDetectionService;
