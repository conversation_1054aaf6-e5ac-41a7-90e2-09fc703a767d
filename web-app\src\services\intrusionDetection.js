// ProChat Advanced Anti-Intrusion & Insider Threat Detection System
// Military-grade protection against hackers and malicious employees

import { SecurityLogger } from '../config/security';
import { emergencyModeManager } from '../config/bankingSecurity';
import threatIntelligenceService from './threatIntelligence';
import adminSecurityService from './adminSecurity';
import fraudDetectionService from './fraudDetection';

class IntrusionDetectionService {
  constructor() {
    this.intrusionAttempts = new Map();
    this.insiderThreats = new Map();
    this.behaviorBaselines = new Map();
    this.honeypots = new Map();
    this.tripwires = new Map();
    this.realTimeMonitoring = new Map();
    this.alertThresholds = new Map();
    
    this.initializeIntrusionDetection();
    this.startRealTimeMonitoring();
  }

  initializeIntrusionDetection() {
    // Initialize intrusion detection patterns
    this.setupIntrusionPatterns();
    
    // Initialize insider threat detection
    this.setupInsiderThreatDetection();
    
    // Initialize honeypots and tripwires
    this.setupHoneypots();
    this.setupTripwires();
    
    // Initialize behavioral baselines
    this.setupBehavioralBaselines();
    
    // Initialize alert thresholds
    this.setupAlertThresholds();
  }

  setupIntrusionPatterns() {
    // Network-based intrusion patterns
    this.intrusionPatterns = {
      'port_scanning': {
        name: 'Port Scanning Attack',
        description: 'Systematic scanning of network ports',
        indicators: [
          { type: 'rapid_connections', threshold: 100, timeWindow: 60000 },
          { type: 'sequential_ports', pattern: /^(80|443|22|21|23|25|53|110|143|993|995)$/ },
          { type: 'failed_connections', threshold: 50, timeWindow: 30000 },
        ],
        severity: 'HIGH',
        response: 'block_ip_immediately',
      },
      
      'brute_force_admin': {
        name: 'Admin Brute Force Attack',
        description: 'Repeated failed admin login attempts',
        indicators: [
          { type: 'failed_admin_logins', threshold: 5, timeWindow: 300000 },
          { type: 'password_variations', patterns: ['admin', 'password', '123456'] },
          { type: 'rapid_attempts', threshold: 10, timeWindow: 60000 },
        ],
        severity: 'CRITICAL',
        response: 'emergency_lockdown',
      },
      
      'privilege_escalation': {
        name: 'Privilege Escalation Attempt',
        description: 'Unauthorized attempt to gain higher privileges',
        indicators: [
          { type: 'unauthorized_admin_access', threshold: 1 },
          { type: 'permission_bypass_attempt', threshold: 3, timeWindow: 600000 },
          { type: 'system_file_access', patterns: ['/etc/', '/admin/', '/config/'] },
        ],
        severity: 'CRITICAL',
        response: 'isolate_and_investigate',
      },
      
      'data_exfiltration': {
        name: 'Data Exfiltration Attempt',
        description: 'Unauthorized data extraction',
        indicators: [
          { type: 'bulk_data_download', threshold: 1000, timeWindow: 300000 },
          { type: 'unusual_api_calls', threshold: 500, timeWindow: 600000 },
          { type: 'off_hours_access', timeRestriction: true },
        ],
        severity: 'CRITICAL',
        response: 'block_and_alert_authorities',
      },
      
      'sql_injection_advanced': {
        name: 'Advanced SQL Injection',
        description: 'Sophisticated database attack attempts',
        indicators: [
          { type: 'sql_patterns', patterns: ['UNION SELECT', 'DROP TABLE', 'INSERT INTO'] },
          { type: 'encoded_payloads', patterns: ['%27', '%22', '%3B'] },
          { type: 'time_based_attacks', patterns: ['WAITFOR DELAY', 'SLEEP('] },
        ],
        severity: 'HIGH',
        response: 'block_and_sanitize',
      },
      
      'session_hijacking': {
        name: 'Session Hijacking Attempt',
        description: 'Unauthorized session takeover',
        indicators: [
          { type: 'session_anomaly', threshold: 1 },
          { type: 'ip_change_rapid', threshold: 3, timeWindow: 300000 },
          { type: 'concurrent_sessions', threshold: 5 },
        ],
        severity: 'HIGH',
        response: 'terminate_sessions',
      },
    };
  }

  setupInsiderThreatDetection() {
    // Insider threat behavioral patterns
    this.insiderThreatPatterns = {
      'data_hoarding': {
        name: 'Data Hoarding Behavior',
        description: 'Employee collecting excessive data',
        indicators: [
          { type: 'excessive_downloads', threshold: 100, timeWindow: 86400000 }, // 24 hours
          { type: 'unusual_data_access', departments: ['finance', 'hr', 'admin'] },
          { type: 'off_hours_activity', timePattern: 'outside_business_hours' },
        ],
        severity: 'MEDIUM',
        response: 'flag_for_investigation',
      },
      
      'privilege_abuse': {
        name: 'Privilege Abuse',
        description: 'Misuse of administrative privileges',
        indicators: [
          { type: 'unauthorized_user_access', threshold: 10, timeWindow: 3600000 },
          { type: 'financial_data_access', unauthorized: true },
          { type: 'admin_action_unusual', pattern: 'outside_normal_duties' },
        ],
        severity: 'HIGH',
        response: 'suspend_privileges',
      },
      
      'sabotage_attempt': {
        name: 'System Sabotage',
        description: 'Intentional system damage attempts',
        indicators: [
          { type: 'mass_deletion', threshold: 100, timeWindow: 600000 },
          { type: 'system_config_changes', unauthorized: true },
          { type: 'security_disable_attempt', threshold: 1 },
        ],
        severity: 'CRITICAL',
        response: 'immediate_termination',
      },
      
      'information_theft': {
        name: 'Information Theft',
        description: 'Stealing sensitive information',
        indicators: [
          { type: 'customer_data_bulk_access', threshold: 1000, timeWindow: 3600000 },
          { type: 'financial_records_access', unauthorized: true },
          { type: 'export_sensitive_data', threshold: 1 },
        ],
        severity: 'CRITICAL',
        response: 'legal_action_alert',
      },
    };
  }

  setupHoneypots() {
    // Fake admin endpoints to catch attackers
    this.honeypots.set('fake_admin_panel', {
      url: '/admin/secret',
      description: 'Fake admin panel to catch intruders',
      alertLevel: 'CRITICAL',
      logDetails: true,
    });

    this.honeypots.set('fake_database_backup', {
      url: '/backup/database.sql',
      description: 'Fake database backup file',
      alertLevel: 'HIGH',
      logDetails: true,
    });

    this.honeypots.set('fake_config_file', {
      url: '/config/secrets.json',
      description: 'Fake configuration file with secrets',
      alertLevel: 'HIGH',
      logDetails: true,
    });

    this.honeypots.set('fake_user_data', {
      url: '/api/users/export/all',
      description: 'Fake user data export endpoint',
      alertLevel: 'CRITICAL',
      logDetails: true,
    });
  }

  setupTripwires() {
    // System tripwires for detecting unauthorized access
    this.tripwires.set('admin_directory_access', {
      path: '/admin/*',
      description: 'Unauthorized admin directory access',
      alertLevel: 'CRITICAL',
      autoBlock: true,
    });

    this.tripwires.set('system_file_modification', {
      files: ['package.json', 'config.js', '.env'],
      description: 'Critical system file modification',
      alertLevel: 'CRITICAL',
      autoBlock: true,
    });

    this.tripwires.set('database_direct_access', {
      pattern: 'direct_db_connection',
      description: 'Direct database access attempt',
      alertLevel: 'HIGH',
      autoBlock: true,
    });
  }

  setupBehavioralBaselines() {
    // Establish normal behavior patterns for users and admins
    this.behaviorBaselines.set('normal_admin_activity', {
      loginTimes: { start: 8, end: 18 }, // 8 AM to 6 PM
      averageSessionDuration: 4 * 60 * 60 * 1000, // 4 hours
      typicalActions: ['user_view', 'system_monitor', 'report_generate'],
      maxActionsPerHour: 100,
      allowedIPs: ['***********/24', '10.0.0.0/8'],
    });

    this.behaviorBaselines.set('normal_user_activity', {
      loginTimes: { start: 6, end: 23 }, // 6 AM to 11 PM
      averageSessionDuration: 30 * 60 * 1000, // 30 minutes
      typicalActions: ['post_create', 'like', 'comment', 'message'],
      maxActionsPerHour: 200,
      maxTransactionsPerDay: 10,
    });
  }

  setupAlertThresholds() {
    this.alertThresholds.set('failed_logins', {
      threshold: 5,
      timeWindow: 300000, // 5 minutes
      action: 'temporary_block',
    });

    this.alertThresholds.set('suspicious_api_calls', {
      threshold: 1000,
      timeWindow: 3600000, // 1 hour
      action: 'rate_limit',
    });

    this.alertThresholds.set('data_access_volume', {
      threshold: 10000,
      timeWindow: 3600000, // 1 hour
      action: 'flag_for_review',
    });
  }

  startRealTimeMonitoring() {
    // Real-time monitoring of system activities
    setInterval(() => {
      this.monitorNetworkActivity();
      this.monitorUserBehavior();
      this.monitorSystemIntegrity();
      this.checkHoneypots();
    }, 10000); // Every 10 seconds

    // Behavioral analysis
    setInterval(() => {
      this.analyzeBehavioralAnomalies();
      this.detectInsiderThreats();
    }, 60000); // Every minute

    // Deep security scan
    setInterval(() => {
      this.performDeepSecurityScan();
      this.updateThreatIntelligence();
    }, 300000); // Every 5 minutes

    console.log('🛡️ Intrusion Detection System: Real-time monitoring started');
  }

  // Network activity monitoring
  monitorNetworkActivity() {
    // Monitor for suspicious network patterns
    const recentConnections = this.getRecentNetworkConnections();
    
    recentConnections.forEach(connection => {
      this.analyzeConnectionPattern(connection);
    });
  }

  analyzeConnectionPattern(connection) {
    const { ip, requests, timeWindow, userAgent } = connection;
    
    // Check for port scanning
    if (requests.length > 100 && timeWindow < 60000) {
      this.triggerIntrusionAlert('port_scanning', {
        ip,
        requestCount: requests.length,
        timeWindow,
        severity: 'HIGH',
      });
    }

    // Check for bot-like behavior
    if (this.isBotLikePattern(userAgent, requests)) {
      this.triggerIntrusionAlert('bot_attack', {
        ip,
        userAgent,
        pattern: 'automated_requests',
        severity: 'MEDIUM',
      });
    }

    // Check for SQL injection attempts
    const sqlInjectionAttempts = requests.filter(req => 
      this.containsSQLInjectionPattern(req.url + req.body)
    );
    
    if (sqlInjectionAttempts.length > 0) {
      this.triggerIntrusionAlert('sql_injection_advanced', {
        ip,
        attempts: sqlInjectionAttempts.length,
        patterns: sqlInjectionAttempts.map(a => a.pattern),
        severity: 'HIGH',
      });
    }
  }

  // User behavior monitoring
  monitorUserBehavior() {
    const activeUsers = this.getActiveUsers();
    
    activeUsers.forEach(user => {
      this.analyzeBehavioralPattern(user);
    });
  }

  analyzeBehavioralPattern(user) {
    const { userId, actions, sessionData, role } = user;
    const baseline = this.behaviorBaselines.get(
      role === 'admin' ? 'normal_admin_activity' : 'normal_user_activity'
    );

    // Check for unusual activity times
    const currentHour = new Date().getHours();
    if (currentHour < baseline.loginTimes.start || currentHour > baseline.loginTimes.end) {
      if (role === 'admin') {
        this.flagSuspiciousActivity(userId, 'off_hours_admin_activity', {
          time: currentHour,
          expectedRange: baseline.loginTimes,
        });
      }
    }

    // Check for excessive actions
    const actionsPerHour = this.calculateActionsPerHour(actions);
    if (actionsPerHour > baseline.maxActionsPerHour) {
      this.flagSuspiciousActivity(userId, 'excessive_activity', {
        actionsPerHour,
        threshold: baseline.maxActionsPerHour,
      });
    }

    // Check for unusual action patterns
    const unusualActions = actions.filter(action => 
      !baseline.typicalActions.includes(action.type)
    );
    
    if (unusualActions.length > 10) {
      this.flagSuspiciousActivity(userId, 'unusual_action_pattern', {
        unusualActions: unusualActions.map(a => a.type),
        count: unusualActions.length,
      });
    }
  }

  // System integrity monitoring
  monitorSystemIntegrity() {
    // Check for unauthorized file modifications
    this.checkFileIntegrity();
    
    // Check for unauthorized process execution
    this.checkProcessIntegrity();
    
    // Check for memory anomalies
    this.checkMemoryIntegrity();
  }

  checkFileIntegrity() {
    // In production, this would check file hashes
    const criticalFiles = [
      'package.json',
      'src/config/security.js',
      'src/services/adminSecurity.js',
    ];

    criticalFiles.forEach(file => {
      // Simulate file integrity check
      const currentHash = this.calculateFileHash(file);
      const expectedHash = this.getExpectedFileHash(file);
      
      if (currentHash !== expectedHash) {
        this.triggerIntrusionAlert('file_modification', {
          file,
          currentHash,
          expectedHash,
          severity: 'CRITICAL',
        });
      }
    });
  }

  // Honeypot monitoring
  checkHoneypots() {
    this.honeypots.forEach((honeypot, honeypotId) => {
      const accessAttempts = this.getHoneypotAccessAttempts(honeypot.url);
      
      if (accessAttempts.length > 0) {
        accessAttempts.forEach(attempt => {
          this.triggerIntrusionAlert('honeypot_triggered', {
            honeypotId,
            honeypot: honeypot.description,
            ip: attempt.ip,
            userAgent: attempt.userAgent,
            timestamp: attempt.timestamp,
            severity: honeypot.alertLevel,
          });
        });
      }
    });
  }

  // Behavioral anomaly detection
  analyzeBehavioralAnomalies() {
    const users = this.getAllUsers();
    
    users.forEach(user => {
      const anomalies = this.detectAnomalies(user);
      
      if (anomalies.length > 0) {
        this.processAnomalies(user.userId, anomalies);
      }
    });
  }

  detectAnomalies(user) {
    const anomalies = [];
    const { userId, recentActivity, role, permissions } = user;

    // Detect privilege escalation attempts
    const unauthorizedActions = recentActivity.filter(action => 
      !permissions.includes(action.requiredPermission)
    );
    
    if (unauthorizedActions.length > 0) {
      anomalies.push({
        type: 'privilege_escalation',
        severity: 'HIGH',
        details: unauthorizedActions,
      });
    }

    // Detect data hoarding
    const dataAccessActions = recentActivity.filter(action => 
      action.type === 'data_access' || action.type === 'data_export'
    );
    
    if (dataAccessActions.length > 50) {
      anomalies.push({
        type: 'data_hoarding',
        severity: 'MEDIUM',
        details: { accessCount: dataAccessActions.length },
      });
    }

    // Detect unusual login patterns
    const loginLocations = this.getRecentLoginLocations(userId);
    if (loginLocations.length > 5) {
      anomalies.push({
        type: 'unusual_login_pattern',
        severity: 'MEDIUM',
        details: { locations: loginLocations },
      });
    }

    return anomalies;
  }

  // Insider threat detection
  detectInsiderThreats() {
    const employees = this.getEmployeeUsers();
    
    employees.forEach(employee => {
      const threatLevel = this.assessInsiderThreatLevel(employee);
      
      if (threatLevel.level >= 7) { // High threat level
        this.handleInsiderThreat(employee, threatLevel);
      }
    });
  }

  assessInsiderThreatLevel(employee) {
    let threatScore = 0;
    const indicators = [];

    // Check for recent disciplinary actions
    if (employee.disciplinaryActions && employee.disciplinaryActions.length > 0) {
      threatScore += 3;
      indicators.push('disciplinary_history');
    }

    // Check for unusual data access
    const dataAccess = this.getEmployeeDataAccess(employee.userId);
    if (dataAccess.sensitiveDataAccess > 100) {
      threatScore += 4;
      indicators.push('excessive_data_access');
    }

    // Check for off-hours activity
    const offHoursActivity = this.getOffHoursActivity(employee.userId);
    if (offHoursActivity.percentage > 30) {
      threatScore += 2;
      indicators.push('off_hours_activity');
    }

    // Check for privilege misuse
    const privilegeMisuse = this.detectPrivilegeMisuse(employee.userId);
    if (privilegeMisuse.detected) {
      threatScore += 5;
      indicators.push('privilege_misuse');
    }

    return {
      level: threatScore,
      indicators,
      riskCategory: this.categorizeRisk(threatScore),
    };
  }

  // Alert and response handling
  triggerIntrusionAlert(alertType, details) {
    const alertId = this.generateAlertId();
    const alert = {
      id: alertId,
      type: alertType,
      details,
      timestamp: Date.now(),
      status: 'active',
      severity: details.severity || 'MEDIUM',
    };

    this.intrusionAttempts.set(alertId, alert);

    // Log security event
    SecurityLogger.logSecurityEvent('INTRUSION_DETECTED', {
      alertId,
      alertType,
      severity: alert.severity,
      details,
    });

    // Execute response based on severity
    this.executeIntrusionResponse(alert);

    // Notify security team
    this.notifySecurityTeam(alert);

    return alertId;
  }

  executeIntrusionResponse(alert) {
    const { type, details, severity } = alert;

    switch (severity) {
      case 'CRITICAL':
        this.executeCriticalResponse(alert);
        break;
      case 'HIGH':
        this.executeHighResponse(alert);
        break;
      case 'MEDIUM':
        this.executeMediumResponse(alert);
        break;
      default:
        this.executeDefaultResponse(alert);
    }
  }

  executeCriticalResponse(alert) {
    const { details } = alert;

    // Immediate actions for critical threats
    if (details.ip) {
      this.blockIPImmediately(details.ip, 'Critical intrusion detected');
    }

    // Activate emergency mode for certain threats
    if (['brute_force_admin', 'privilege_escalation'].includes(alert.type)) {
      emergencyModeManager.activateEmergencyMode(
        `Critical intrusion: ${alert.type}`,
        ['view_only']
      );
    }

    // Terminate all admin sessions if admin compromise suspected
    if (alert.type === 'brute_force_admin') {
      adminSecurityService.terminateAllAdminSessions('Security breach detected');
    }

    // Alert authorities for severe threats
    this.alertAuthorities(alert);
  }

  executeHighResponse(alert) {
    const { details } = alert;

    if (details.ip) {
      this.blockIPTemporarily(details.ip, 3600000); // 1 hour
    }

    if (details.userId) {
      fraudDetectionService.blockUser(details.userId, `Security threat: ${alert.type}`);
    }

    // Increase monitoring for related activities
    this.increaseMonitoring(alert);
  }

  executeMediumResponse(alert) {
    const { details } = alert;

    // Rate limit the source
    if (details.ip) {
      this.applyRateLimit(details.ip, 600000); // 10 minutes
    }

    // Flag for investigation
    this.flagForInvestigation(alert);
  }

  // Utility methods
  generateAlertId() {
    return `intrusion_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getRecentNetworkConnections() {
    // In production, get from network monitoring system
    return [];
  }

  isBotLikePattern(userAgent, requests) {
    const botPatterns = [
      /bot/i, /crawler/i, /spider/i, /scraper/i,
      /curl/i, /wget/i, /python/i
    ];

    return botPatterns.some(pattern => pattern.test(userAgent)) ||
           requests.length > 100; // High request volume
  }

  containsSQLInjectionPattern(input) {
    const sqlPatterns = [
      /(\%27)|(\')|(\-\-)|(\%23)|(#)/i,
      /((\%3D)|(=))[^\n]*((\%27)|(\')|(\-\-)|(\%3B)|(;))/i,
      /\w*((\%27)|(\'))((\%6F)|o|(\%4F))((\%72)|r|(\%52))/i,
      /union.*select/i,
      /drop.*table/i,
      /insert.*into/i,
    ];

    return sqlPatterns.some(pattern => pattern.test(input));
  }

  calculateFileHash(filename) {
    // In production, calculate actual file hash
    return `hash_${filename}_${Date.now()}`;
  }

  getExpectedFileHash(filename) {
    // In production, get from secure hash database
    return `expected_hash_${filename}`;
  }

  blockIPImmediately(ip, reason) {
    console.log(`🚫 BLOCKING IP ${ip}: ${reason}`);
    // In production, update firewall rules
  }

  blockIPTemporarily(ip, duration) {
    console.log(`⏰ TEMPORARILY BLOCKING IP ${ip} for ${duration}ms`);
    // In production, set temporary firewall rule
  }

  applyRateLimit(ip, duration) {
    console.log(`🐌 RATE LIMITING IP ${ip} for ${duration}ms`);
    // In production, apply rate limiting
  }

  alertAuthorities(alert) {
    console.log(`🚨 ALERTING AUTHORITIES: ${alert.type}`);
    // In production, send alert to law enforcement
  }

  notifySecurityTeam(alert) {
    console.log(`📢 SECURITY TEAM ALERT: ${alert.type} - ${alert.severity}`);
    // In production, send notifications to security team
  }

  flagForInvestigation(alert) {
    console.log(`🔍 FLAGGED FOR INVESTIGATION: ${alert.id}`);
    // In production, add to investigation queue
  }

  increaseMonitoring(alert) {
    console.log(`👁️ INCREASED MONITORING: ${alert.type}`);
    // In production, enhance monitoring for related patterns
  }

  // Public API methods
  getIntrusionStatistics() {
    const activeAlerts = Array.from(this.intrusionAttempts.values())
      .filter(alert => alert.status === 'active');

    return {
      totalIntrusions: this.intrusionAttempts.size,
      activeAlerts: activeAlerts.length,
      criticalAlerts: activeAlerts.filter(a => a.severity === 'CRITICAL').length,
      highAlerts: activeAlerts.filter(a => a.severity === 'HIGH').length,
      mediumAlerts: activeAlerts.filter(a => a.severity === 'MEDIUM').length,
      honeypotTriggers: activeAlerts.filter(a => a.type === 'honeypot_triggered').length,
    };
  }

  getActiveAlerts(limit = 50) {
    return Array.from(this.intrusionAttempts.values())
      .filter(alert => alert.status === 'active')
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  getInsiderThreatAssessments() {
    return Array.from(this.insiderThreats.values())
      .sort((a, b) => b.threatLevel - a.threatLevel);
  }

  acknowledgeAlert(alertId) {
    const alert = this.intrusionAttempts.get(alertId);
    if (alert) {
      alert.status = 'acknowledged';
      alert.acknowledgedAt = Date.now();
    }
  }

  resolveAlert(alertId, resolution) {
    const alert = this.intrusionAttempts.get(alertId);
    if (alert) {
      alert.status = 'resolved';
      alert.resolvedAt = Date.now();
      alert.resolution = resolution;
    }
  }

  // Mock data methods (replace with real implementations)
  getActiveUsers() { return []; }
  getAllUsers() { return []; }
  getEmployeeUsers() { return []; }
  calculateActionsPerHour(actions) { return 0; }
  getHoneypotAccessAttempts(url) { return []; }
  getRecentLoginLocations(userId) { return []; }
  getEmployeeDataAccess(userId) { return { sensitiveDataAccess: 0 }; }
  getOffHoursActivity(userId) { return { percentage: 0 }; }
  detectPrivilegeMisuse(userId) { return { detected: false }; }
  categorizeRisk(score) { return score >= 7 ? 'HIGH' : score >= 4 ? 'MEDIUM' : 'LOW'; }
  flagSuspiciousActivity(userId, type, details) { console.log(`🚩 Suspicious: ${type} for ${userId}`); }
  processAnomalies(userId, anomalies) { console.log(`🔍 Processing anomalies for ${userId}`); }
  handleInsiderThreat(employee, threatLevel) { console.log(`⚠️ Insider threat: ${employee.userId}`); }
  checkFileIntegrity() { /* File integrity check */ }
  checkProcessIntegrity() { /* Process integrity check */ }
  checkMemoryIntegrity() { /* Memory integrity check */ }
}

// Create singleton instance
const intrusionDetectionService = new IntrusionDetectionService();

export default intrusionDetectionService;
