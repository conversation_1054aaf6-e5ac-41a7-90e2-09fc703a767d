// ProChat Advanced Push Notification Service
// Cross-platform notification system with Firebase integration

import { getMessaging, getToken, onMessage } from 'firebase/messaging';
import { app } from '../config/firebase';
import { SecurityLogger } from '../config/security';
import offlineStorage from './offlineStorage';

class NotificationService {
  constructor() {
    this.messaging = null;
    this.isSupported = false;
    this.isPermissionGranted = false;
    this.currentToken = null;
    this.listeners = new Map();
    
    this.init();
  }

  async init() {
    try {
      // Check if notifications are supported
      this.isSupported = 'Notification' in window && 'serviceWorker' in navigator;
      
      if (!this.isSupported) {
        console.warn('Push notifications are not supported in this browser');
        return;
      }

      // Initialize Firebase Messaging
      this.messaging = getMessaging(app);
      
      // Setup message listener
      this.setupMessageListener();
      
      console.log('ProChat Notifications: Service initialized');
    } catch (error) {
      console.error('ProChat Notifications: Initialization failed', error);
    }
  }

  async requestPermission() {
    if (!this.isSupported) {
      return { success: false, error: 'Notifications not supported' };
    }

    try {
      // Request notification permission
      const permission = await Notification.requestPermission();
      
      if (permission === 'granted') {
        this.isPermissionGranted = true;
        
        // Get FCM token
        const token = await this.getFCMToken();
        
        if (token) {
          this.currentToken = token;
          
          // Save token to backend
          await this.saveTokenToBackend(token);
          
          SecurityLogger.logSecurityEvent('NOTIFICATION_PERMISSION_GRANTED', {
            token: token.substring(0, 20) + '...',
            platform: 'web',
          });
          
          return { success: true, token };
        } else {
          return { success: false, error: 'Failed to get FCM token' };
        }
      } else {
        SecurityLogger.logSecurityEvent('NOTIFICATION_PERMISSION_DENIED', {
          permission,
          platform: 'web',
        });
        
        return { success: false, error: 'Permission denied' };
      }
    } catch (error) {
      console.error('Failed to request notification permission:', error);
      return { success: false, error: error.message };
    }
  }

  async getFCMToken() {
    if (!this.messaging) return null;

    try {
      const vapidKey = process.env.REACT_APP_FIREBASE_VAPID_KEY;
      
      const token = await getToken(this.messaging, {
        vapidKey: vapidKey || undefined,
      });
      
      return token;
    } catch (error) {
      console.error('Failed to get FCM token:', error);
      return null;
    }
  }

  async saveTokenToBackend(token) {
    try {
      const response = await fetch('/api/notifications/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('prochat_access_token')}`,
        },
        body: JSON.stringify({
          token,
          platform: 'web',
          deviceInfo: {
            userAgent: navigator.userAgent,
            language: navigator.language,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          },
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save token to backend');
      }

      console.log('FCM token saved to backend successfully');
    } catch (error) {
      console.error('Failed to save FCM token to backend:', error);
    }
  }

  setupMessageListener() {
    if (!this.messaging) return;

    onMessage(this.messaging, (payload) => {
      console.log('Message received in foreground:', payload);
      
      // Handle foreground message
      this.handleForegroundMessage(payload);
      
      // Save notification to offline storage
      this.saveNotificationOffline(payload);
      
      // Emit to listeners
      this.emit('message-received', payload);
    });
  }

  async handleForegroundMessage(payload) {
    const { notification, data } = payload;
    
    if (!notification) return;

    try {
      // Show browser notification
      const notificationOptions = {
        body: notification.body,
        icon: notification.icon || '/logo192.png',
        badge: '/badge-72x72.png',
        image: notification.image,
        tag: data?.tag || 'prochat-notification',
        renotify: true,
        requireInteraction: data?.requireInteraction === 'true',
        silent: data?.silent === 'true',
        vibrate: data?.vibrate ? JSON.parse(data.vibrate) : [100, 50, 100],
        data: {
          ...data,
          timestamp: Date.now(),
          platform: 'web',
        },
        actions: this.getNotificationActions(data?.type),
      };

      const browserNotification = new Notification(notification.title, notificationOptions);
      
      // Handle notification click
      browserNotification.onclick = (event) => {
        event.preventDefault();
        this.handleNotificationClick(data);
        browserNotification.close();
      };

      // Auto-close after 5 seconds if not persistent
      if (!notificationOptions.requireInteraction) {
        setTimeout(() => {
          browserNotification.close();
        }, 5000);
      }

    } catch (error) {
      console.error('Failed to show foreground notification:', error);
    }
  }

  getNotificationActions(type) {
    const actions = [];

    switch (type) {
      case 'message':
        actions.push(
          { action: 'reply', title: 'Jibu', icon: '/icons/reply.png' },
          { action: 'view', title: 'Angalia', icon: '/icons/view.png' }
        );
        break;
      case 'wallet':
        actions.push(
          { action: 'view-wallet', title: 'Angalia Wallet', icon: '/icons/wallet.png' },
          { action: 'dismiss', title: 'Funga', icon: '/icons/close.png' }
        );
        break;
      case 'social':
        actions.push(
          { action: 'like', title: 'Penda', icon: '/icons/like.png' },
          { action: 'view-post', title: 'Angalia', icon: '/icons/view.png' }
        );
        break;
      default:
        actions.push(
          { action: 'view', title: 'Angalia', icon: '/icons/view.png' },
          { action: 'dismiss', title: 'Funga', icon: '/icons/close.png' }
        );
    }

    return actions;
  }

  async handleNotificationClick(data) {
    try {
      // Focus or open window
      if (window.focus) {
        window.focus();
      }

      // Navigate based on notification type
      const { type, conversationId, postId, transactionId, url } = data || {};

      switch (type) {
        case 'message':
          if (conversationId) {
            window.location.href = `/chat/${conversationId}`;
          } else {
            window.location.href = '/chats';
          }
          break;
        case 'wallet':
          if (transactionId) {
            window.location.href = `/transaction/${transactionId}`;
          } else {
            window.location.href = '/propay';
          }
          break;
        case 'social':
          if (postId) {
            window.location.href = `/post/${postId}`;
          } else {
            window.location.href = '/home';
          }
          break;
        case 'event':
          window.location.href = '/discover?tab=events';
          break;
        case 'job':
          window.location.href = '/discover?tab=jobs';
          break;
        default:
          if (url) {
            window.location.href = url;
          } else {
            window.location.href = '/';
          }
      }

      // Log notification interaction
      SecurityLogger.logSecurityEvent('NOTIFICATION_CLICKED', {
        type,
        platform: 'web',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      console.error('Failed to handle notification click:', error);
    }
  }

  async saveNotificationOffline(payload) {
    try {
      const notification = {
        title: payload.notification?.title,
        body: payload.notification?.body,
        icon: payload.notification?.icon,
        data: payload.data,
        timestamp: new Date().toISOString(),
        read: false,
        platform: 'web',
      };

      await offlineStorage.add('notifications', notification);
    } catch (error) {
      console.error('Failed to save notification offline:', error);
    }
  }

  // Local notification methods
  async showLocalNotification(title, options = {}) {
    if (!this.isPermissionGranted) {
      const result = await this.requestPermission();
      if (!result.success) {
        return { success: false, error: 'Permission not granted' };
      }
    }

    try {
      const notification = new Notification(title, {
        body: options.body,
        icon: options.icon || '/logo192.png',
        badge: '/badge-72x72.png',
        tag: options.tag || 'prochat-local',
        ...options,
      });

      return { success: true, notification };
    } catch (error) {
      console.error('Failed to show local notification:', error);
      return { success: false, error: error.message };
    }
  }

  // Notification management
  async getNotificationHistory(limit = 50) {
    try {
      return await offlineStorage.getAll('notifications', limit);
    } catch (error) {
      console.error('Failed to get notification history:', error);
      return [];
    }
  }

  async markNotificationAsRead(id) {
    try {
      const notification = await offlineStorage.get('notifications', id);
      if (notification) {
        notification.read = true;
        await offlineStorage.update('notifications', notification);
      }
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  }

  async clearNotifications() {
    try {
      await offlineStorage.clear('notifications');
      return { success: true };
    } catch (error) {
      console.error('Failed to clear notifications:', error);
      return { success: false, error: error.message };
    }
  }

  // Subscription management
  async updateSubscription(preferences) {
    try {
      const response = await fetch('/api/notifications/preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('prochat_access_token')}`,
        },
        body: JSON.stringify({
          ...preferences,
          platform: 'web',
          token: this.currentToken,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update notification preferences');
      }

      return { success: true };
    } catch (error) {
      console.error('Failed to update notification preferences:', error);
      return { success: false, error: error.message };
    }
  }

  async unsubscribe() {
    try {
      if (this.currentToken) {
        const response = await fetch('/api/notifications/unsubscribe', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('prochat_access_token')}`,
          },
          body: JSON.stringify({
            token: this.currentToken,
            platform: 'web',
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to unsubscribe from notifications');
        }
      }

      this.currentToken = null;
      this.isPermissionGranted = false;

      SecurityLogger.logSecurityEvent('NOTIFICATION_UNSUBSCRIBED', {
        platform: 'web',
      });

      return { success: true };
    } catch (error) {
      console.error('Failed to unsubscribe from notifications:', error);
      return { success: false, error: error.message };
    }
  }

  // Event system
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Notification event callback error:', error);
        }
      });
    }
  }

  // Utility methods
  getPermissionStatus() {
    return {
      supported: this.isSupported,
      permission: Notification.permission,
      granted: this.isPermissionGranted,
      token: this.currentToken,
    };
  }

  async testNotification() {
    return this.showLocalNotification('ProChat Test', {
      body: 'Hii ni notification ya majaribio',
      tag: 'test-notification',
    });
  }
}

// Create singleton instance
const notificationService = new NotificationService();

export default notificationService;
