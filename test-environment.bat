@echo off
color 0A
echo ========================================
echo    ProChat Environment Test
echo ========================================
echo.

set "success_count=0"
set "total_tests=10"

echo 🧪 Running comprehensive environment tests...
echo.

REM Test 1: Node.js
echo [1/10] Testing Node.js...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js: WORKING
    node --version
    set /a success_count+=1
) else (
    echo ❌ Node.js: FAILED
)
echo.

REM Test 2: npm
echo [2/10] Testing npm...
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ npm: WORKING
    npm --version
    set /a success_count+=1
) else (
    echo ❌ npm: FAILED
)
echo.

REM Test 3: Java
echo [3/10] Testing Java...
java -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Java: WORKING
    java -version 2>&1 | findstr "version"
    set /a success_count+=1
) else (
    echo ❌ Java: FAILED
)
echo.

REM Test 4: Maven
echo [4/10] Testing Maven...
mvn -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Maven: WORKING
    mvn -version | findstr "Apache Maven"
    set /a success_count+=1
) else (
    echo ❌ Maven: FAILED
)
echo.

REM Test 5: MySQL
echo [5/10] Testing MySQL...
mysql --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ MySQL: WORKING
    mysql --version
    set /a success_count+=1
) else (
    echo ❌ MySQL: FAILED
)
echo.

REM Test 6: MySQL Connection
echo [6/10] Testing MySQL Connection...
mysql -u root -p"Ram$0101" -e "SELECT 'Connection OK' as status;" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ MySQL Connection: WORKING
    mysql -u root -p"Ram$0101" -e "SELECT 'Connection OK' as status;"
    set /a success_count+=1
) else (
    echo ❌ MySQL Connection: FAILED
)
echo.

REM Test 7: Database Exists
echo [7/10] Testing ProChat Database...
mysql -u root -p"Ram$0101" -e "USE prochat_db; SELECT 'Database OK' as status;" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ ProChat Database: EXISTS
    mysql -u root -p"Ram$0101" -e "USE prochat_db; SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'prochat_db';"
    set /a success_count+=1
) else (
    echo ❌ ProChat Database: NOT FOUND
)
echo.

REM Test 8: Web-app package.json
echo [8/10] Testing Web-app Structure...
if exist "web-app\package.json" (
    echo ✅ Web-app Structure: EXISTS
    echo Found package.json in web-app
    set /a success_count+=1
) else (
    echo ❌ Web-app Structure: MISSING
)
echo.

REM Test 9: Backend Structure
echo [9/10] Testing Backend Structure...
if exist "backend\pom.xml" (
    echo ✅ Backend Structure: EXISTS
    echo Found pom.xml in backend
    set /a success_count+=1
) else (
    echo ❌ Backend Structure: MISSING
)
echo.

REM Test 10: Configuration Files
echo [10/10] Testing Configuration Files...
if exist "backend\src\main\resources\application.yml" (
    echo ✅ Configuration Files: EXISTS
    echo Found application.yml
    set /a success_count+=1
) else (
    echo ❌ Configuration Files: MISSING
)
echo.

echo ========================================
echo Test Results Summary
echo ========================================
echo.
echo Tests Passed: %success_count%/%total_tests%
echo.

if %success_count% geq 8 (
    echo 🎉 EXCELLENT! Environment is mostly ready!
    echo.
    echo ✅ Ready to run ProChat development environment
    echo.
    echo Next steps:
    echo 1. Run: run-project-local.bat
    echo 2. Open: http://localhost:3000 (Web App)
    echo 3. Open: http://localhost:3001 (Admin Panel)
    echo 4. Open: http://localhost:3002 (Public Website)
) else if %success_count% geq 6 (
    echo ⚠️ GOOD! Most components are working
    echo.
    echo Some issues need to be resolved:
    if %success_count% lss 8 echo - Check failed components above
    echo - Install missing software
    echo - Fix configuration issues
) else (
    echo ❌ NEEDS WORK! Several components are missing
    echo.
    echo Please install:
    echo - Node.js (https://nodejs.org/)
    echo - Java 11 (https://adoptium.net/)
    echo - Maven (https://maven.apache.org/)
    echo - MySQL 8.0 (https://dev.mysql.com/downloads/)
)

echo.
echo ========================================
echo Environment test completed!
echo ========================================
pause
