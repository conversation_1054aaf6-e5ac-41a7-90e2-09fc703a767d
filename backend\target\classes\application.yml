# ProChat Backend Configuration
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: prochat-backend

  # Database Configuration
  datasource:
    url: jdbc:h2:mem:prochat_db;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
    username: sa
    password:
    driver-class-name: org.h2.Driver

  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true

  # H2 Console Configuration
  h2:
    console:
      enabled: true
      path: /h2-console

  # File Upload Configuration
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

  # Email Configuration (for notifications)
  mail:
    host: smtp.gmail.com
    port: 587
    username: ${EMAIL_USERNAME:<EMAIL>}
    password: ${EMAIL_PASSWORD:Ram$0101}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

# AWS S3 Configuration
aws:
  s3:
    bucket-name: prochat-media-storage
    region: us-east-1
    access-key: ********************
    secret-key: xJk6tgfSRRLg4B/F3Nfe64wc02nlXJ8cphPpY3vK

# JWT Configuration
jwt:
  secret: 138a6ffe9eaadc0fae5368ececa8a52dbc4b31b7e36bbcd5d9b27e49e6d081c5c898375073ff68d40a554175bc5c9b143cd04a18c9a2992e1136f7b907ff05a3791395ecff50ac87a422300c8a2d62605e7a93d4019cef460bb7bbf89cb4ba9a5b09416bcbf301f94e547f32e27963b38f8aa550d061833c93144968235b5d1656103b8151c2dae299e107abc60983ba9189017e90125e96d9cd9a5f093136674166dfb1ba897fa1aa81566dbb210046c935a5bce277179f2de06d0f1f53fcc3dd4f01c968cb9b5588736a2de3e449e036800966be4950746f2619b808f5203d0ca8f4f73774b4d73c595890b9b622d3c3ccf7e69e6aad424e478d98e54d98f3
  expiration: ******** # 24 hours
  refresh-expiration: ********* # 7 days

# ProPay Configuration
propay:
  transaction-fee: 0.025 # 2.5%
  withdrawal-fee: 1000 # TSH
  minimum-withdrawal: 10000 # TSH
  maximum-withdrawal: 5000000 # TSH
  agent-commission: 0.01 # 1%
  merchant-commission: 0.015 # 1.5%

# SMS Configuration (for notifications and invitations)
sms:
  provider: twilio
  account-sid: ${TWILIO_ACCOUNT_SID:}
  auth-token: ${TWILIO_AUTH_TOKEN:}
  from-number: ${TWILIO_FROM_NUMBER:}

# Push Notifications
firebase:
  server-key: ${FIREBASE_SERVER_KEY:}

# Security Configuration
security:
  cors:
    allowed-origins:
      - http://localhost:3000
      - http://localhost:3001
      - http://localhost:3002
      - https://prochat.co.tz
      - https://admin.prochat.co.tz
  rate-limit:
    requests-per-minute: 100

# Logging
logging:
  level:
    com.prochat: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/prochat.log

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# CORS Configuration
cors:
  allowed-origins:
    - http://localhost:3000
    - http://localhost:19006
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers:
    - "*"
  allow-credentials: true

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev

  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update

logging:
  level:
    com.prochat: DEBUG

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod

  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate

logging:
  level:
    com.prochat: WARN
