// ProChat Social Commerce Product Post Component
// Twitter-like post with integrated e-commerce

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Box,
  Typography,
  Button,
  IconButton,
  Avatar,
  Chip,
  Badge,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Divider,
  Rating,
  Tooltip,
} from '@mui/material';
import {
  Favorite,
  FavoriteBorder,
  Comment,
  Share,
  ShoppingCart,
  Add,
  Remove,
  LocalShipping,
  Verified,
  Store,
  MonetizationOn,
  Inventory,
  Star,
  LocationOn,
  Schedule,
  Security,
} from '@mui/icons-material';

import { useAuth } from '../../contexts/AuthContext';
import socialCommerceService from '../../services/socialCommerceService';

const ProductPost = ({ post, onLike, onComment, onShare, onPurchase }) => {
  const { user } = useAuth();
  const [liked, setLiked] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [purchaseDialog, setPurchaseDialog] = useState(false);
  const [loading, setLoading] = useState(false);

  // Extract product info from post
  const product = post.products?.[0];
  const productInfo = product ? {
    id: product.productId,
    name: post.content.productName || 'Product',
    price: product.displayPrice,
    currency: product.currency || 'TZS',
    stock: product.stockStatus,
    image: post.content.media?.[0],
    seller: post.userInfo,
  } : null;

  const handleLike = () => {
    setLiked(!liked);
    onLike?.(post.id, !liked);
  };

  const handlePurchase = async () => {
    try {
      setLoading(true);
      const result = await socialCommerceService.processSocialPurchase(
        user.id,
        post.id,
        product.productId,
        quantity,
        'wallet'
      );

      if (result.success) {
        alert(`Purchase successful! Order ID: ${result.orderId}`);
        setPurchaseDialog(false);
        onPurchase?.(result);
      } else {
        alert('Purchase failed: ' + result.reason);
      }
    } catch (error) {
      alert('Purchase error: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price, currency) => {
    return `${currency} ${price.toLocaleString()}`;
  };

  const getStockStatus = (stock) => {
    if (stock === 'in_stock') return { label: 'In Stock', color: 'success' };
    if (stock === 'low_stock') return { label: 'Low Stock', color: 'warning' };
    if (stock === 'out_of_stock') return { label: 'Out of Stock', color: 'error' };
    return { label: 'Unknown', color: 'default' };
  };

  if (!productInfo) {
    return null; // Not a product post
  }

  const stockStatus = getStockStatus(productInfo.stock);

  return (
    <>
      <Card sx={{ mb: 2, border: '2px solid', borderColor: 'primary.light' }}>
        {/* Post Header */}
        <CardContent sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar src={productInfo.seller?.avatar} sx={{ mr: 2 }}>
              <Store />
            </Avatar>
            <Box sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography variant="h6" sx={{ mr: 1 }}>
                  {productInfo.seller?.name || 'Business'}
                </Typography>
                <Verified color="primary" sx={{ fontSize: 16, mr: 1 }} />
                <Chip label="Business" size="small" color="primary" variant="outlined" />
              </Box>
              <Typography variant="caption" color="text.secondary">
                <Schedule sx={{ fontSize: 12, mr: 0.5 }} />
                {new Date(post.createdAt).toLocaleString()}
              </Typography>
            </Box>
            <IconButton size="small">
              <Share />
            </IconButton>
          </Box>

          {/* Post Content */}
          <Typography variant="body1" sx={{ mb: 2 }}>
            {post.content.text}
          </Typography>

          {/* Product Image */}
          {productInfo.image && (
            <Box
              component="img"
              src={productInfo.image}
              alt={productInfo.name}
              sx={{
                width: '100%',
                maxHeight: 300,
                objectFit: 'cover',
                borderRadius: 1,
                mb: 2,
              }}
            />
          )}

          {/* Product Info Card */}
          <Card variant="outlined" sx={{ bgcolor: 'grey.50', p: 2 }}>
            <Grid container spacing={2} alignItems="center">
              {/* Product Name & Price */}
              <Grid item xs={12} sm={8}>
                <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                  🛍️ {productInfo.name}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="h5" color="primary.main" sx={{ fontWeight: 'bold', mr: 2 }}>
                    {formatPrice(productInfo.price, productInfo.currency)}
                  </Typography>
                  <Chip 
                    label={stockStatus.label}
                    color={stockStatus.color}
                    size="small"
                    icon={<Inventory />}
                  />
                </Box>
                
                {/* Product Rating */}
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Rating value={4.5} precision={0.1} size="small" readOnly />
                  <Typography variant="caption" sx={{ ml: 1 }}>
                    (4.5) • 127 reviews
                  </Typography>
                </Box>

                {/* Location & Shipping */}
                <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 1 }}>
                  <Chip 
                    icon={<LocationOn />}
                    label="Dar es Salaam"
                    size="small"
                    variant="outlined"
                  />
                  <Chip 
                    icon={<LocalShipping />}
                    label="Free Shipping"
                    size="small"
                    variant="outlined"
                    color="success"
                  />
                  <Chip 
                    icon={<Security />}
                    label="Secure Payment"
                    size="small"
                    variant="outlined"
                    color="info"
                  />
                </Box>
              </Grid>

              {/* Purchase Actions */}
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center' }}>
                  {/* Quantity Selector */}
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                    <IconButton 
                      size="small" 
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      disabled={quantity <= 1}
                    >
                      <Remove />
                    </IconButton>
                    <Typography variant="h6" sx={{ mx: 2, minWidth: 30, textAlign: 'center' }}>
                      {quantity}
                    </Typography>
                    <IconButton 
                      size="small" 
                      onClick={() => setQuantity(quantity + 1)}
                      disabled={productInfo.stock === 'out_of_stock'}
                    >
                      <Add />
                    </IconButton>
                  </Box>

                  {/* Purchase Buttons */}
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<MonetizationOn />}
                      onClick={() => setPurchaseDialog(true)}
                      disabled={productInfo.stock === 'out_of_stock'}
                      fullWidth
                    >
                      Buy Now
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<ShoppingCart />}
                      disabled={productInfo.stock === 'out_of_stock'}
                      fullWidth
                    >
                      Add to Cart
                    </Button>
                  </Box>

                  {/* Total Price */}
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Total: {formatPrice(productInfo.price * quantity, productInfo.currency)}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Card>

          {/* Hashtags */}
          {post.content.hashtags && (
            <Box sx={{ mt: 2 }}>
              {post.content.hashtags.map((tag, index) => (
                <Chip
                  key={index}
                  label={tag}
                  size="small"
                  variant="outlined"
                  sx={{ mr: 1, mb: 1 }}
                  clickable
                />
              ))}
            </Box>
          )}
        </CardContent>

        {/* Post Actions */}
        <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title={liked ? 'Unlike' : 'Like'}>
              <IconButton onClick={handleLike} color={liked ? 'error' : 'default'}>
                <Badge badgeContent={post.engagement?.likes || 0} color="error">
                  {liked ? <Favorite /> : <FavoriteBorder />}
                </Badge>
              </IconButton>
            </Tooltip>

            <Tooltip title="Comment">
              <IconButton onClick={() => onComment?.(post.id)}>
                <Badge badgeContent={post.engagement?.comments || 0} color="primary">
                  <Comment />
                </Badge>
              </IconButton>
            </Tooltip>

            <Tooltip title="Share">
              <IconButton onClick={() => onShare?.(post.id)}>
                <Badge badgeContent={post.engagement?.shares || 0} color="success">
                  <Share />
                </Badge>
              </IconButton>
            </Tooltip>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="caption" color="text.secondary" sx={{ mr: 2 }}>
              {post.engagement?.purchases || 0} purchases
            </Typography>
            <Chip 
              label="🔥 Trending"
              size="small"
              color="warning"
              variant="outlined"
            />
          </Box>
        </CardActions>
      </Card>

      {/* Purchase Confirmation Dialog */}
      <Dialog open={purchaseDialog} onClose={() => setPurchaseDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ShoppingCart sx={{ mr: 1 }} />
            Confirm Purchase
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 1 }}>
              {productInfo.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Seller: {productInfo.seller?.name}
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="body2">
                  <strong>Unit Price:</strong> {formatPrice(productInfo.price, productInfo.currency)}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2">
                  <strong>Quantity:</strong> {quantity}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2">
                  <strong>Subtotal:</strong> {formatPrice(productInfo.price * quantity, productInfo.currency)}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2">
                  <strong>Shipping:</strong> TZS 5,000
                </Typography>
              </Grid>
            </Grid>
            
            <Divider sx={{ my: 2 }} />
            
            <Typography variant="h6" color="primary.main">
              <strong>Total: {formatPrice((productInfo.price * quantity) + 5000, productInfo.currency)}</strong>
            </Typography>
          </Box>

          <TextField
            fullWidth
            label="Delivery Address"
            multiline
            rows={3}
            placeholder="Enter your delivery address..."
            sx={{ mb: 2 }}
          />

          <TextField
            fullWidth
            label="Special Instructions (Optional)"
            multiline
            rows={2}
            placeholder="Any special delivery instructions..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPurchaseDialog(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handlePurchase}
            disabled={loading}
            startIcon={<MonetizationOn />}
          >
            {loading ? 'Processing...' : 'Confirm Purchase'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ProductPost;
