import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  BottomNavigation,
  BottomNavigationAction,
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Badge,
  Avatar,
  Paper,
} from '@mui/material';
import {
  Home as HomeIcon,
  Explore as ExploreIcon,
  Chat as ChatIcon,
  Person as PersonIcon,
  Notifications as NotificationsIcon,
  Search as SearchIcon,
  Add as AddIcon,
} from '@mui/icons-material';

// Hooks
import { useAuth } from '../../contexts/AuthContext';

const MobileLayout = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [value, setValue] = useState(getActiveTab(location.pathname));

  function getActiveTab(pathname) {
    if (pathname === '/' || pathname === '/home') return 0;
    if (pathname === '/explore') return 1;
    if (pathname === '/chat') return 2;
    if (pathname === '/profile') return 3;
    return 0;
  }

  const handleTabChange = (event, newValue) => {
    setValue(newValue);
    
    switch (newValue) {
      case 0:
        navigate('/home');
        break;
      case 1:
        navigate('/explore');
        break;
      case 2:
        navigate('/chat');
        break;
      case 3:
        navigate('/profile');
        break;
      default:
        navigate('/home');
    }
  };

  const getPageTitle = () => {
    switch (location.pathname) {
      case '/':
      case '/home':
        return 'ProChat';
      case '/explore':
        return 'Gundua';
      case '/chat':
        return 'Mazungumzo';
      case '/profile':
        return 'Wasifu';
      case '/events':
        return 'Matukio';
      case '/jobs':
        return 'Kazi';
      case '/news':
        return 'Habari';
      case '/live':
        return 'Mubashara';
      case '/propay':
        return 'ProPay';
      case '/prozone':
        return 'ProZone';
      default:
        return 'ProChat';
    }
  };

  return (
    <Box sx={{ 
      height: '100vh', 
      display: 'flex', 
      flexDirection: 'column',
      bgcolor: 'background.default',
    }}>
      {/* Top App Bar */}
      <AppBar 
        position="fixed" 
        sx={{ 
          zIndex: (theme) => theme.zIndex.drawer + 1,
          bgcolor: 'primary.main',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar sx={{ justifyContent: 'space-between', px: 2 }}>
          {/* Left Side - Title */}
          <Typography 
            variant="h6" 
            component="div" 
            sx={{ 
              fontWeight: 'bold',
              color: 'white',
            }}
          >
            {getPageTitle()}
          </Typography>

          {/* Right Side - Actions */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {/* Search Icon */}
            <IconButton 
              color="inherit" 
              onClick={() => navigate('/explore')}
              sx={{ color: 'white' }}
            >
              <SearchIcon />
            </IconButton>

            {/* Notifications */}
            <IconButton 
              color="inherit"
              sx={{ color: 'white' }}
            >
              <Badge badgeContent={3} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>

            {/* Add/Create Content */}
            <IconButton 
              color="inherit"
              sx={{ color: 'white' }}
            >
              <AddIcon />
            </IconButton>

            {/* Profile Avatar */}
            <IconButton 
              onClick={() => navigate('/profile')}
              sx={{ p: 0.5 }}
            >
              <Avatar 
                src={user?.profilePicture} 
                alt={user?.name}
                sx={{ width: 32, height: 32 }}
              >
                {user?.name?.charAt(0)}
              </Avatar>
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Main Content Area */}
      <Box 
        component="main"
        sx={{ 
          flexGrow: 1,
          mt: '64px', // AppBar height
          mb: '56px', // BottomNavigation height
          overflow: 'auto',
          bgcolor: 'background.default',
        }}
      >
        {children}
      </Box>

      {/* Bottom Navigation */}
      <Paper 
        sx={{ 
          position: 'fixed', 
          bottom: 0, 
          left: 0, 
          right: 0,
          zIndex: (theme) => theme.zIndex.drawer + 1,
          borderTop: '1px solid',
          borderColor: 'divider',
        }} 
        elevation={3}
      >
        <BottomNavigation
          value={value}
          onChange={handleTabChange}
          showLabels
          sx={{
            height: 56,
            '& .MuiBottomNavigationAction-root': {
              minWidth: 'auto',
              padding: '6px 12px',
            },
            '& .MuiBottomNavigationAction-label': {
              fontSize: '0.75rem',
              fontWeight: 500,
            },
          }}
        >
          <BottomNavigationAction 
            label="Nyumbani" 
            icon={<HomeIcon />}
            sx={{
              color: value === 0 ? 'primary.main' : 'text.secondary',
            }}
          />
          <BottomNavigationAction 
            label="Gundua" 
            icon={<ExploreIcon />}
            sx={{
              color: value === 1 ? 'primary.main' : 'text.secondary',
            }}
          />
          <BottomNavigationAction 
            label="Mazungumzo" 
            icon={
              <Badge badgeContent={5} color="error" variant="dot">
                <ChatIcon />
              </Badge>
            }
            sx={{
              color: value === 2 ? 'primary.main' : 'text.secondary',
            }}
          />
          <BottomNavigationAction 
            label="Wasifu" 
            icon={<PersonIcon />}
            sx={{
              color: value === 3 ? 'primary.main' : 'text.secondary',
            }}
          />
        </BottomNavigation>
      </Paper>
    </Box>
  );
};

export default MobileLayout;
