{"version": 3, "sources": ["../../../src/utils/strings.ts"], "sourcesContent": ["/**\n * Joins strings with commas and 'and', based on English rules, limiting the number of items enumerated to keep from filling the console.\n * @param items strings to join\n * @param limit max number of strings to enumerate before using 'others'\n * @returns joined string\n */\nexport function joinWithCommasAnd(items: string[], limit: number | undefined = 10): string {\n  if (!items.length) {\n    return '';\n  }\n\n  const uniqueItems = items.filter((value, index, array) => array.indexOf(value) === index);\n\n  if (uniqueItems.length === 1) {\n    return uniqueItems[0];\n  }\n\n  if (limit && uniqueItems.length > limit) {\n    const first = uniqueItems.slice(0, limit);\n    const remaining = uniqueItems.length - limit;\n    return `${first.join(', ')}, and ${remaining} ${remaining > 1 ? 'others' : 'other'}`;\n  }\n\n  const last = uniqueItems.pop();\n  return `${uniqueItems.join(', ')}${uniqueItems.length >= 2 ? ',' : ''} and ${last}`;\n}\n"], "names": ["joinWithCommasAnd", "items", "limit", "length", "uniqueItems", "filter", "value", "index", "array", "indexOf", "first", "slice", "remaining", "join", "last", "pop"], "mappings": "AAAA;;;;;CAKC;;;;+BACeA;;;eAAAA;;;AAAT,SAASA,kBAAkBC,KAAe,EAAEC,QAA4B,EAAE;IAC/E,IAAI,CAACD,MAAME,MAAM,EAAE;QACjB,OAAO;IACT;IAEA,MAAMC,cAAcH,MAAMI,MAAM,CAAC,CAACC,OAAOC,OAAOC,QAAUA,MAAMC,OAAO,CAACH,WAAWC;IAEnF,IAAIH,YAAYD,MAAM,KAAK,GAAG;QAC5B,OAAOC,WAAW,CAAC,EAAE;IACvB;IAEA,IAAIF,SAASE,YAAYD,MAAM,GAAGD,OAAO;QACvC,MAAMQ,QAAQN,YAAYO,KAAK,CAAC,GAAGT;QACnC,MAAMU,YAAYR,YAAYD,MAAM,GAAGD;QACvC,OAAO,GAAGQ,MAAMG,IAAI,CAAC,MAAM,MAAM,EAAED,UAAU,CAAC,EAAEA,YAAY,IAAI,WAAW,SAAS;IACtF;IAEA,MAAME,OAAOV,YAAYW,GAAG;IAC5B,OAAO,GAAGX,YAAYS,IAAI,CAAC,QAAQT,YAAYD,MAAM,IAAI,IAAI,MAAM,GAAG,KAAK,EAAEW,MAAM;AACrF"}