// ProChat Tabs Container Component
// Main tabs navigation container for web app

import React, { useState, useEffect } from 'react';
import {
  Box,
  BottomNavigation,
  BottomNavigationAction,
  Paper,
  useTheme,
  useMediaQuery,
  Badge,
} from '@mui/material';
import {
  Home,
  Chat,
  Explore,
  Person,
  HomeOutlined,
  ChatOutlined,
  ExploreOutlined,
  PersonOutlined,
} from '@mui/icons-material';

// Import tab components
import HomeTab from './HomeTab';
import ChatsTab from './ChatsTab';
import DiscoverTab from './DiscoverTab';
import MeTab from './MeTab';

const TabsContainer = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [unreadChats, setUnreadChats] = useState(3);
  const [notifications, setNotifications] = useState(5);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Determine active tab based on current URL
  useEffect(() => {
    const path = window.location.pathname;
    if (path === '/' || path === '/home') {
      setActiveTab(0);
    } else if (path === '/chats') {
      setActiveTab(1);
    } else if (path === '/discover') {
      setActiveTab(2);
    } else if (path === '/me') {
      setActiveTab(3);
    }
  }, []);

  // Tab configuration
  const tabs = [
    {
      label: 'Nyumbani',
      icon: <HomeOutlined />,
      activeIcon: <Home />,
      component: <HomeTab />,
      badge: 0,
    },
    {
      label: 'Mazungumzo',
      icon: <ChatOutlined />,
      activeIcon: <Chat />,
      component: <ChatsTab />,
      badge: unreadChats,
    },
    {
      label: 'Gundua',
      icon: <ExploreOutlined />,
      activeIcon: <Explore />,
      component: <DiscoverTab />,
      badge: 0,
    },
    {
      label: 'Mimi',
      icon: <PersonOutlined />,
      activeIcon: <Person />,
      component: <MeTab />,
      badge: notifications,
    },
  ];

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);

    // Navigate to corresponding route
    const routes = ['/', '/chats', '/discover', '/me'];
    const route = routes[newValue];
    if (route) {
      window.history.pushState({}, '', route);
    }
  };

  // Mock function to update unread counts
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate receiving new messages/notifications
      if (Math.random() > 0.8) {
        setUnreadChats(prev => prev + 1);
      }
      if (Math.random() > 0.9) {
        setNotifications(prev => prev + 1);
      }
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  const renderTabContent = () => {
    return tabs[activeTab]?.component || <HomeTab />;
  };

  const renderBottomNavigation = () => (
    <Paper 
      sx={{ 
        position: 'fixed', 
        bottom: 0, 
        left: 0, 
        right: 0, 
        zIndex: 1000,
        borderTop: 1,
        borderColor: 'divider',
      }} 
      elevation={3}
    >
      <BottomNavigation
        value={activeTab}
        onChange={handleTabChange}
        sx={{
          height: 70,
          '& .MuiBottomNavigationAction-root': {
            minWidth: 'auto',
            padding: '6px 12px 8px',
            '&.Mui-selected': {
              color: 'primary.main',
            },
          },
        }}
      >
        {tabs.map((tab, index) => (
          <BottomNavigationAction
            key={index}
            label={tab.label}
            icon={
              tab.badge > 0 ? (
                <Badge badgeContent={tab.badge} color="error">
                  {activeTab === index ? tab.activeIcon : tab.icon}
                </Badge>
              ) : (
                activeTab === index ? tab.activeIcon : tab.icon
              )
            }
            sx={{
              '& .MuiBottomNavigationAction-label': {
                fontSize: '0.75rem',
                fontWeight: activeTab === index ? 600 : 400,
              },
            }}
          />
        ))}
      </BottomNavigation>
    </Paper>
  );

  const renderSideNavigation = () => (
    <Box
      sx={{
        position: 'fixed',
        left: 0,
        top: 0,
        bottom: 0,
        width: 240,
        bgcolor: 'background.paper',
        borderRight: 1,
        borderColor: 'divider',
        zIndex: 1000,
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* Logo */}
      <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: 2,
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold',
              fontSize: '1.2rem',
              mr: 2,
            }}
          >
            P
          </Box>
          <Box>
            <Box sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>ProChat</Box>
            <Box sx={{ fontSize: '0.8rem', color: 'text.secondary' }}>Super App</Box>
          </Box>
        </Box>
      </Box>

      {/* Navigation Items */}
      <Box sx={{ flex: 1, py: 2 }}>
        {tabs.map((tab, index) => (
          <Box
            key={index}
            onClick={() => setActiveTab(index)}
            sx={{
              display: 'flex',
              alignItems: 'center',
              px: 3,
              py: 2,
              cursor: 'pointer',
              bgcolor: activeTab === index ? 'primary.light' : 'transparent',
              color: activeTab === index ? 'primary.main' : 'text.primary',
              borderRight: activeTab === index ? 3 : 0,
              borderColor: 'primary.main',
              '&:hover': {
                bgcolor: activeTab === index ? 'primary.light' : 'action.hover',
              },
            }}
          >
            <Box sx={{ mr: 2 }}>
              {tab.badge > 0 ? (
                <Badge badgeContent={tab.badge} color="error">
                  {activeTab === index ? tab.activeIcon : tab.icon}
                </Badge>
              ) : (
                activeTab === index ? tab.activeIcon : tab.icon
              )}
            </Box>
            <Box sx={{ fontWeight: activeTab === index ? 600 : 400 }}>
              {tab.label}
            </Box>
          </Box>
        ))}
      </Box>

      {/* User Info */}
      <Box sx={{ p: 3, borderTop: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box
            sx={{
              width: 32,
              height: 32,
              borderRadius: '50%',
              bgcolor: 'primary.main',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '0.9rem',
              mr: 2,
            }}
          >
            JM
          </Box>
          <Box>
            <Box sx={{ fontSize: '0.9rem', fontWeight: 500 }}>John Mwangi</Box>
            <Box sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>@johnmwangi</Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
      {/* Side Navigation for Desktop */}
      {!isMobile && renderSideNavigation()}

      {/* Main Content */}
      <Box
        sx={{
          flex: 1,
          marginLeft: isMobile ? 0 : '240px',
          marginBottom: isMobile ? '70px' : 0,
          overflow: 'auto',
          bgcolor: 'background.default',
        }}
      >
        {renderTabContent()}
      </Box>

      {/* Bottom Navigation for Mobile */}
      {isMobile && renderBottomNavigation()}
    </Box>
  );
};

export default TabsContainer;
