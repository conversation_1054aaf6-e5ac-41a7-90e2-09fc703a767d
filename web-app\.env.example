# ProChat Payment Gateway Environment Variables
# Copy this file to .env and fill in your actual credentials

# ===========================================
# GENERAL CONFIGURATION
# ===========================================
NODE_ENV=development
REACT_APP_API_URL=http://localhost:3001/api
WEBHOOK_BASE_URL=https://api.prochat.co.tz/webhooks

# ===========================================
# M-PESA CONFIGURATION
# ===========================================
MPESA_API_KEY=your_mpesa_api_key_here
MPESA_PUBLIC_KEY=your_mpesa_public_key_here
MPESA_SERVICE_PROVIDER_CODE=171717
MPESA_INITIATOR_IDENTIFIER=your_initiator_identifier
MPESA_SECURITY_CREDENTIAL=your_security_credential

# ===========================================
# AIRTEL MONEY CONFIGURATION
# ===========================================
AIRTEL_CLIENT_ID=your_airtel_client_id_here
AIRTEL_CLIENT_SECRET=your_airtel_client_secret_here
AIRTEL_API_KEY=your_airtel_api_key_here

# ===========================================
# TIGO PESA (MIXX BY YAS) CONFIGURATION
# ===========================================
TIGO_USERNAME=your_tigo_username_here
TIGO_PASSWORD=your_tigo_password_here
TIGO_API_KEY=your_tigo_api_key_here
TIGO_BRAND_ID=your_tigo_brand_id
TIGO_ACCOUNT_MSISDN=your_tigo_account_msisdn

# ===========================================
# HALOPESA CONFIGURATION
# ===========================================
HALOPESA_MERCHANT_ID=your_halopesa_merchant_id_here
HALOPESA_API_KEY=your_halopesa_api_key_here
HALOPESA_SECRET_KEY=your_halopesa_secret_key_here
HALOPESA_CALLBACK_URL=https://api.prochat.co.tz/webhooks/halopesa

# ===========================================
# AZAMPESA CONFIGURATION
# ===========================================
AZAMPESA_MERCHANT_CODE=your_azampesa_merchant_code_here
AZAMPESA_API_KEY=your_azampesa_api_key_here
AZAMPESA_SECRET_KEY=your_azampesa_secret_key_here
AZAMPESA_CALLBACK_URL=https://api.prochat.co.tz/webhooks/azampesa

# ===========================================
# NARA CONFIGURATION
# ===========================================
NARA_CLIENT_ID=your_nara_client_id_here
NARA_CLIENT_SECRET=your_nara_client_secret_here
NARA_API_KEY=your_nara_api_key_here
NARA_MERCHANT_ID=your_nara_merchant_id

# ===========================================
# CRDB BANK CONFIGURATION
# ===========================================
CRDB_BANK_CODE=your_crdb_bank_code_here
CRDB_API_KEY=your_crdb_api_key_here
CRDB_SECRET_KEY=your_crdb_secret_key_here
CRDB_MERCHANT_ID=your_crdb_merchant_id
CRDB_ACCOUNT_NUMBER=your_crdb_account_number

# ===========================================
# NMB BANK CONFIGURATION
# ===========================================
NMB_BANK_CODE=your_nmb_bank_code_here
NMB_API_KEY=your_nmb_api_key_here
NMB_SECRET_KEY=your_nmb_secret_key_here
NMB_MERCHANT_ID=your_nmb_merchant_id
NMB_ACCOUNT_NUMBER=your_nmb_account_number

# ===========================================
# NBC BANK CONFIGURATION
# ===========================================
NBC_BANK_CODE=your_nbc_bank_code_here
NBC_API_KEY=your_nbc_api_key_here
NBC_SECRET_KEY=your_nbc_secret_key_here
NBC_MERCHANT_ID=your_nbc_merchant_id
NBC_ACCOUNT_NUMBER=your_nbc_account_number

# ===========================================
# VISA CONFIGURATION
# ===========================================
VISA_USER_ID=your_visa_user_id_here
VISA_PASSWORD=your_visa_password_here
VISA_KEY_ID=your_visa_key_id_here
VISA_SHARED_SECRET=your_visa_shared_secret_here
VISA_CERTIFICATE_PATH=./certificates/visa_cert.pem
VISA_PRIVATE_KEY_PATH=./certificates/visa_private_key.pem

# ===========================================
# MASTERCARD CONFIGURATION
# ===========================================
MASTERCARD_CONSUMER_KEY=your_mastercard_consumer_key_here
MASTERCARD_PRIVATE_KEY=your_mastercard_private_key_here
MASTERCARD_KEY_ALIAS=your_mastercard_key_alias_here
MASTERCARD_KEY_PASSWORD=your_mastercard_key_password_here
MASTERCARD_CERTIFICATE_PATH=./certificates/mastercard_cert.p12

# ===========================================
# STRIPE CONFIGURATION
# ===========================================
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here
STRIPE_CONNECT_CLIENT_ID=ca_your_stripe_connect_client_id

# ===========================================
# CRYPTOCURRENCY CONFIGURATION
# ===========================================
# Bitcoin
BLOCKCHAIN_API_KEY=your_blockchain_api_key_here
BITCOIN_WALLET_ID=your_bitcoin_wallet_id_here
BITCOIN_WALLET_PASSWORD=your_bitcoin_wallet_password_here

# USDT (Ethereum)
ETHERSCAN_API_KEY=your_etherscan_api_key_here
USDT_WALLET_ADDRESS=your_usdt_wallet_address_here
USDT_PRIVATE_KEY=your_usdt_private_key_here

# ===========================================
# SECURITY CONFIGURATION
# ===========================================
JWT_SECRET=your_super_secret_jwt_key_here
ENCRYPTION_KEY=your_32_character_encryption_key_here
API_RATE_LIMIT_WINDOW_MS=900000
API_RATE_LIMIT_MAX_REQUESTS=100

# ===========================================
# DATABASE CONFIGURATION
# ===========================================
DATABASE_URL=mongodb://localhost:27017/prochat_payments
REDIS_URL=redis://localhost:6379

# ===========================================
# LOGGING AND MONITORING
# ===========================================
LOG_LEVEL=info
SENTRY_DSN=your_sentry_dsn_here
DATADOG_API_KEY=your_datadog_api_key_here

# ===========================================
# EMAIL CONFIGURATION (for notifications)
# ===========================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password_here

# ===========================================
# SMS CONFIGURATION (for notifications)
# ===========================================
SMS_API_KEY=your_sms_api_key_here
SMS_SENDER_ID=PROCHAT

# ===========================================
# SLACK CONFIGURATION (for alerts)
# ===========================================
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook

# ===========================================
# COMPLIANCE AND REPORTING
# ===========================================
KYC_API_KEY=your_kyc_provider_api_key_here
AML_API_KEY=your_aml_provider_api_key_here
SANCTIONS_API_KEY=your_sanctions_screening_api_key_here

# ===========================================
# BACKUP AND RECOVERY
# ===========================================
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_S3_BUCKET=prochat-payment-backups
AWS_REGION=us-east-1

# ===========================================
# DEVELOPMENT SETTINGS
# ===========================================
# Set to true to enable sandbox/test mode for all providers
ENABLE_SANDBOX_MODE=true

# Set to true to enable detailed logging
ENABLE_DEBUG_LOGGING=true

# Set to true to skip signature verification (DEVELOPMENT ONLY!)
SKIP_SIGNATURE_VERIFICATION=false

# ===========================================
# PRODUCTION SETTINGS
# ===========================================
# These should only be set in production
# FORCE_HTTPS=true
# ENABLE_CORS=false
# ENABLE_RATE_LIMITING=true
# ENABLE_IP_WHITELISTING=true

# ===========================================
# WEBHOOK SECURITY
# ===========================================
# IP addresses allowed to send webhooks (comma-separated)
WEBHOOK_ALLOWED_IPS=*************/24,*********/24,***********/16

# ===========================================
# FRAUD DETECTION
# ===========================================
FRAUD_DETECTION_ENABLED=true
MAX_AMOUNT_PER_TRANSACTION=10000000
MAX_TRANSACTIONS_PER_DAY=50
MAX_AMOUNT_PER_DAY=50000000

# ===========================================
# BUSINESS CONFIGURATION
# ===========================================
BUSINESS_NAME=ProChat Tanzania
BUSINESS_EMAIL=<EMAIL>
BUSINESS_PHONE=+255123456789
BUSINESS_ADDRESS=Dar es Salaam, Tanzania
BUSINESS_LICENSE=your_business_license_number

# ===========================================
# SUPPORT CONFIGURATION
# ===========================================
SUPPORT_EMAIL=<EMAIL>
SUPPORT_PHONE=+255987654321
SUPPORT_HOURS=24/7

# ===========================================
# TERMS AND CONDITIONS
# ===========================================
TERMS_URL=https://prochat.co.tz/terms
PRIVACY_URL=https://prochat.co.tz/privacy
REFUND_POLICY_URL=https://prochat.co.tz/refund-policy
