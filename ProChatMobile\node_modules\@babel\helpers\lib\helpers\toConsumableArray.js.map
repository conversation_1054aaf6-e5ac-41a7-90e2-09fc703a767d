{"version": 3, "names": ["_arrayWithoutHoles", "require", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "_toConsumableArray", "arr", "arrayWithoutHoles", "iterableToArray", "unsupportedIterableToArray", "nonIterableSpread"], "sources": ["../../src/helpers/toConsumableArray.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport arrayWithoutHoles from \"./arrayWithoutHoles.ts\";\nimport iterableToArray from \"./iterableToArray.ts\";\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.ts\";\n// @ts-expect-error nonIterableSpread is still being converted to TS.\nimport nonIterableSpread from \"./nonIterableSpread.ts\";\n\nexport default function _toConsumableArray<T>(arr: any): T[] {\n  return (\n    arrayWithoutHoles<T>(arr) ||\n    iterableToArray<T>(arr) ||\n    unsupportedIterableToArray<T>(arr) ||\n    nonIterableSpread()\n  );\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AACA,IAAAE,2BAAA,GAAAF,OAAA;AAEA,IAAAG,kBAAA,GAAAH,OAAA;AAEe,SAASI,kBAAkBA,CAAIC,GAAQ,EAAO;EAC3D,OACE,IAAAC,0BAAiB,EAAID,GAAG,CAAC,IACzB,IAAAE,wBAAe,EAAIF,GAAG,CAAC,IACvB,IAAAG,mCAA0B,EAAIH,GAAG,CAAC,IAClC,IAAAI,0BAAiB,EAAC,CAAC;AAEvB", "ignoreList": []}