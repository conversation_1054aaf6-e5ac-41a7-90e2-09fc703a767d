const set = require('regenerate')(0x5BF, 0x5C7, 0x670, 0x711, 0x7FD, 0x9BC, 0x9D7, 0x9FE, 0xA3C, 0xA51, 0xA75, 0xABC, 0xB3C, 0xB82, 0xBD7, 0xC3C, 0xCBC, 0xCF3, 0xD57, 0xDCA, 0xDD6, 0xE31, 0xEB1, 0xF35, 0xF37, 0xF39, 0xFC6, 0x108F, 0x17DD, 0x180F, 0x18A9, 0x1A7F, 0x1CED, 0x1CF4, 0x2D7F, 0xA802, 0xA806, 0xA80B, 0xA82C, 0xA8FF, 0xA9E5, 0xAA43, 0xAAB0, 0xAAC1, 0xFB1E, 0x101FD, 0x102E0, 0x10A3F, 0x11070, 0x110C2, 0x11173, 0x1123E, 0x11241, 0x11357, 0x113C2, 0x113C5, 0x113D2, 0x1145E, 0x11940, 0x119E4, 0x11A47, 0x11D3A, 0x11D47, 0x11F03, 0x11F5A, 0x13440, 0x16F4F, 0x16FE4, 0x1DA75, 0x1DA84, 0x1E08F, 0x1E2AE);
set.addRange(0x300, 0x36F).addRange(0x483, 0x489).addRange(0x591, 0x5BD).addRange(0x5C1, 0x5C2).addRange(0x5C4, 0x5C5).addRange(0x610, 0x61A).addRange(0x64B, 0x65F).addRange(0x6D6, 0x6DC).addRange(0x6DF, 0x6E4).addRange(0x6E7, 0x6E8).addRange(0x6EA, 0x6ED).addRange(0x730, 0x74A).addRange(0x7A6, 0x7B0).addRange(0x7EB, 0x7F3).addRange(0x816, 0x819).addRange(0x81B, 0x823).addRange(0x825, 0x827).addRange(0x829, 0x82D).addRange(0x859, 0x85B).addRange(0x897, 0x89F).addRange(0x8CA, 0x8E1).addRange(0x8E3, 0x903).addRange(0x93A, 0x93C).addRange(0x93E, 0x94F).addRange(0x951, 0x957).addRange(0x962, 0x963).addRange(0x981, 0x983).addRange(0x9BE, 0x9C4).addRange(0x9C7, 0x9C8).addRange(0x9CB, 0x9CD).addRange(0x9E2, 0x9E3).addRange(0xA01, 0xA03).addRange(0xA3E, 0xA42).addRange(0xA47, 0xA48).addRange(0xA4B, 0xA4D).addRange(0xA70, 0xA71).addRange(0xA81, 0xA83).addRange(0xABE, 0xAC5).addRange(0xAC7, 0xAC9).addRange(0xACB, 0xACD).addRange(0xAE2, 0xAE3).addRange(0xAFA, 0xAFF).addRange(0xB01, 0xB03).addRange(0xB3E, 0xB44).addRange(0xB47, 0xB48).addRange(0xB4B, 0xB4D).addRange(0xB55, 0xB57).addRange(0xB62, 0xB63).addRange(0xBBE, 0xBC2).addRange(0xBC6, 0xBC8).addRange(0xBCA, 0xBCD);
set.addRange(0xC00, 0xC04).addRange(0xC3E, 0xC44).addRange(0xC46, 0xC48).addRange(0xC4A, 0xC4D).addRange(0xC55, 0xC56).addRange(0xC62, 0xC63).addRange(0xC81, 0xC83).addRange(0xCBE, 0xCC4).addRange(0xCC6, 0xCC8).addRange(0xCCA, 0xCCD).addRange(0xCD5, 0xCD6).addRange(0xCE2, 0xCE3).addRange(0xD00, 0xD03).addRange(0xD3B, 0xD3C).addRange(0xD3E, 0xD44).addRange(0xD46, 0xD48).addRange(0xD4A, 0xD4D).addRange(0xD62, 0xD63).addRange(0xD81, 0xD83).addRange(0xDCF, 0xDD4).addRange(0xDD8, 0xDDF).addRange(0xDF2, 0xDF3).addRange(0xE34, 0xE3A).addRange(0xE47, 0xE4E).addRange(0xEB4, 0xEBC).addRange(0xEC8, 0xECE).addRange(0xF18, 0xF19).addRange(0xF3E, 0xF3F).addRange(0xF71, 0xF84).addRange(0xF86, 0xF87).addRange(0xF8D, 0xF97).addRange(0xF99, 0xFBC).addRange(0x102B, 0x103E).addRange(0x1056, 0x1059).addRange(0x105E, 0x1060).addRange(0x1062, 0x1064).addRange(0x1067, 0x106D).addRange(0x1071, 0x1074).addRange(0x1082, 0x108D).addRange(0x109A, 0x109D).addRange(0x135D, 0x135F).addRange(0x1712, 0x1715).addRange(0x1732, 0x1734).addRange(0x1752, 0x1753).addRange(0x1772, 0x1773).addRange(0x17B4, 0x17D3).addRange(0x180B, 0x180D).addRange(0x1885, 0x1886).addRange(0x1920, 0x192B).addRange(0x1930, 0x193B).addRange(0x1A17, 0x1A1B);
set.addRange(0x1A55, 0x1A5E).addRange(0x1A60, 0x1A7C).addRange(0x1AB0, 0x1ACE).addRange(0x1B00, 0x1B04).addRange(0x1B34, 0x1B44).addRange(0x1B6B, 0x1B73).addRange(0x1B80, 0x1B82).addRange(0x1BA1, 0x1BAD).addRange(0x1BE6, 0x1BF3).addRange(0x1C24, 0x1C37).addRange(0x1CD0, 0x1CD2).addRange(0x1CD4, 0x1CE8).addRange(0x1CF7, 0x1CF9).addRange(0x1DC0, 0x1DFF).addRange(0x20D0, 0x20F0).addRange(0x2CEF, 0x2CF1).addRange(0x2DE0, 0x2DFF).addRange(0x302A, 0x302F).addRange(0x3099, 0x309A).addRange(0xA66F, 0xA672).addRange(0xA674, 0xA67D).addRange(0xA69E, 0xA69F).addRange(0xA6F0, 0xA6F1).addRange(0xA823, 0xA827).addRange(0xA880, 0xA881).addRange(0xA8B4, 0xA8C5).addRange(0xA8E0, 0xA8F1).addRange(0xA926, 0xA92D).addRange(0xA947, 0xA953).addRange(0xA980, 0xA983).addRange(0xA9B3, 0xA9C0).addRange(0xAA29, 0xAA36).addRange(0xAA4C, 0xAA4D).addRange(0xAA7B, 0xAA7D).addRange(0xAAB2, 0xAAB4).addRange(0xAAB7, 0xAAB8).addRange(0xAABE, 0xAABF).addRange(0xAAEB, 0xAAEF).addRange(0xAAF5, 0xAAF6).addRange(0xABE3, 0xABEA).addRange(0xABEC, 0xABED).addRange(0xFE00, 0xFE0F).addRange(0xFE20, 0xFE2F).addRange(0x10376, 0x1037A).addRange(0x10A01, 0x10A03).addRange(0x10A05, 0x10A06).addRange(0x10A0C, 0x10A0F).addRange(0x10A38, 0x10A3A).addRange(0x10AE5, 0x10AE6).addRange(0x10D24, 0x10D27).addRange(0x10D69, 0x10D6D);
set.addRange(0x10EAB, 0x10EAC).addRange(0x10EFC, 0x10EFF).addRange(0x10F46, 0x10F50).addRange(0x10F82, 0x10F85).addRange(0x11000, 0x11002).addRange(0x11038, 0x11046).addRange(0x11073, 0x11074).addRange(0x1107F, 0x11082).addRange(0x110B0, 0x110BA).addRange(0x11100, 0x11102).addRange(0x11127, 0x11134).addRange(0x11145, 0x11146).addRange(0x11180, 0x11182).addRange(0x111B3, 0x111C0).addRange(0x111C9, 0x111CC).addRange(0x111CE, 0x111CF).addRange(0x1122C, 0x11237).addRange(0x112DF, 0x112EA).addRange(0x11300, 0x11303).addRange(0x1133B, 0x1133C).addRange(0x1133E, 0x11344).addRange(0x11347, 0x11348).addRange(0x1134B, 0x1134D).addRange(0x11362, 0x11363).addRange(0x11366, 0x1136C).addRange(0x11370, 0x11374).addRange(0x113B8, 0x113C0).addRange(0x113C7, 0x113CA).addRange(0x113CC, 0x113D0).addRange(0x113E1, 0x113E2).addRange(0x11435, 0x11446).addRange(0x114B0, 0x114C3).addRange(0x115AF, 0x115B5).addRange(0x115B8, 0x115C0).addRange(0x115DC, 0x115DD).addRange(0x11630, 0x11640).addRange(0x116AB, 0x116B7).addRange(0x1171D, 0x1172B).addRange(0x1182C, 0x1183A).addRange(0x11930, 0x11935).addRange(0x11937, 0x11938).addRange(0x1193B, 0x1193E).addRange(0x11942, 0x11943).addRange(0x119D1, 0x119D7).addRange(0x119DA, 0x119E0).addRange(0x11A01, 0x11A0A).addRange(0x11A33, 0x11A39).addRange(0x11A3B, 0x11A3E).addRange(0x11A51, 0x11A5B).addRange(0x11A8A, 0x11A99).addRange(0x11C2F, 0x11C36);
set.addRange(0x11C38, 0x11C3F).addRange(0x11C92, 0x11CA7).addRange(0x11CA9, 0x11CB6).addRange(0x11D31, 0x11D36).addRange(0x11D3C, 0x11D3D).addRange(0x11D3F, 0x11D45).addRange(0x11D8A, 0x11D8E).addRange(0x11D90, 0x11D91).addRange(0x11D93, 0x11D97).addRange(0x11EF3, 0x11EF6).addRange(0x11F00, 0x11F01).addRange(0x11F34, 0x11F3A).addRange(0x11F3E, 0x11F42).addRange(0x13447, 0x13455).addRange(0x1611E, 0x1612F).addRange(0x16AF0, 0x16AF4).addRange(0x16B30, 0x16B36).addRange(0x16F51, 0x16F87).addRange(0x16F8F, 0x16F92).addRange(0x16FF0, 0x16FF1).addRange(0x1BC9D, 0x1BC9E).addRange(0x1CF00, 0x1CF2D).addRange(0x1CF30, 0x1CF46).addRange(0x1D165, 0x1D169).addRange(0x1D16D, 0x1D172).addRange(0x1D17B, 0x1D182).addRange(0x1D185, 0x1D18B).addRange(0x1D1AA, 0x1D1AD).addRange(0x1D242, 0x1D244).addRange(0x1DA00, 0x1DA36).addRange(0x1DA3B, 0x1DA6C).addRange(0x1DA9B, 0x1DA9F).addRange(0x1DAA1, 0x1DAAF).addRange(0x1E000, 0x1E006).addRange(0x1E008, 0x1E018).addRange(0x1E01B, 0x1E021).addRange(0x1E023, 0x1E024).addRange(0x1E026, 0x1E02A).addRange(0x1E130, 0x1E136).addRange(0x1E2EC, 0x1E2EF).addRange(0x1E4EC, 0x1E4EF).addRange(0x1E5EE, 0x1E5EF).addRange(0x1E8D0, 0x1E8D6).addRange(0x1E944, 0x1E94A).addRange(0xE0100, 0xE01EF);
exports.characters = set;
