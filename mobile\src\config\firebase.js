// Firebase Configuration for ProChat Mobile
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getMessaging, isSupported } from 'firebase/messaging';
import { Platform } from 'react-native';

// Firebase configuration for different platforms
const firebaseConfig = Platform.select({
  web: {
    apiKey: "AIzaSyBIolLq1VuFHaFNoxHYvJqfsGe6vmygSQk",
    authDomain: "prochat-7ff37.firebaseapp.com",
    projectId: "prochat-7ff37",
    storageBucket: "prochat-7ff37.appspot.com",
    messagingSenderId: "725026416309",
    appId: "1:725026416309:web:732e5b4c9b9cdb93de80fc",
    measurementId: "G-9THG0PXHC1"
  },
  ios: {
    apiKey: "AIzaSyCtB34EKRskmxEfN7YORp_uEzqWp5DrAKw",
    authDomain: "prochat-7ff37.firebaseapp.com",
    projectId: "prochat-7ff37",
    storageBucket: "prochat-7ff37.appspot.com",
    messagingSenderId: "725026416309",
    appId: "1:725026416309:ios:85bae917f367b024de80fc"
  },
  android: {
    apiKey: "AIzaSyCAQvYg0-s-obt067u0nFdv5I_wwYHnO4o",
    authDomain: "prochat-7ff37.firebaseapp.com",
    projectId: "prochat-7ff37",
    storageBucket: "prochat-7ff37.appspot.com",
    messagingSenderId: "725026416309",
    appId: "1:725026416309:android:732e5b4c9b9cdb93de80fc"
  }
});

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Initialize messaging for web only
export let messaging = null;
if (Platform.OS === 'web') {
  isSupported().then((supported) => {
    if (supported) {
      messaging = getMessaging(app);
    }
  });
}

export default app;
