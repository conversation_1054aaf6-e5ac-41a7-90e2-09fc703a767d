/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

// @generated by enums.py
// clang-format off
#include <yoga/YGEnums.h>

const char* YGAlignToString(const YGAlign value) {
  switch (value) {
    case YGAlignAuto:
      return "auto";
    case YGAlignFlexStart:
      return "flex-start";
    case YGAlignCenter:
      return "center";
    case YGAlignFlexEnd:
      return "flex-end";
    case YGAlignStretch:
      return "stretch";
    case YGAlignBaseline:
      return "baseline";
    case YGAlignSpaceBetween:
      return "space-between";
    case YGAlignSpaceAround:
      return "space-around";
    case YGAlignSpaceEvenly:
      return "space-evenly";
  }
  return "unknown";
}

const char* YGBoxSizingToString(const YGBoxSizing value) {
  switch (value) {
    case YGBoxSizingBorderBox:
      return "border-box";
    case YGBoxSizingContentBox:
      return "content-box";
  }
  return "unknown";
}

const char* YGDimensionToString(const YGDimension value) {
  switch (value) {
    case YGDimensionWidth:
      return "width";
    case YGDimensionHeight:
      return "height";
  }
  return "unknown";
}

const char* YGDirectionToString(const YGDirection value) {
  switch (value) {
    case YGDirectionInherit:
      return "inherit";
    case YGDirectionLTR:
      return "ltr";
    case YGDirectionRTL:
      return "rtl";
  }
  return "unknown";
}

const char* YGDisplayToString(const YGDisplay value) {
  switch (value) {
    case YGDisplayFlex:
      return "flex";
    case YGDisplayNone:
      return "none";
    case YGDisplayContents:
      return "contents";
  }
  return "unknown";
}

const char* YGEdgeToString(const YGEdge value) {
  switch (value) {
    case YGEdgeLeft:
      return "left";
    case YGEdgeTop:
      return "top";
    case YGEdgeRight:
      return "right";
    case YGEdgeBottom:
      return "bottom";
    case YGEdgeStart:
      return "start";
    case YGEdgeEnd:
      return "end";
    case YGEdgeHorizontal:
      return "horizontal";
    case YGEdgeVertical:
      return "vertical";
    case YGEdgeAll:
      return "all";
  }
  return "unknown";
}

const char* YGErrataToString(const YGErrata value) {
  switch (value) {
    case YGErrataNone:
      return "none";
    case YGErrataStretchFlexBasis:
      return "stretch-flex-basis";
    case YGErrataAbsolutePositionWithoutInsetsExcludesPadding:
      return "absolute-position-without-insets-excludes-padding";
    case YGErrataAbsolutePercentAgainstInnerSize:
      return "absolute-percent-against-inner-size";
    case YGErrataAll:
      return "all";
    case YGErrataClassic:
      return "classic";
  }
  return "unknown";
}

const char* YGExperimentalFeatureToString(const YGExperimentalFeature value) {
  switch (value) {
    case YGExperimentalFeatureWebFlexBasis:
      return "web-flex-basis";
  }
  return "unknown";
}

const char* YGFlexDirectionToString(const YGFlexDirection value) {
  switch (value) {
    case YGFlexDirectionColumn:
      return "column";
    case YGFlexDirectionColumnReverse:
      return "column-reverse";
    case YGFlexDirectionRow:
      return "row";
    case YGFlexDirectionRowReverse:
      return "row-reverse";
  }
  return "unknown";
}

const char* YGGutterToString(const YGGutter value) {
  switch (value) {
    case YGGutterColumn:
      return "column";
    case YGGutterRow:
      return "row";
    case YGGutterAll:
      return "all";
  }
  return "unknown";
}

const char* YGJustifyToString(const YGJustify value) {
  switch (value) {
    case YGJustifyFlexStart:
      return "flex-start";
    case YGJustifyCenter:
      return "center";
    case YGJustifyFlexEnd:
      return "flex-end";
    case YGJustifySpaceBetween:
      return "space-between";
    case YGJustifySpaceAround:
      return "space-around";
    case YGJustifySpaceEvenly:
      return "space-evenly";
  }
  return "unknown";
}

const char* YGLogLevelToString(const YGLogLevel value) {
  switch (value) {
    case YGLogLevelError:
      return "error";
    case YGLogLevelWarn:
      return "warn";
    case YGLogLevelInfo:
      return "info";
    case YGLogLevelDebug:
      return "debug";
    case YGLogLevelVerbose:
      return "verbose";
    case YGLogLevelFatal:
      return "fatal";
  }
  return "unknown";
}

const char* YGMeasureModeToString(const YGMeasureMode value) {
  switch (value) {
    case YGMeasureModeUndefined:
      return "undefined";
    case YGMeasureModeExactly:
      return "exactly";
    case YGMeasureModeAtMost:
      return "at-most";
  }
  return "unknown";
}

const char* YGNodeTypeToString(const YGNodeType value) {
  switch (value) {
    case YGNodeTypeDefault:
      return "default";
    case YGNodeTypeText:
      return "text";
  }
  return "unknown";
}

const char* YGOverflowToString(const YGOverflow value) {
  switch (value) {
    case YGOverflowVisible:
      return "visible";
    case YGOverflowHidden:
      return "hidden";
    case YGOverflowScroll:
      return "scroll";
  }
  return "unknown";
}

const char* YGPositionTypeToString(const YGPositionType value) {
  switch (value) {
    case YGPositionTypeStatic:
      return "static";
    case YGPositionTypeRelative:
      return "relative";
    case YGPositionTypeAbsolute:
      return "absolute";
  }
  return "unknown";
}

const char* YGUnitToString(const YGUnit value) {
  switch (value) {
    case YGUnitUndefined:
      return "undefined";
    case YGUnitPoint:
      return "point";
    case YGUnitPercent:
      return "percent";
    case YGUnitAuto:
      return "auto";
    case YGUnitMaxContent:
      return "max-content";
    case YGUnitFitContent:
      return "fit-content";
    case YGUnitStretch:
      return "stretch";
  }
  return "unknown";
}

const char* YGWrapToString(const YGWrap value) {
  switch (value) {
    case YGWrapNoWrap:
      return "no-wrap";
    case YGWrapWrap:
      return "wrap";
    case YGWrapWrapReverse:
      return "wrap-reverse";
  }
  return "unknown";
}
