{"name": "prochat-web-app", "version": "1.0.0", "description": "ProChat Progressive Web App - Mobile & Desktop Experience", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.3.0", "web-vitals": "^2.1.4", "firebase": "^9.23.0", "axios": "^1.4.0", "socket.io-client": "^4.7.2", "@mui/material": "^5.14.1", "@mui/icons-material": "^5.14.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "react-hook-form": "^7.45.2", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "framer-motion": "^10.12.18", "swiper": "^10.0.4", "react-intersection-observer": "^9.5.2", "workbox-background-sync": "^6.6.0", "workbox-broadcast-update": "^6.6.0", "workbox-cacheable-response": "^6.6.0", "workbox-core": "^6.6.0", "workbox-expiration": "^6.6.0", "workbox-google-analytics": "^6.6.0", "workbox-navigation-preload": "^6.6.0", "workbox-precaching": "^6.6.0", "workbox-range-requests": "^6.6.0", "workbox-routing": "^6.6.0", "workbox-strategies": "^6.6.0", "workbox-streams": "^6.6.0"}, "scripts": {"start": "react-scripts start", "start:https": "HTTPS=true react-scripts start", "build": "react-scripts build", "build:prod": "NODE_ENV=production react-scripts build", "build:analyze": "npm run build && npx bundle-analyzer build/static/js/*.js", "build:pwa": "npm run build && npm run generate-sw", "test": "react-scripts test", "test:coverage": "react-scripts test --coverage --watchAll=false", "test:ci": "CI=true react-scripts test --coverage --watchAll=false", "eject": "react-scripts eject", "generate-sw": "workbox generateSW workbox-config.js", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,json,css,md}", "format:check": "prettier --check src/**/*.{js,jsx,ts,tsx,json,css,md}", "serve": "npx serve -s build -l 3000", "serve:https": "npx serve -s build -l 3000 --ssl-cert cert.pem --ssl-key key.pem", "pwa:test": "npm run build && npm run serve", "lighthouse": "lighthouse http://localhost:3000 --output html --output-path ./lighthouse-report.html", "security:audit": "npm audit --audit-level moderate", "security:fix": "npm audit fix", "deps:check": "npm outdated", "deps:update": "npm update", "clean": "rm -rf build node_modules package-lock.json && npm install", "precommit": "npm run lint && npm run format:check && npm run test:ci"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}