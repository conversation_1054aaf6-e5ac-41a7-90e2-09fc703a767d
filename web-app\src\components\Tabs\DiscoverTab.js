// ProChat Discover Tab Component
// Explore content, trending topics, and discover new features

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Grid,
  Chip,
  Button,
  Avatar,
  IconButton,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
  Paper,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Search,
  TrendingUp,
  Whatshot,
  Star,
  PersonAdd,
  Verified,
  PlayArrow,
  Favorite,
  Share,
  BookmarkBorder,
  MoreVert,
  LocationOn,
  Schedule,
  Group,
  Business,
  School,
  Work,
  Event,
  LocalOffer,
} from '@mui/icons-material';

const DiscoverTab = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [trendingTopics, setTrendingTopics] = useState([]);
  const [suggestedUsers, setSuggestedUsers] = useState([]);
  const [featuredContent, setFeaturedContent] = useState([]);
  const [events, setEvents] = useState([]);
  const [jobs, setJobs] = useState([]);
  
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  useEffect(() => {
    loadDiscoverData();
  }, []);

  const loadDiscoverData = () => {
    // Mock trending topics
    setTrendingTopics([
      { tag: '#ProChatJobs', posts: 1234, trend: 'up' },
      { tag: '#TanzaniaTech', posts: 856, trend: 'up' },
      { tag: '#ProPayLaunch', posts: 2341, trend: 'hot' },
      { tag: '#DigitalPayments', posts: 567, trend: 'up' },
      { tag: '#StartupTanzania', posts: 432, trend: 'stable' },
      { tag: '#AIInnovation', posts: 789, trend: 'up' },
      { tag: '#MobileFirst', posts: 345, trend: 'stable' },
      { tag: '#FinTechAfrica', posts: 678, trend: 'hot' },
    ]);

    // Mock suggested users
    setSuggestedUsers([
      {
        id: 'user_1',
        name: 'Dr. Amina Hassan',
        username: '@aminahassan',
        avatar: '/avatars/amina.jpg',
        isVerified: true,
        bio: 'AI Researcher & Tech Entrepreneur',
        followers: 15600,
        mutualConnections: 12,
        category: 'tech',
      },
      {
        id: 'user_2',
        name: 'TechCrunch Africa',
        username: '@techcrunchafrica',
        avatar: '/avatars/techcrunch.jpg',
        isVerified: true,
        bio: 'Latest tech news from Africa',
        followers: 89400,
        mutualConnections: 45,
        category: 'media',
      },
      {
        id: 'user_3',
        name: 'Startup Grind Dar',
        username: '@startupgrinddar',
        avatar: '/avatars/startupgrind.jpg',
        isVerified: false,
        bio: 'Building startup ecosystem in Tanzania',
        followers: 5600,
        mutualConnections: 8,
        category: 'community',
      },
    ]);

    // Mock featured content
    setFeaturedContent([
      {
        id: 'content_1',
        type: 'video',
        title: 'Jinsi ya Kutumia ProPay kwa Malipo ya Haraka',
        description: 'Jifunze jinsi ya kutumia ProPay kulipa bili na kutuma pesa kwa haraka na usalama.',
        thumbnail: '/content/propay-tutorial.jpg',
        duration: '5:23',
        views: 12400,
        likes: 890,
        author: {
          name: 'ProChat Official',
          avatar: '/avatars/prochat.jpg',
          isVerified: true,
        },
        category: 'tutorial',
      },
      {
        id: 'content_2',
        type: 'article',
        title: 'Mafanikio ya Vijana wa Tanzania katika Teknolojia',
        description: 'Hadithi za kuvutia za vijana wanaobadilisha mazingira ya teknolojia Tanzania.',
        thumbnail: '/content/youth-tech.jpg',
        readTime: '8 min',
        views: 8900,
        likes: 567,
        author: {
          name: 'Tech Tanzania',
          avatar: '/avatars/techtz.jpg',
          isVerified: false,
        },
        category: 'inspiration',
      },
      {
        id: 'content_3',
        type: 'live',
        title: 'Live: Mazungumzo ya Teknolojia na Biashara',
        description: 'Jiunge nasi katika mazungumzo ya moja kwa moja kuhusu teknolojia na biashara.',
        thumbnail: '/content/live-tech.jpg',
        viewers: 234,
        isLive: true,
        author: {
          name: 'Business Hub TZ',
          avatar: '/avatars/businesshub.jpg',
          isVerified: true,
        },
        category: 'live',
      },
    ]);

    // Mock events
    setEvents([
      {
        id: 'event_1',
        title: 'Tanzania Tech Summit 2024',
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        location: 'Dar es Salaam, Tanzania',
        attendees: 1200,
        price: 'Free',
        image: '/events/tech-summit.jpg',
        organizer: 'Tanzania Tech Community',
      },
      {
        id: 'event_2',
        title: 'ProChat Developer Meetup',
        date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
        location: 'Online',
        attendees: 450,
        price: 'Free',
        image: '/events/dev-meetup.jpg',
        organizer: 'ProChat Team',
      },
    ]);

    // Mock jobs
    setJobs([
      {
        id: 'job_1',
        title: 'Senior React Developer',
        company: 'TechCorp Tanzania',
        location: 'Dar es Salaam',
        salary: 'TZS 2.5M - 4M',
        type: 'Full-time',
        posted: '2 siku zilizopita',
        logo: '/companies/techcorp.jpg',
      },
      {
        id: 'job_2',
        title: 'Product Manager',
        company: 'StartupHub',
        location: 'Remote',
        salary: 'TZS 3M - 5M',
        type: 'Full-time',
        posted: '1 wiki iliyopita',
        logo: '/companies/startuphub.jpg',
      },
    ]);
  };

  const renderTrendingTab = () => (
    <Box>
      {/* Trending Topics */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
          <TrendingUp sx={{ mr: 1 }} />
          Mada Zinazovuma
        </Typography>
        <Grid container spacing={1}>
          {trendingTopics.map((topic, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Card variant="outlined" sx={{ cursor: 'pointer', '&:hover': { bgcolor: 'action.hover' } }}>
                <CardContent sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                      {topic.tag}
                    </Typography>
                    {topic.trend === 'hot' && <Whatshot sx={{ color: 'error.main', fontSize: 20 }} />}
                    {topic.trend === 'up' && <TrendingUp sx={{ color: 'success.main', fontSize: 20 }} />}
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {topic.posts.toLocaleString()} machapisho
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Paper>

      {/* Featured Content */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
          <Star sx={{ mr: 1 }} />
          Maudhui Maalum
        </Typography>
        <Grid container spacing={3}>
          {featuredContent.map((content) => (
            <Grid item xs={12} sm={6} md={4} key={content.id}>
              <Card sx={{ cursor: 'pointer', '&:hover': { transform: 'translateY(-2px)' }, transition: 'transform 0.2s' }}>
                <Box sx={{ position: 'relative' }}>
                  <CardMedia
                    component="img"
                    height="200"
                    image={content.thumbnail}
                    alt={content.title}
                    sx={{ bgcolor: 'grey.200' }}
                  />
                  {content.type === 'video' && (
                    <Box
                      sx={{
                        position: 'absolute',
                        bottom: 8,
                        right: 8,
                        bgcolor: 'rgba(0,0,0,0.7)',
                        color: 'white',
                        px: 1,
                        py: 0.5,
                        borderRadius: 1,
                        fontSize: '0.75rem',
                      }}
                    >
                      {content.duration}
                    </Box>
                  )}
                  {content.isLive && (
                    <Chip
                      label="LIVE"
                      color="error"
                      size="small"
                      sx={{ position: 'absolute', top: 8, left: 8 }}
                    />
                  )}
                  <IconButton
                    sx={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      bgcolor: 'rgba(0,0,0,0.7)',
                      color: 'white',
                      '&:hover': { bgcolor: 'rgba(0,0,0,0.8)' },
                    }}
                  >
                    <PlayArrow />
                  </IconButton>
                </Box>
                <CardContent>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                    {content.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {content.description}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar src={content.author.avatar} sx={{ width: 24, height: 24, mr: 1 }} />
                      <Typography variant="caption">
                        {content.author.name}
                      </Typography>
                      {content.author.isVerified && (
                        <Verified sx={{ fontSize: 14, color: 'primary.main', ml: 0.5 }} />
                      )}
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="caption" color="text.secondary">
                        {content.views?.toLocaleString() || content.viewers} {content.isLive ? 'viewers' : 'views'}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Paper>
    </Box>
  );

  const renderPeopleTab = () => (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <PersonAdd sx={{ mr: 1 }} />
        Watu Unaoweza Kuwajua
      </Typography>
      <List>
        {suggestedUsers.map((user) => (
          <ListItem key={user.id} sx={{ border: 1, borderColor: 'divider', borderRadius: 2, mb: 2 }}>
            <ListItemAvatar>
              <Avatar src={user.avatar} sx={{ width: 56, height: 56 }} />
            </ListItemAvatar>
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                    {user.name}
                  </Typography>
                  {user.isVerified && <Verified sx={{ fontSize: 16, color: 'primary.main' }} />}
                </Box>
              }
              secondary={
                <Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                    {user.username}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    {user.bio}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {user.followers.toLocaleString()} wafuasi • {user.mutualConnections} marafiki wa pamoja
                  </Typography>
                </Box>
              }
            />
            <ListItemSecondaryAction>
              <Button variant="contained" size="small">
                Fuata
              </Button>
            </ListItemSecondaryAction>
          </ListItem>
        ))}
      </List>
    </Paper>
  );

  const renderEventsTab = () => (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <Event sx={{ mr: 1 }} />
        Matukio Yanayokuja
      </Typography>
      <Grid container spacing={3}>
        {events.map((event) => (
          <Grid item xs={12} sm={6} key={event.id}>
            <Card>
              <CardMedia
                component="img"
                height="160"
                image={event.image}
                alt={event.title}
                sx={{ bgcolor: 'grey.200' }}
              />
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {event.title}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Schedule sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2" color="text.secondary">
                    {event.date.toLocaleDateString('sw-TZ')}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <LocationOn sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2" color="text.secondary">
                    {event.location}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Group sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2" color="text.secondary">
                    {event.attendees} watahudhuria
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Chip label={event.price} color="success" size="small" />
                  <Button variant="outlined" size="small">
                    Jiunge
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Paper>
  );

  const renderJobsTab = () => (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <Work sx={{ mr: 1 }} />
        Fursa za Kazi
      </Typography>
      <List>
        {jobs.map((job) => (
          <ListItem key={job.id} sx={{ border: 1, borderColor: 'divider', borderRadius: 2, mb: 2 }}>
            <ListItemAvatar>
              <Avatar src={job.logo} sx={{ width: 48, height: 48 }} />
            </ListItemAvatar>
            <ListItemText
              primary={
                <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                  {job.title}
                </Typography>
              }
              secondary={
                <Box>
                  <Typography variant="body2" sx={{ mb: 0.5 }}>
                    {job.company}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, mb: 1 }}>
                    <Typography variant="caption" color="text.secondary">
                      📍 {job.location}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      💰 {job.salary}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      ⏰ {job.type}
                    </Typography>
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    Imechapishwa {job.posted}
                  </Typography>
                </Box>
              }
            />
            <ListItemSecondaryAction>
              <Button variant="contained" size="small">
                Omba
              </Button>
            </ListItemSecondaryAction>
          </ListItem>
        ))}
      </List>
    </Paper>
  );

  const tabs = [
    { label: 'Vinavyovuma', component: renderTrendingTab() },
    { label: 'Watu', component: renderPeopleTab() },
    { label: 'Matukio', component: renderEventsTab() },
    { label: 'Kazi', component: renderJobsTab() },
  ];

  return (
    <Box sx={{ p: isMobile ? 1 : 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          Gundua
        </Typography>
      </Box>

      {/* Search */}
      <TextField
        fullWidth
        placeholder="Tafuta maudhui, watu, matukio..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Search />
            </InputAdornment>
          ),
        }}
        sx={{ mb: 3 }}
      />

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          variant={isMobile ? 'scrollable' : 'fullWidth'}
          scrollButtons="auto"
        >
          {tabs.map((tab, index) => (
            <Tab key={index} label={tab.label} />
          ))}
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {tabs[activeTab]?.component}
    </Box>
  );
};

export default DiscoverTab;
