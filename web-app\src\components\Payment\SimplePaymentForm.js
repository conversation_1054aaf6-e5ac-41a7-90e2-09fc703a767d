// ProChat Simple Payment Form Component
// Basic payment form without external dependencies

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Chip,
  Divider,
} from '@mui/material';
import {
  Payment,
  Security,
  CheckCircle,
  Error,
  Phone,
  CreditCard,
  AccountBalance,
} from '@mui/icons-material';

const SimplePaymentForm = ({ 
  amount = 10000, 
  currency = 'TZS', 
  description = 'Malipo ya ProPay',
  onSuccess,
  onError 
}) => {
  const [selectedMethod, setSelectedMethod] = useState('');
  const [formData, setFormData] = useState({
    amount: amount,
    customerPhone: '',
    customerAccount: '',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
    description: description,
  });
  const [processing, setProcessing] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  const [result, setResult] = useState(null);

  const paymentMethods = [
    { id: 'mpesa', name: 'M-<PERSON>esa', icon: '📱', type: 'mobile' },
    { id: 'airtel_money', name: 'Airtel Money', icon: '📱', type: 'mobile' },
    { id: 'tigo_pesa', name: 'Tigo Pesa', icon: '📱', type: 'mobile' },
    { id: 'visa', name: 'Visa Card', icon: '💳', type: 'card' },
    { id: 'mastercard', name: 'Mastercard', icon: '💳', type: 'card' },
    { id: 'crdb_bank', name: 'CRDB Bank', icon: '🏦', type: 'bank' },
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = () => {
    if (!formData.amount || formData.amount < 100) {
      return 'Kiasi cha chini ni TZS 100';
    }
    
    if (['mpesa', 'airtel_money', 'tigo_pesa'].includes(selectedMethod)) {
      if (!formData.customerPhone) {
        return 'Nambari ya simu ni lazima';
      }
      if (!/^(\+255|0)[67]\d{8}$/.test(formData.customerPhone)) {
        return 'Nambari ya simu si sahihi';
      }
    }
    
    if (['visa', 'mastercard'].includes(selectedMethod)) {
      if (!formData.cardNumber || !formData.expiryDate || !formData.cvv || !formData.cardholderName) {
        return 'Taarifa zote za kadi ni lazima';
      }
    }
    
    if (['crdb_bank'].includes(selectedMethod)) {
      if (!formData.customerAccount) {
        return 'Nambari ya akaunti ni lazima';
      }
    }
    
    return null;
  };

  const processPayment = async () => {
    setProcessing(true);
    setShowConfirm(false);
    
    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const success = Math.random() > 0.2; // 80% success rate
      
      const paymentResult = {
        success,
        message: success ? 'Malipo yamefanikiwa!' : 'Malipo yameshindwa. Jaribu tena.',
        transactionId: success ? 'TXN_' + Date.now() : null,
        amount: formData.amount,
        method: selectedMethod,
      };
      
      setResult(paymentResult);
      
      if (success && onSuccess) {
        onSuccess(paymentResult);
      } else if (!success && onError) {
        onError(paymentResult);
      }
    } catch (error) {
      const errorResult = {
        success: false,
        message: 'Hitilafu imetokea. Jaribu tena.',
        error: error.message,
      };
      
      setResult(errorResult);
      if (onError) {
        onError(errorResult);
      }
    } finally {
      setProcessing(false);
    }
  };

  const handleSubmit = () => {
    const error = validateForm();
    if (error) {
      alert(error);
      return;
    }
    setShowConfirm(true);
  };

  const renderMethodSelector = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Chagua Njia ya Malipo
      </Typography>
      <Grid container spacing={2}>
        {paymentMethods.map((method) => (
          <Grid item xs={6} sm={4} key={method.id}>
            <Button
              fullWidth
              variant={selectedMethod === method.id ? 'contained' : 'outlined'}
              onClick={() => setSelectedMethod(method.id)}
              sx={{ 
                p: 2, 
                flexDirection: 'column',
                height: 80,
                '&:hover': {
                  transform: 'translateY(-2px)',
                },
                transition: 'transform 0.2s',
              }}
            >
              <Typography variant="h4" sx={{ mb: 1 }}>{method.icon}</Typography>
              <Typography variant="body2">{method.name}</Typography>
            </Button>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  const renderPaymentForm = () => {
    const method = paymentMethods.find(m => m.id === selectedMethod);
    if (!method) return null;

    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Taarifa za Malipo - {method.name}
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Kiasi"
              type="number"
              value={formData.amount}
              onChange={(e) => handleInputChange('amount', e.target.value)}
              InputProps={{
                startAdornment: <Typography sx={{ mr: 1 }}>{currency}</Typography>,
              }}
            />
          </Grid>

          {/* Mobile Money Fields */}
          {method.type === 'mobile' && (
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Nambari ya Simu"
                value={formData.customerPhone}
                onChange={(e) => handleInputChange('customerPhone', e.target.value)}
                placeholder="+************"
                InputProps={{
                  startAdornment: <Phone sx={{ mr: 1, color: 'action.active' }} />,
                }}
              />
            </Grid>
          )}

          {/* Bank Fields */}
          {method.type === 'bank' && (
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Nambari ya Akaunti"
                value={formData.customerAccount}
                onChange={(e) => handleInputChange('customerAccount', e.target.value)}
                InputProps={{
                  startAdornment: <AccountBalance sx={{ mr: 1, color: 'action.active' }} />,
                }}
              />
            </Grid>
          )}

          {/* Card Fields */}
          {method.type === 'card' && (
            <>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Jina la Mmiliki wa Kadi"
                  value={formData.cardholderName}
                  onChange={(e) => handleInputChange('cardholderName', e.target.value)}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Nambari ya Kadi"
                  value={formData.cardNumber}
                  onChange={(e) => handleInputChange('cardNumber', e.target.value)}
                  placeholder="1234 5678 9012 3456"
                  InputProps={{
                    startAdornment: <CreditCard sx={{ mr: 1, color: 'action.active' }} />,
                  }}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Tarehe ya Mwisho"
                  value={formData.expiryDate}
                  onChange={(e) => handleInputChange('expiryDate', e.target.value)}
                  placeholder="MM/YY"
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="CVV"
                  value={formData.cvv}
                  onChange={(e) => handleInputChange('cvv', e.target.value)}
                  placeholder="123"
                />
              </Grid>
            </>
          )}

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Maelezo (si lazima)"
              multiline
              rows={2}
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
          <Button onClick={() => setSelectedMethod('')}>
            Rudi Nyuma
          </Button>
          <Button variant="contained" onClick={handleSubmit}>
            Thibitisha Malipo
          </Button>
        </Box>
      </Box>
    );
  };

  const renderResult = () => {
    if (!result) return null;

    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        {result.success ? (
          <>
            <CheckCircle sx={{ fontSize: 60, color: 'success.main', mb: 2 }} />
            <Typography variant="h5" gutterBottom color="success.main">
              Malipo Yamefanikiwa!
            </Typography>
            <Typography variant="body1" gutterBottom>
              {result.message}
            </Typography>
            {result.transactionId && (
              <Chip
                label={`ID: ${result.transactionId}`}
                variant="outlined"
                sx={{ mt: 2 }}
              />
            )}
          </>
        ) : (
          <>
            <Error sx={{ fontSize: 60, color: 'error.main', mb: 2 }} />
            <Typography variant="h5" gutterBottom color="error.main">
              Malipo Yameshindwa
            </Typography>
            <Typography variant="body1" gutterBottom>
              {result.message}
            </Typography>
            <Button
              variant="contained"
              onClick={() => {
                setResult(null);
                setSelectedMethod('');
              }}
              sx={{ mt: 2 }}
            >
              Jaribu Tena
            </Button>
          </>
        )}
      </Box>
    );
  };

  if (processing) {
    return (
      <Card>
        <CardContent>
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <CircularProgress size={60} sx={{ mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Inachakata malipo...
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Tafadhali subiri, usifunge ukurasa huu
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (result) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <Payment sx={{ mr: 1 }} />
            Matokeo ya Malipo
          </Typography>
          {renderResult()}
        </CardContent>
      </Card>
    );
  }

  return (
    <Box>
      <Card>
        <CardContent>
          <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <Payment sx={{ mr: 1 }} />
            Malipo ya ProChat
          </Typography>

          {!selectedMethod ? renderMethodSelector() : renderPaymentForm()}
        </CardContent>
      </Card>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirm} onClose={() => setShowConfirm(false)}>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Security sx={{ mr: 1 }} />
            Thibitisha Malipo
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Je, una uhakika unataka kuendelea na malipo haya?
          </Typography>
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Kiasi:</strong> {currency} {formData.amount?.toLocaleString()}
            </Typography>
            <Typography variant="body2">
              <strong>Njia ya Malipo:</strong> {paymentMethods.find(m => m.id === selectedMethod)?.name}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowConfirm(false)}>
            Ghairi
          </Button>
          <Button variant="contained" onClick={processPayment}>
            Thibitisha
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SimplePaymentForm;
