// ProChat Payment API Integration
import axios from 'axios';
import { Platform } from 'react-native';

const API_BASE_URL = Platform.select({
  web: 'http://localhost:8080/api',
  default: 'http://********:8080/api' // Android emulator
});

// Payment API endpoints
export const paymentAPI = {
  // Wallet operations
  getBalance: async (userId) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/wallet/balance/${userId}`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch wallet balance');
    }
  },

  // Send money
  sendMoney: async (sendData) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/wallet/send`, sendData);
      return response.data;
    } catch (error) {
      throw new Error('Failed to send money');
    }
  },

  // Receive money
  receiveMoney: async (receiveData) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/wallet/receive`, receiveData);
      return response.data;
    } catch (error) {
      throw new Error('Failed to receive money');
    }
  },

  // Pay bills
  payBill: async (billData) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/wallet/pay-bill`, billData);
      return response.data;
    } catch (error) {
      throw new Error('Failed to pay bill');
    }
  },

  // Buy airtime
  buyAirtime: async (airtimeData) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/wallet/buy-airtime`, airtimeData);
      return response.data;
    } catch (error) {
      throw new Error('Failed to buy airtime');
    }
  },

  // Get transaction history
  getTransactions: async (userId) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/wallet/transactions/${userId}`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch transactions');
    }
  },

  // Mobile money integration
  mobileMoneyTransfer: async (transferData) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/mobile-money/transfer`, transferData);
      return response.data;
    } catch (error) {
      throw new Error('Mobile money transfer failed');
    }
  },

  // Bank transfer
  bankTransfer: async (transferData) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/bank/transfer`, transferData);
      return response.data;
    } catch (error) {
      throw new Error('Bank transfer failed');
    }
  },

  // Agent services
  findNearbyAgents: async (location) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/agents/nearby`, {
        params: { lat: location.latitude, lng: location.longitude }
      });
      return response.data;
    } catch (error) {
      throw new Error('Failed to find nearby agents');
    }
  }
};

export default paymentAPI;
