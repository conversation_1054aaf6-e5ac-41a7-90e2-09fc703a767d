// ProChat Service Worker
// Handles offline functionality, caching, and push notifications

const CACHE_NAME = 'prochat-v1.0.0';
const API_CACHE_NAME = 'prochat-api-v1.0.0';

// Files to cache for offline functionality
const STATIC_CACHE_URLS = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json',
  '/logo192.png',
  '/logo512.png',
  // Add other static assets
];

// API endpoints to cache
const API_CACHE_URLS = [
  '/api/auth/me',
  '/api/posts/feed',
  '/api/chat/conversations',
  '/api/events/upcoming',
  '/api/jobs/featured',
  '/api/news/latest',
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('ProChat SW: Installing...');
  
  event.waitUntil(
    Promise.all([
      // Cache static files
      caches.open(CACHE_NAME).then((cache) => {
        console.log('ProChat SW: Caching static files');
        return cache.addAll(STATIC_CACHE_URLS);
      }),
      // Skip waiting to activate immediately
      self.skipWaiting()
    ])
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('ProChat SW: Activating...');
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && cacheName !== API_CACHE_NAME) {
              console.log('ProChat SW: Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // Take control of all clients
      self.clients.claim()
    ])
  );
});

// Fetch event - handle network requests
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle API requests
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request));
    return;
  }

  // Handle static file requests
  if (request.destination === 'document' || 
      request.destination === 'script' || 
      request.destination === 'style' ||
      request.destination === 'image') {
    event.respondWith(handleStaticRequest(request));
    return;
  }

  // Default: try network first, then cache
  event.respondWith(
    fetch(request).catch(() => {
      return caches.match(request);
    })
  );
});

// Handle API requests with network-first strategy
async function handleApiRequest(request) {
  const cache = await caches.open(API_CACHE_NAME);
  
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    // Cache successful responses
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('ProChat SW: Network failed, trying cache for:', request.url);
    
    // Fallback to cache
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline response for critical endpoints
    if (request.url.includes('/api/auth/me')) {
      return new Response(JSON.stringify({ offline: true }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    throw error;
  }
}

// Handle static requests with cache-first strategy
async function handleStaticRequest(request) {
  const cache = await caches.open(CACHE_NAME);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    
    // Cache the response
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    // Return offline page for navigation requests
    if (request.destination === 'document') {
      return cache.match('/');
    }
    
    throw error;
  }
}

// Push notification event
self.addEventListener('push', (event) => {
  console.log('ProChat SW: Push received');
  
  let data = {};
  if (event.data) {
    data = event.data.json();
  }
  
  const options = {
    title: data.title || 'ProChat',
    body: data.body || 'Una ujumbe mpya',
    icon: '/logo192.png',
    badge: '/badge-72x72.png',
    image: data.image,
    data: data.data,
    actions: [
      {
        action: 'open',
        title: 'Fungua',
        icon: '/action-open.png'
      },
      {
        action: 'close',
        title: 'Funga',
        icon: '/action-close.png'
      }
    ],
    tag: data.tag || 'prochat-notification',
    renotify: true,
    requireInteraction: data.requireInteraction || false,
    silent: false,
    vibrate: [200, 100, 200],
    timestamp: Date.now()
  };
  
  event.waitUntil(
    self.registration.showNotification(options.title, options)
  );
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
  console.log('ProChat SW: Notification clicked');
  
  event.notification.close();
  
  const action = event.action;
  const data = event.notification.data;
  
  if (action === 'close') {
    return;
  }
  
  // Determine URL to open
  let urlToOpen = '/';
  if (data && data.url) {
    urlToOpen = data.url;
  } else if (data && data.type) {
    switch (data.type) {
      case 'chat':
        urlToOpen = '/chat';
        break;
      case 'event':
        urlToOpen = '/events';
        break;
      case 'job':
        urlToOpen = '/jobs';
        break;
      case 'news':
        urlToOpen = '/news';
        break;
      default:
        urlToOpen = '/';
    }
  }
  
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then((clientList) => {
        // Check if app is already open
        for (const client of clientList) {
          if (client.url.includes(self.location.origin)) {
            client.focus();
            client.postMessage({
              type: 'NAVIGATE',
              url: urlToOpen
            });
            return;
          }
        }
        
        // Open new window
        return clients.openWindow(urlToOpen);
      })
  );
});

// Background sync event
self.addEventListener('sync', (event) => {
  console.log('ProChat SW: Background sync triggered');
  
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

// Perform background sync
async function doBackgroundSync() {
  try {
    // Sync pending messages, posts, etc.
    console.log('ProChat SW: Performing background sync');
    
    // This would typically sync with your backend
    // For now, just log the action
    
  } catch (error) {
    console.error('ProChat SW: Background sync failed:', error);
  }
}

// Message event - handle messages from main thread
self.addEventListener('message', (event) => {
  console.log('ProChat SW: Message received:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CACHE_URLS') {
    const urls = event.data.urls;
    event.waitUntil(
      caches.open(CACHE_NAME).then((cache) => {
        return cache.addAll(urls);
      })
    );
  }
});
