@echo off
echo ========================================
echo ProChat Alternative Database Setup
echo ========================================

echo.
echo Trying different MySQL password combinations...

echo Testing with empty password...
mysql -u root -e "SELECT 'Success with empty password!' as result;" 2>nul
if %errorlevel% equ 0 (
    echo ✅ MySQL works with empty password!
    echo Setting new password...
    mysql -u root -e "ALTER USER 'root'@'localhost' IDENTIFIED BY 'Ram$0101';"
    echo Creating database...
    mysql -u root -p"Ram$0101" -e "CREATE DATABASE IF NOT EXISTS prochat_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    goto :success
)

echo Testing with 'root' password...
mysql -u root -p"root" -e "SELECT 'Success with root password!' as result;" 2>nul
if %errorlevel% equ 0 (
    echo ✅ MySQL works with 'root' password!
    echo Setting new password...
    mysql -u root -p"root" -e "ALTER USER 'root'@'localhost' IDENTIFIED BY 'Ram$0101';"
    echo Creating database...
    mysql -u root -p"Ram$0101" -e "CREATE DATABASE IF NOT EXISTS prochat_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    goto :success
)

echo Testing with 'password' password...
mysql -u root -p"password" -e "SELECT 'Success with password!' as result;" 2>nul
if %errorlevel% equ 0 (
    echo ✅ MySQL works with 'password' password!
    echo Setting new password...
    mysql -u root -p"password" -e "ALTER USER 'root'@'localhost' IDENTIFIED BY 'Ram$0101';"
    echo Creating database...
    mysql -u root -p"Ram$0101" -e "CREATE DATABASE IF NOT EXISTS prochat_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    goto :success
)

echo Testing with '123456' password...
mysql -u root -p"123456" -e "SELECT 'Success with 123456!' as result;" 2>nul
if %errorlevel% equ 0 (
    echo ✅ MySQL works with '123456' password!
    echo Setting new password...
    mysql -u root -p"123456" -e "ALTER USER 'root'@'localhost' IDENTIFIED BY 'Ram$0101';"
    echo Creating database...
    mysql -u root -p"Ram$0101" -e "CREATE DATABASE IF NOT EXISTS prochat_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    goto :success
)

echo Testing current password Ram$0101...
mysql -u root -p"Ram$0101" -e "SELECT 'Password already correct!' as result;" 2>nul
if %errorlevel% equ 0 (
    echo ✅ MySQL already has correct password!
    echo Creating database...
    mysql -u root -p"Ram$0101" -e "CREATE DATABASE IF NOT EXISTS prochat_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    goto :success
)

echo.
echo ❌ Could not connect to MySQL with any common passwords.
echo.
echo Manual setup required:
echo 1. Open MySQL Workbench or command line
echo 2. Connect with your current password
echo 3. Run: ALTER USER 'root'@'localhost' IDENTIFIED BY 'Ram$0101';
echo 4. Run: CREATE DATABASE IF NOT EXISTS prochat_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
echo.
echo Or reset MySQL password using:
echo 1. Stop MySQL service: net stop mysql80
echo 2. Start in safe mode: mysqld --skip-grant-tables
echo 3. Connect and reset: mysql -u root
echo 4. Run: FLUSH PRIVILEGES; ALTER USER 'root'@'localhost' IDENTIFIED BY 'Ram$0101';
echo.
goto :end

:success
echo.
echo ✅ Database setup completed successfully!
echo Database: prochat_db
echo Username: root  
echo Password: Ram$0101
echo.

:end
pause
