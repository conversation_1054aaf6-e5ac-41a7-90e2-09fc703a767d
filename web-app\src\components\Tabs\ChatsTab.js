// ProChat Chats Tab Component
// Messaging interface with chat list and conversations

import React, { useState, useEffect } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Typography,
  Badge,
  IconButton,
  TextField,
  InputAdornment,
  Fab,
  Chip,
  Paper,
  Divider,
  Menu,
  MenuItem,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Search,
  Edit,
  MoreVert,
  VideoCall,
  Call,
  Info,
  Archive,
  Delete,
  Block,
  Verified,
  Group,
  Business,
  Star,
  Schedule,
  Done,
  DoneAll,
} from '@mui/icons-material';

const ChatsTab = () => {
  const [chats, setChats] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedChat, setSelectedChat] = useState(null);
  const [anchorEl, setAnchorEl] = useState(null);
  const [activeFilter, setActiveFilter] = useState('all');
  
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  useEffect(() => {
    loadChats();
  }, []);

  const loadChats = () => {
    // Mock chat data
    const mockChats = [
      {
        id: 'chat_1',
        type: 'individual',
        participant: {
          name: 'Mary Kimani',
          username: '@marykimani',
          avatar: '/avatars/mary.jpg',
          isVerified: false,
          isOnline: true,
          lastSeen: null,
        },
        lastMessage: {
          text: 'Asante sana kwa msaada! Nimepata kazi 🎉',
          timestamp: new Date(Date.now() - 300000), // 5 minutes ago
          senderId: 'user_2',
          isRead: false,
          messageType: 'text',
        },
        unreadCount: 2,
        isPinned: true,
        isMuted: false,
        isArchived: false,
      },
      {
        id: 'chat_2',
        type: 'group',
        groupInfo: {
          name: 'ProChat Developers',
          avatar: '/avatars/group1.jpg',
          memberCount: 156,
          description: 'Kikundi cha wasanidi programu wa ProChat',
        },
        lastMessage: {
          text: 'John: Tumefanikiwa kuongeza kipengele kipya cha ProPay!',
          timestamp: new Date(Date.now() - 1800000), // 30 minutes ago
          senderId: 'user_3',
          senderName: 'John Mwangi',
          isRead: true,
          messageType: 'text',
        },
        unreadCount: 0,
        isPinned: false,
        isMuted: false,
        isArchived: false,
      },
      {
        id: 'chat_3',
        type: 'business',
        participant: {
          name: 'ProChat Support',
          username: '@prochatsupport',
          avatar: '/avatars/support.jpg',
          isVerified: true,
          isOnline: true,
          lastSeen: null,
          businessType: 'support',
        },
        lastMessage: {
          text: 'Karibu ProChat! Tupo hapa kukusaidia 24/7',
          timestamp: new Date(Date.now() - 3600000), // 1 hour ago
          senderId: 'support_1',
          isRead: true,
          messageType: 'text',
        },
        unreadCount: 0,
        isPinned: false,
        isMuted: false,
        isArchived: false,
      },
      {
        id: 'chat_4',
        type: 'individual',
        participant: {
          name: 'Sarah Mwalimu',
          username: '@sarahmwalimu',
          avatar: '/avatars/sarah.jpg',
          isVerified: false,
          isOnline: false,
          lastSeen: new Date(Date.now() - 7200000), // 2 hours ago
        },
        lastMessage: {
          text: 'Nimesoma makala yako kuhusu AI. Ni nzuri sana!',
          timestamp: new Date(Date.now() - 7200000),
          senderId: 'user_4',
          isRead: true,
          messageType: 'text',
        },
        unreadCount: 0,
        isPinned: false,
        isMuted: false,
        isArchived: false,
      },
      {
        id: 'chat_5',
        type: 'group',
        groupInfo: {
          name: 'Tanzania Tech Community',
          avatar: '/avatars/group2.jpg',
          memberCount: 2340,
          description: 'Jumuiya ya wateknolojia Tanzania',
        },
        lastMessage: {
          text: 'Alice: Kuna event ya tech kesho Dar es Salaam',
          timestamp: new Date(Date.now() - 10800000), // 3 hours ago
          senderId: 'user_5',
          senderName: 'Alice Mwenda',
          isRead: true,
          messageType: 'text',
        },
        unreadCount: 5,
        isPinned: false,
        isMuted: true,
        isArchived: false,
      },
      {
        id: 'chat_6',
        type: 'individual',
        participant: {
          name: 'David Mwangi',
          username: '@davidmwangi',
          avatar: '/avatars/david.jpg',
          isVerified: false,
          isOnline: false,
          lastSeen: new Date(Date.now() - 86400000), // 1 day ago
        },
        lastMessage: {
          text: 'Unafikiri nini kuhusu mradi wetu?',
          timestamp: new Date(Date.now() - 86400000),
          senderId: 'current_user',
          isRead: true,
          messageType: 'text',
        },
        unreadCount: 0,
        isPinned: false,
        isMuted: false,
        isArchived: false,
      },
    ];

    setChats(mockChats);
  };

  const filteredChats = chats.filter(chat => {
    const matchesSearch = chat.type === 'individual' 
      ? chat.participant.name.toLowerCase().includes(searchQuery.toLowerCase())
      : chat.groupInfo.name.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesFilter = activeFilter === 'all' || 
      (activeFilter === 'unread' && chat.unreadCount > 0) ||
      (activeFilter === 'groups' && chat.type === 'group') ||
      (activeFilter === 'business' && chat.type === 'business');
    
    return matchesSearch && matchesFilter && !chat.isArchived;
  });

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Sasa hivi';
    if (minutes < 60) return `${minutes}d`;
    if (hours < 24) return `${hours}s`;
    if (days < 7) return `${days} siku`;
    return timestamp.toLocaleDateString('sw-TZ');
  };

  const getMessageStatus = (message, isCurrentUser) => {
    if (!isCurrentUser) return null;
    
    if (message.isRead) {
      return <DoneAll sx={{ fontSize: 16, color: 'primary.main' }} />;
    } else {
      return <Done sx={{ fontSize: 16, color: 'text.secondary' }} />;
    }
  };

  const getChatIcon = (chat) => {
    if (chat.type === 'group') {
      return <Group sx={{ fontSize: 16, color: 'text.secondary' }} />;
    } else if (chat.type === 'business') {
      return <Business sx={{ fontSize: 16, color: 'primary.main' }} />;
    }
    return null;
  };

  const renderChatItem = (chat) => {
    const isCurrentUserMessage = chat.lastMessage.senderId === 'current_user';
    const displayName = chat.type === 'individual' 
      ? chat.participant.name 
      : chat.groupInfo.name;
    const avatar = chat.type === 'individual' 
      ? chat.participant.avatar 
      : chat.groupInfo.avatar;
    const isOnline = chat.type === 'individual' && chat.participant.isOnline;
    const isVerified = chat.type === 'individual' && chat.participant.isVerified;

    return (
      <ListItem
        key={chat.id}
        button
        onClick={() => setSelectedChat(chat)}
        sx={{
          bgcolor: selectedChat?.id === chat.id ? 'action.selected' : 'transparent',
          '&:hover': { bgcolor: 'action.hover' },
          borderRadius: 1,
          mb: 0.5,
        }}
      >
        <ListItemAvatar>
          <Badge
            overlap="circular"
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            badgeContent={
              isOnline ? (
                <Box
                  sx={{
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    bgcolor: 'success.main',
                    border: 2,
                    borderColor: 'background.paper',
                  }}
                />
              ) : null
            }
          >
            <Avatar src={avatar} />
          </Badge>
        </ListItemAvatar>

        <ListItemText
          primary={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Typography
                variant="subtitle1"
                sx={{
                  fontWeight: chat.unreadCount > 0 ? 600 : 400,
                  color: chat.unreadCount > 0 ? 'text.primary' : 'text.primary',
                }}
              >
                {displayName}
              </Typography>
              {isVerified && <Verified sx={{ fontSize: 16, color: 'primary.main' }} />}
              {getChatIcon(chat)}
              {chat.isPinned && <Star sx={{ fontSize: 16, color: 'warning.main' }} />}
              {chat.isMuted && <Schedule sx={{ fontSize: 16, color: 'text.disabled' }} />}
            </Box>
          }
          secondary={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              {getMessageStatus(chat.lastMessage, isCurrentUserMessage)}
              <Typography
                variant="body2"
                sx={{
                  color: chat.unreadCount > 0 ? 'text.primary' : 'text.secondary',
                  fontWeight: chat.unreadCount > 0 ? 500 : 400,
                  flex: 1,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {chat.type === 'group' && chat.lastMessage.senderName && 
                  `${chat.lastMessage.senderName}: `
                }
                {chat.lastMessage.text}
              </Typography>
            </Box>
          }
        />

        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 1 }}>
          <Typography variant="caption" color="text.secondary">
            {formatTimestamp(chat.lastMessage.timestamp)}
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            {chat.unreadCount > 0 && (
              <Badge
                badgeContent={chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                color="primary"
                sx={{
                  '& .MuiBadge-badge': {
                    position: 'static',
                    transform: 'none',
                  },
                }}
              />
            )}
            
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                setAnchorEl(e.currentTarget);
                setSelectedChat(chat);
              }}
            >
              <MoreVert sx={{ fontSize: 16 }} />
            </IconButton>
          </Box>
        </Box>
      </ListItem>
    );
  };

  const filters = [
    { key: 'all', label: 'Zote', count: chats.length },
    { key: 'unread', label: 'Hazijasomwa', count: chats.filter(c => c.unreadCount > 0).length },
    { key: 'groups', label: 'Vikundi', count: chats.filter(c => c.type === 'group').length },
    { key: 'business', label: 'Biashara', count: chats.filter(c => c.type === 'business').length },
  ];

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
            Mazungumzo
          </Typography>
          <Box>
            <IconButton>
              <VideoCall />
            </IconButton>
            <IconButton>
              <Edit />
            </IconButton>
          </Box>
        </Box>

        {/* Search */}
        <TextField
          fullWidth
          placeholder="Tafuta mazungumzo..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
          sx={{ mb: 2 }}
        />

        {/* Filters */}
        <Box sx={{ display: 'flex', gap: 1, overflowX: 'auto' }}>
          {filters.map((filter) => (
            <Chip
              key={filter.key}
              label={`${filter.label} (${filter.count})`}
              onClick={() => setActiveFilter(filter.key)}
              color={activeFilter === filter.key ? 'primary' : 'default'}
              variant={activeFilter === filter.key ? 'filled' : 'outlined'}
            />
          ))}
        </Box>
      </Box>

      {/* Chat List */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <List sx={{ p: 2 }}>
          {filteredChats.map(renderChatItem)}
        </List>

        {filteredChats.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Hakuna mazungumzo
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {searchQuery ? 'Hakuna matokeo ya utafutaji' : 'Anza mazungumzo mapya'}
            </Typography>
          </Box>
        )}
      </Box>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        sx={{
          position: 'fixed',
          bottom: isMobile ? 90 : 20,
          right: 20,
        }}
      >
        <Edit />
      </Fab>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
      >
        <MenuItem onClick={() => setAnchorEl(null)}>
          <Info sx={{ mr: 1 }} />
          Maelezo
        </MenuItem>
        <MenuItem onClick={() => setAnchorEl(null)}>
          <Archive sx={{ mr: 1 }} />
          Hifadhi
        </MenuItem>
        <MenuItem onClick={() => setAnchorEl(null)}>
          <Block sx={{ mr: 1 }} />
          Zuia
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => setAnchorEl(null)} sx={{ color: 'error.main' }}>
          <Delete sx={{ mr: 1 }} />
          Futa
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default ChatsTab;
