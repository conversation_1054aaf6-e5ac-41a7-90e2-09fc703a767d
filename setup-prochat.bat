@echo off
color 0A
echo ========================================
echo    ProChat Complete Setup Script
echo ========================================
echo.

echo 🚀 Setting up ProChat development environment...
echo.

echo ========================================
echo Step 1: Environment Check
echo ========================================
echo.
echo Checking required software...

echo Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found! Please install Node.js first.
    echo Download from: https://nodejs.org/
    pause
    exit /b 1
) else (
    echo ✅ Node.js found: 
    node --version
)

echo.
echo Checking npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm not found!
    pause
    exit /b 1
) else (
    echo ✅ npm found:
    npm --version
)

echo.
echo Checking Java...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java not found! Please install Java 11.
    pause
    exit /b 1
) else (
    echo ✅ Java found:
    java -version 2>&1 | findstr "version"
)

echo.
echo Checking Maven...
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Maven not found! Please install Maven.
    pause
    exit /b 1
) else (
    echo ✅ Maven found:
    mvn -version | findstr "Apache Maven"
)

echo.
echo Checking MySQL...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL not found! Please install MySQL 8.0.
    pause
    exit /b 1
) else (
    echo ✅ MySQL found:
    mysql --version
)

echo.
echo ========================================
echo Step 2: Database Setup
echo ========================================
echo.
echo Setting up ProChat database...

echo Starting MySQL service...
net start mysql80 >nul 2>&1

echo Testing MySQL connection...
mysql -u root -p"Ram$0101" -e "SELECT 'Connection successful!' as status;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL connection failed! Running setup...
    call setup-mysql.bat
) else (
    echo ✅ MySQL connection successful!
)

echo Creating/updating database...
mysql -u root -p"Ram$0101" -e "CREATE DATABASE IF NOT EXISTS prochat_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" >nul 2>&1
echo ✅ Database created/verified!

echo.
echo ========================================
echo Step 3: NPM Dependencies
echo ========================================
echo.
echo Installing frontend dependencies...

echo Configuring npm...
npm config set legacy-peer-deps true
npm config set fetch-timeout 600000
npm cache clean --force >nul 2>&1

echo Installing web-app dependencies...
cd web-app
npm install --legacy-peer-deps >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Web-app dependencies installation had issues, but continuing...
) else (
    echo ✅ Web-app dependencies installed!
)

echo Installing admin-panel dependencies...
cd ..\admin-panel
npm install --legacy-peer-deps >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Admin-panel dependencies installation had issues, but continuing...
) else (
    echo ✅ Admin-panel dependencies installed!
)

echo Installing public-website dependencies...
cd ..\public-website
npm install --legacy-peer-deps >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Public-website dependencies installation had issues, but continuing...
) else (
    echo ✅ Public-website dependencies installed!
)

cd ..

echo.
echo ========================================
echo Step 4: Backend Compilation
echo ========================================
echo.
echo Compiling backend...

cd backend
mvn clean compile -DskipTests -q >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Backend compilation had issues, but continuing...
) else (
    echo ✅ Backend compiled successfully!
)

cd ..

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo ✅ ProChat development environment is ready!
echo.
echo Next steps:
echo 1. Run: run-project-local.bat (to start all services)
echo 2. Open: http://localhost:3000 (Web App)
echo 3. Open: http://localhost:3001 (Admin Panel)
echo 4. Open: http://localhost:3002 (Public Website)
echo.
echo Would you like to start the development servers now? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo.
    echo Starting development servers...
    call run-project-local.bat
)

echo.
echo Setup completed successfully! 🎉
pause
