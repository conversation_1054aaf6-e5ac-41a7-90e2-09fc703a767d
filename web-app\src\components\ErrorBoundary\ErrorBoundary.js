// ProChat Enhanced Error Boundary
// Advanced error handling with cross-platform monitoring

import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Card,
  CardContent,
  <PERSON>ert,
  <PERSON><PERSON>se,
  IconButton,
  Chip,
} from '@mui/material';
import {
  Refresh,
  BugReport,
  ExpandMore,
  ExpandLess,
  Home,
  Send,
} from '@mui/icons-material';
import { SecurityLogger } from '../../config/security';
import { detectDeviceType } from '../../config/responsive';
import analyticsService from '../../services/analyticsService';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      showDetails: false,
      retryCount: 0,
      isReporting: false,
      reportSent: false,
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    this.setState({
      errorInfo,
    });

    // Log to console for development
    console.error('ProChat Error Boundary caught an error:', error, errorInfo);

    // Create detailed error report
    const errorReport = this.createErrorReport(error, errorInfo);

    // Log to security logger
    SecurityLogger.logSecurityEvent('REACT_ERROR_BOUNDARY', errorReport);

    // Track in analytics
    analyticsService.trackError({
      type: 'react_error_boundary',
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
    });

    // Send to error monitoring service
    this.sendErrorReport(errorReport);
  }

  createErrorReport(error, errorInfo) {
    const deviceInfo = {
      deviceType: detectDeviceType(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
    };

    return {
      errorId: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      props: this.props,
      deviceInfo,
      retryCount: this.state.retryCount,
      userId: localStorage.getItem('prochat_user_id'),
      sessionId: analyticsService.sessionId,
    };
  }

  async sendErrorReport(errorReport) {
    try {
      await fetch('/api/errors/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('prochat_access_token')}`,
        },
        body: JSON.stringify(errorReport),
      });
    } catch (error) {
      console.error('Failed to send error report:', error);
    }
  }

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      showDetails: false,
      retryCount: prevState.retryCount + 1,
      isReporting: false,
      reportSent: false,
    }));

    // Track retry attempt
    analyticsService.trackEvent('error_boundary_retry', {
      retryCount: this.state.retryCount + 1,
      errorId: this.state.errorId,
    });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleReportError = async () => {
    this.setState({ isReporting: true });

    try {
      const errorReport = this.createErrorReport(this.state.error, this.state.errorInfo);
      
      // Send detailed report
      await this.sendErrorReport({
        ...errorReport,
        userReported: true,
        userFeedback: 'User manually reported this error',
      });

      this.setState({ 
        isReporting: false, 
        reportSent: true 
      });

      // Track manual report
      analyticsService.trackEvent('error_manually_reported', {
        errorId: this.state.errorId,
      });

    } catch (error) {
      console.error('Failed to report error:', error);
      this.setState({ isReporting: false });
    }
  };

  toggleDetails = () => {
    this.setState(prevState => ({
      showDetails: !prevState.showDetails,
    }));
  };

  getErrorSeverity() {
    const { error } = this.state;
    
    if (!error) return 'medium';
    
    // Determine severity based on error type
    if (error.name === 'ChunkLoadError') return 'low';
    if (error.message.includes('Network Error')) return 'low';
    if (error.message.includes('TypeError')) return 'medium';
    if (error.message.includes('ReferenceError')) return 'high';
    if (error.message.includes('SecurityError')) return 'critical';
    
    return 'medium';
  }

  getSeverityColor() {
    const severity = this.getErrorSeverity();
    
    switch (severity) {
      case 'low': return 'info';
      case 'medium': return 'warning';
      case 'high': return 'error';
      case 'critical': return 'error';
      default: return 'warning';
    }
  }

  getErrorSuggestion() {
    const { error } = this.state;
    
    if (!error) return 'Jaribu tena au rudi nyumbani.';
    
    if (error.name === 'ChunkLoadError') {
      return 'Hii inaweza kuwa tatizo la mtandao. Jaribu kurefresh ukurasa.';
    }
    
    if (error.message.includes('Network Error')) {
      return 'Angalia muunganiko wako wa mtandao na ujaribu tena.';
    }
    
    if (error.message.includes('TypeError')) {
      return 'Hitilafu ya data. Jaribu tena au rudi nyumbani.';
    }
    
    return 'Hitilafu isiyotarajiwa imetokea. Jaribu tena au wasiliana na msaada.';
  }

  render() {
    if (this.state.hasError) {
      const { error, errorInfo, showDetails, isReporting, reportSent, retryCount } = this.state;
      const deviceType = detectDeviceType();
      const isMobile = deviceType === 'mobile';

      return (
        <Box
          sx={{
            minHeight: '100vh',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.default',
            p: isMobile ? 2 : 4,
          }}
        >
          <Card
            sx={{
              maxWidth: isMobile ? '100%' : 600,
              width: '100%',
              boxShadow: 3,
            }}
          >
            <CardContent sx={{ p: isMobile ? 2 : 4 }}>
              {/* Error Header */}
              <Box sx={{ textAlign: 'center', mb: 3 }}>
                <BugReport
                  sx={{
                    fontSize: isMobile ? 48 : 64,
                    color: 'error.main',
                    mb: 2,
                  }}
                />
                <Typography
                  variant={isMobile ? 'h5' : 'h4'}
                  sx={{ fontWeight: 'bold', mb: 1 }}
                >
                  Oops! Hitilafu imetokea
                </Typography>
                <Typography
                  variant="body1"
                  color="text.secondary"
                  sx={{ mb: 2 }}
                >
                  {this.getErrorSuggestion()}
                </Typography>
                
                {/* Error Severity */}
                <Chip
                  label={`Kiwango: ${this.getErrorSeverity().toUpperCase()}`}
                  color={this.getSeverityColor()}
                  size="small"
                  sx={{ mb: 2 }}
                />
              </Box>

              {/* Error Actions */}
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: isMobile ? 'column' : 'row',
                  gap: 2,
                  mb: 3,
                }}
              >
                <Button
                  variant="contained"
                  startIcon={<Refresh />}
                  onClick={this.handleRetry}
                  fullWidth={isMobile}
                  sx={{ flex: 1 }}
                >
                  Jaribu Tena
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<Home />}
                  onClick={this.handleGoHome}
                  fullWidth={isMobile}
                  sx={{ flex: 1 }}
                >
                  Rudi Nyumbani
                </Button>
              </Box>

              {/* Report Error */}
              <Box sx={{ mb: 3 }}>
                {!reportSent ? (
                  <Button
                    variant="text"
                    startIcon={<Send />}
                    onClick={this.handleReportError}
                    disabled={isReporting}
                    fullWidth={isMobile}
                    size="small"
                  >
                    {isReporting ? 'Inatuma ripoti...' : 'Tuma ripoti ya hitilafu'}
                  </Button>
                ) : (
                  <Alert severity="success" sx={{ textAlign: 'center' }}>
                    Ripoti imetumwa! Asante kwa msaada wako.
                  </Alert>
                )}
              </Box>

              {/* Retry Count */}
              {retryCount > 0 && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  Majaribio: {retryCount}
                </Alert>
              )}

              {/* Error Details Toggle */}
              <Box sx={{ textAlign: 'center' }}>
                <Button
                  variant="text"
                  size="small"
                  onClick={this.toggleDetails}
                  endIcon={showDetails ? <ExpandLess /> : <ExpandMore />}
                >
                  {showDetails ? 'Ficha' : 'Onyesha'} maelezo ya kiufundi
                </Button>
              </Box>

              {/* Error Details */}
              <Collapse in={showDetails}>
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                    Maelezo ya Hitilafu:
                  </Typography>
                  
                  <Card variant="outlined" sx={{ mb: 2 }}>
                    <CardContent sx={{ p: 2 }}>
                      <Typography variant="caption" color="text.secondary">
                        Error ID: {this.state.errorId}
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', mt: 1 }}>
                        {error?.message}
                      </Typography>
                    </CardContent>
                  </Card>

                  {error?.stack && (
                    <Card variant="outlined" sx={{ mb: 2 }}>
                      <CardContent sx={{ p: 2 }}>
                        <Typography variant="caption" color="text.secondary">
                          Stack Trace:
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            fontFamily: 'monospace',
                            display: 'block',
                            mt: 1,
                            maxHeight: 200,
                            overflow: 'auto',
                            whiteSpace: 'pre-wrap',
                            bgcolor: 'grey.100',
                            p: 1,
                            borderRadius: 1,
                          }}
                        >
                          {error.stack}
                        </Typography>
                      </CardContent>
                    </Card>
                  )}

                  {errorInfo?.componentStack && (
                    <Card variant="outlined">
                      <CardContent sx={{ p: 2 }}>
                        <Typography variant="caption" color="text.secondary">
                          Component Stack:
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            fontFamily: 'monospace',
                            display: 'block',
                            mt: 1,
                            maxHeight: 200,
                            overflow: 'auto',
                            whiteSpace: 'pre-wrap',
                            bgcolor: 'grey.100',
                            p: 1,
                            borderRadius: 1,
                          }}
                        >
                          {errorInfo.componentStack}
                        </Typography>
                      </CardContent>
                    </Card>
                  )}
                </Box>
              </Collapse>

              {/* Footer */}
              <Box sx={{ textAlign: 'center', mt: 3, pt: 2, borderTop: 1, borderColor: 'divider' }}>
                <Typography variant="caption" color="text.secondary">
                  ProChat v1.0.0 • {new Date().toLocaleString('sw-TZ')}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
