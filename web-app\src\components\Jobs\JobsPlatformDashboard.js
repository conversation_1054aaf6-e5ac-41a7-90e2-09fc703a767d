// ProChat AI-Powered Jobs Platform Dashboard
// World's most advanced job platform with AI anti-cheating

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Tabs,
  Tab,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Alert,
  Badge,
  Fab,
  Paper,
  Divider,
} from '@mui/material';
import {
  Work,
  Search,
  Add,
  VideoCall,
  Mic,
  Psychology,
  Security,
  TrendingUp,
  MonetizationOn,
  School,
  Business,
  LocationOn,
  Schedule,
  Star,
  Verified,
  SmartToy,
  Visibility,
  Apply,
  Send,
  FilterList,
  Sort,
} from '@mui/icons-material';

import { useAuth } from '../../contexts/AuthContext';
import { useDeviceDetection } from '../../hooks/useDeviceDetection';
import jobsPlatformService from '../../services/jobsPlatformService';

const JobsPlatformDashboard = () => {
  const { user } = useAuth();
  const { isMobile } = useDeviceDetection();
  const [activeTab, setActiveTab] = useState(0);
  const [jobsData, setJobsData] = useState({});
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [jobDialog, setJobDialog] = useState(false);
  const [applicationDialog, setApplicationDialog] = useState(false);
  const [selectedJob, setSelectedJob] = useState(null);

  useEffect(() => {
    loadJobsData();
  }, [user]);

  const loadJobsData = async () => {
    try {
      setLoading(true);
      const stats = await jobsPlatformService.getJobsPlatformStats();
      const features = {
        jobs: jobsPlatformService.getJobFeatures(),
        aiInterview: jobsPlatformService.getAIInterviewFeatures(),
        career: jobsPlatformService.getCareerFeatures(),
        freelance: jobsPlatformService.getFreelanceFeatures(),
        antiCheat: jobsPlatformService.getAntiCheatFeatures(),
      };

      setJobsData({ stats, features });
    } catch (error) {
      console.error('Failed to load jobs data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleJobApplication = async (jobId, applicationType = 'traditional') => {
    try {
      const result = await jobsPlatformService.submitJobApplication(user.id, jobId, {
        type: applicationType,
        coverLetter: 'AI-generated cover letter based on job requirements...',
      });

      if (result.success) {
        alert(`Application submitted! Match score: ${result.matchScore}%`);
        setApplicationDialog(false);
      } else {
        alert('Application failed: ' + result.reason);
      }
    } catch (error) {
      alert('Error: ' + error.message);
    }
  };

  const StatCard = ({ title, value, subtitle, icon, color = 'primary', trend }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {icon}
          <Typography variant="h6" sx={{ ml: 1, flexGrow: 1 }}>
            {title}
          </Typography>
          {trend && (
            <Chip 
              label={`${trend > 0 ? '+' : ''}${trend}%`}
              color={trend > 0 ? 'success' : 'error'}
              size="small"
            />
          )}
        </Box>
        <Typography variant="h3" color={`${color}.main`} sx={{ fontWeight: 'bold', mb: 1 }}>
          {value}
        </Typography>
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );

  const JobCard = ({ job }) => (
    <Card sx={{ mb: 2, '&:hover': { boxShadow: 3 } }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
            <Business />
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              {job.title}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
                {job.company}
              </Typography>
              <Verified color="primary" sx={{ fontSize: 16, mr: 1 }} />
              <Chip label="Verified" size="small" color="primary" variant="outlined" />
            </Box>
          </Box>
          <Box sx={{ textAlign: 'right' }}>
            <Typography variant="h6" color="success.main" sx={{ fontWeight: 'bold' }}>
              TZS {job.salary?.toLocaleString()}/month
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {job.type}
            </Typography>
          </Box>
        </Box>

        <Typography variant="body2" sx={{ mb: 2 }}>
          {job.description}
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, flexWrap: 'wrap', gap: 1 }}>
          <Chip icon={<LocationOn />} label={job.location} size="small" variant="outlined" />
          <Chip icon={<Schedule />} label={job.posted} size="small" variant="outlined" />
          <Chip icon={<Star />} label={`${job.matchScore}% match`} size="small" color="success" />
          {job.hasVideo && <Chip icon={<VideoCall />} label="Video Job" size="small" color="info" />}
          {job.aiInterview && <Chip icon={<SmartToy />} label="AI Interview" size="small" color="warning" />}
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="contained"
              startIcon={<Apply />}
              onClick={() => {
                setSelectedJob(job);
                setApplicationDialog(true);
              }}
            >
              Quick Apply
            </Button>
            <Button
              variant="outlined"
              startIcon={<Visibility />}
              onClick={() => setJobDialog(true)}
            >
              View Details
            </Button>
          </Box>
          
          <Typography variant="caption" color="text.secondary">
            {job.applicants} applicants
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );

  const FeatureCard = ({ title, description, icon, features, color = 'primary' }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ color: `${color}.main`, mr: 2 }}>
            {icon}
          </Box>
          <Typography variant="h6">{title}</Typography>
        </Box>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {description}
        </Typography>
        
        <List dense>
          {features.slice(0, 4).map((feature, index) => (
            <ListItem key={index} sx={{ px: 0 }}>
              <ListItemIcon sx={{ minWidth: 32 }}>
                <Star sx={{ fontSize: 16, color: `${color}.main` }} />
              </ListItemIcon>
              <ListItemText 
                primary={feature}
                primaryTypographyProps={{ variant: 'body2' }}
              />
            </ListItem>
          ))}
        </List>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" sx={{ mb: 2 }}>Jobs Platform</Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: isMobile ? 2 : 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Work sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          💼 AI-Powered Jobs Platform
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setJobDialog(true)}
        >
          Post Job
        </Button>
      </Box>

      {/* Welcome Message */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="h6">
          🤖 World's First AI-Powered Job Platform with Anti-Cheating Technology!
        </Typography>
        <Typography variant="body2">
          Find jobs, apply with AI assistance, take secure AI interviews, and build your career - all in one platform! 🚀
        </Typography>
      </Alert>

      {/* Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Available Jobs"
            value={jobsData.stats?.totalJobs || 0}
            subtitle="Active job listings"
            icon={<Work color="primary" />}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Applications"
            value={jobsData.stats?.totalApplications || 0}
            subtitle="Total applications submitted"
            icon={<Send color="success" />}
            color="success"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="AI Interviews"
            value={jobsData.stats?.totalInterviews || 0}
            subtitle="Secure AI interviews conducted"
            icon={<SmartToy color="warning" />}
            color="warning"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Success Rate"
            value={`${((jobsData.stats?.successfulPlacements || 0) / Math.max(jobsData.stats?.totalApplications || 1, 1) * 100).toFixed(1)}%`}
            subtitle="Successful job placements"
            icon={<TrendingUp color="info" />}
            color="info"
          />
        </Grid>
      </Grid>

      {/* Search Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            🔍 Find Your Dream Job
          </Typography>
          
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search jobs, companies, or skills..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
            
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Location</InputLabel>
                <Select defaultValue="" label="Location">
                  <MenuItem value="">All Locations</MenuItem>
                  <MenuItem value="dar">Dar es Salaam</MenuItem>
                  <MenuItem value="arusha">Arusha</MenuItem>
                  <MenuItem value="mwanza">Mwanza</MenuItem>
                  <MenuItem value="remote">Remote</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Job Type</InputLabel>
                <Select defaultValue="" label="Job Type">
                  <MenuItem value="">All Types</MenuItem>
                  <MenuItem value="full-time">Full Time</MenuItem>
                  <MenuItem value="part-time">Part Time</MenuItem>
                  <MenuItem value="contract">Contract</MenuItem>
                  <MenuItem value="internship">Internship</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="contained"
                startIcon={<Search />}
                sx={{ height: 56 }}
              >
                Search Jobs
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={activeTab} 
            onChange={(e, newValue) => setActiveTab(newValue)}
            variant={isMobile ? 'scrollable' : 'fullWidth'}
            scrollButtons="auto"
          >
            <Tab label="💼 Browse Jobs" />
            <Tab label="🤖 AI Features" />
            <Tab label="📚 Career Development" />
            <Tab label="💻 Freelance" />
            <Tab label="🛡️ Anti-Cheating" />
          </Tabs>
        </Box>

        <CardContent>
          {/* Browse Jobs Tab */}
          {activeTab === 0 && (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">Featured Jobs</Typography>
                <Box>
                  <IconButton><FilterList /></IconButton>
                  <IconButton><Sort /></IconButton>
                </Box>
              </Box>

              {/* Mock job listings */}
              <JobCard job={{
                title: 'Senior Software Developer',
                company: 'TechCorp Tanzania',
                salary: 2500000,
                type: 'Full Time',
                location: 'Dar es Salaam',
                posted: '2 days ago',
                matchScore: 95,
                hasVideo: true,
                aiInterview: true,
                applicants: 23,
                description: 'We are looking for an experienced software developer to join our growing team...',
              }} />

              <JobCard job={{
                title: 'Digital Marketing Specialist',
                company: 'Marketing Pro Ltd',
                salary: 1800000,
                type: 'Full Time',
                location: 'Remote',
                posted: '1 day ago',
                matchScore: 87,
                hasVideo: false,
                aiInterview: true,
                applicants: 45,
                description: 'Join our dynamic marketing team and help grow our digital presence...',
              }} />

              <JobCard job={{
                title: 'Data Analyst Intern',
                company: 'DataTech Solutions',
                salary: 800000,
                type: 'Internship',
                location: 'Arusha',
                posted: '3 hours ago',
                matchScore: 78,
                hasVideo: true,
                aiInterview: true,
                applicants: 12,
                description: 'Great opportunity for recent graduates to start their career in data analysis...',
              }} />
            </Box>
          )}

          {/* AI Features Tab */}
          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>
                🤖 AI-Powered Job Platform Features
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FeatureCard
                    title="🎤 AI Interview System"
                    description="Advanced AI conducts secure interviews with anti-cheating technology"
                    icon={<SmartToy sx={{ fontSize: 40 }} />}
                    color="primary"
                    features={[
                      'Live Video Interviews',
                      'Recorded Video Responses',
                      'Voice-Only Interviews',
                      'Technical Assessments',
                      'Personality Analysis',
                      'Real-time Scoring',
                    ]}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <FeatureCard
                    title="🛡️ Anti-Cheating Technology"
                    description="Military-grade security ensures fair and honest interviews"
                    icon={<Security sx={{ fontSize: 40 }} />}
                    color="error"
                    features={[
                      'Face Recognition Verification',
                      'Voice Biometric Authentication',
                      'Behavior Monitoring',
                      'Plagiarism Detection',
                      'AI Assistance Detection',
                      'Environment Monitoring',
                    ]}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <FeatureCard
                    title="🎯 Smart Job Matching"
                    description="AI analyzes your profile and matches you with perfect jobs"
                    icon={<Psychology sx={{ fontSize: 40 }} />}
                    color="success"
                    features={[
                      'Skill Compatibility Analysis',
                      'Experience Relevance Scoring',
                      'Cultural Fit Assessment',
                      'Salary Expectation Matching',
                      'Career Path Alignment',
                      'Growth Potential Analysis',
                    ]}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <FeatureCard
                    title="📝 AI Application Assistant"
                    description="AI helps you create perfect applications and cover letters"
                    icon={<Send sx={{ fontSize: 40 }} />}
                    color="warning"
                    features={[
                      'Auto-Generated Cover Letters',
                      'CV Optimization Suggestions',
                      'Application Tracking',
                      'Interview Preparation',
                      'Skill Gap Analysis',
                      'Career Advice',
                    ]}
                  />
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Career Development Tab */}
          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>
                📚 Career Development Platform
              </Typography>
              
              <Alert severity="success" sx={{ mb: 3 }}>
                <Typography variant="body1">
                  <strong>Learn while you earn!</strong> Access professional courses, certifications, and career coaching.
                </Typography>
              </Alert>

              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <School sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                      <Typography variant="h6" sx={{ mb: 1 }}>
                        Online Courses
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        Professional development courses in technology, business, and soft skills
                      </Typography>
                      <Button variant="contained" fullWidth>
                        Browse Courses
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Verified sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
                      <Typography variant="h6" sx={{ mb: 1 }}>
                        Certifications
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        Industry-recognized certifications to boost your career prospects
                      </Typography>
                      <Button variant="contained" color="success" fullWidth>
                        Get Certified
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Psychology sx={{ fontSize: 48, color: 'warning.main', mb: 2 }} />
                      <Typography variant="h6" sx={{ mb: 1 }}>
                        Career Coaching
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        One-on-one career coaching with industry experts and mentors
                      </Typography>
                      <Button variant="contained" color="warning" fullWidth>
                        Find Mentor
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Freelance Tab */}
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>
                💻 Freelance & Gig Marketplace
              </Typography>
              
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body1">
                  <strong>Work on your terms!</strong> Find freelance projects, set your rates, and work remotely.
                </Typography>
              </Alert>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" sx={{ mb: 2 }}>
                    🔥 Popular Categories
                  </Typography>
                  <List>
                    {[
                      { category: 'Web Development', projects: 45, rate: 'TZS 50,000/project' },
                      { category: 'Graphic Design', projects: 32, rate: 'TZS 25,000/project' },
                      { category: 'Content Writing', projects: 28, rate: 'TZS 15,000/article' },
                      { category: 'Digital Marketing', projects: 23, rate: 'TZS 100,000/campaign' },
                    ].map((item, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <MonetizationOn color="success" />
                        </ListItemIcon>
                        <ListItemText
                          primary={item.category}
                          secondary={`${item.projects} active projects • ${item.rate}`}
                        />
                        <ListItemSecondaryAction>
                          <Button size="small" variant="outlined">
                            Browse
                          </Button>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" sx={{ mb: 2 }}>
                    💰 Freelancer Benefits
                  </Typography>
                  <List>
                    <ListItem>
                      <ListItemIcon><Security color="primary" /></ListItemIcon>
                      <ListItemText primary="Secure Payments" secondary="Escrow protection for all projects" />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><TrendingUp color="success" /></ListItemIcon>
                      <ListItemText primary="Global Reach" secondary="Work with clients worldwide" />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Star color="warning" /></ListItemIcon>
                      <ListItemText primary="Rating System" secondary="Build your reputation" />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><MonetizationOn color="info" /></ListItemIcon>
                      <ListItemText primary="Instant Payments" secondary="Get paid immediately" />
                    </ListItem>
                  </List>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Anti-Cheating Tab */}
          {activeTab === 4 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>
                🛡️ Advanced Anti-Cheating Technology
              </Typography>
              
              <Alert severity="warning" sx={{ mb: 3 }}>
                <Typography variant="body1">
                  <strong>Fair interviews guaranteed!</strong> Our AI ensures every interview is honest and secure.
                </Typography>
              </Alert>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 3 }}>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      🔍 Identity Verification
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemIcon><Verified color="success" /></ListItemIcon>
                        <ListItemText primary="Face Recognition" secondary="Match face to ID document" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><Mic color="info" /></ListItemIcon>
                        <ListItemText primary="Voice Biometrics" secondary="Unique voice fingerprinting" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><Security color="primary" /></ListItemIcon>
                        <ListItemText primary="Document Verification" secondary="AI verifies ID authenticity" />
                      </ListItem>
                    </List>
                  </Paper>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 3 }}>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      👁️ Behavior Monitoring
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemIcon><Visibility color="warning" /></ListItemIcon>
                        <ListItemText primary="Eye Tracking" secondary="Monitor where candidate looks" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><SmartToy color="error" /></ListItemIcon>
                        <ListItemText primary="AI Detection" secondary="Detect AI-generated answers" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><Psychology color="secondary" /></ListItemIcon>
                        <ListItemText primary="Stress Analysis" secondary="Monitor stress indicators" />
                      </ListItem>
                    </List>
                  </Paper>
                </Grid>
              </Grid>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Application Dialog */}
      <Dialog open={applicationDialog} onClose={() => setApplicationDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Apply for Job</DialogTitle>
        <DialogContent>
          {selectedJob && (
            <Box>
              <Typography variant="h6" sx={{ mb: 2 }}>
                {selectedJob.title}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                {selectedJob.company}
              </Typography>
              
              <Typography variant="subtitle1" sx={{ mb: 2 }}>
                Choose Application Method:
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<Send />}
                    onClick={() => handleJobApplication(selectedJob.id, 'traditional')}
                    sx={{ mb: 1 }}
                  >
                    Quick Apply (AI Cover Letter)
                  </Button>
                </Grid>
                <Grid item xs={12}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<VideoCall />}
                    onClick={() => handleJobApplication(selectedJob.id, 'video')}
                    sx={{ mb: 1 }}
                  >
                    Video Application
                  </Button>
                </Grid>
                <Grid item xs={12}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<Mic />}
                    onClick={() => handleJobApplication(selectedJob.id, 'voice')}
                  >
                    Voice Application
                  </Button>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApplicationDialog(false)}>Cancel</Button>
        </DialogActions>
      </Dialog>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => setJobDialog(true)}
      >
        <Add />
      </Fab>
    </Box>
  );
};

export default JobsPlatformDashboard;
