@echo off
echo ========================================
echo ProChat MySQL Setup Script
echo ========================================

echo.
echo 1. Stopping MySQL service...
net stop mysql80 2>nul

echo.
echo 2. Starting MySQL in safe mode...
mysqld --skip-grant-tables --skip-networking &

echo.
echo 3. Waiting for MySQL to start...
timeout /t 10 /nobreak >nul

echo.
echo 4. Connecting to MySQL and setting password...
mysql -u root -e "FLUSH PRIVILEGES; ALTER USER 'root'@'localhost' IDENTIFIED BY 'Ram$0101'; FLUSH PRIVILEGES;"

echo.
echo 5. Stopping safe mode MySQL...
taskkill /f /im mysqld.exe 2>nul

echo.
echo 6. Starting MySQL service normally...
net start mysql80

echo.
echo 7. Testing connection...
mysql -u root -p"Ram$0101" -e "SELECT 'MySQL connection successful!' as status;"

echo.
echo 8. Creating ProChat database...
mysql -u root -p"Ram$0101" -e "CREATE DATABASE IF NOT EXISTS prochat_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

echo.
echo 9. Importing database schema...
mysql -u root -p"Ram$0101" prochat_db < database/init_database.sql

echo.
echo ========================================
echo MySQL setup completed successfully!
echo Database: prochat_db
echo Username: root
echo Password: Ram$0101
echo ========================================
pause
