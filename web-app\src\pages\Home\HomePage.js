import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Avatar,
  IconButton,
  Chip,
  Fab,
  SwipeableDrawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  CircularProgress,
} from '@mui/material';
import {
  Favorite,
  FavoriteBorder,
  Comment,
  Share,
  MoreVert,
  Add,
  Photo,
  Videocam,
  Event,
  LocationOn,
  AccessTime,
  Visibility,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useSocket } from '../../contexts/SocketContext';
import { PostsAPI } from '../../services/api';

const HomePage = () => {
  const { user } = useAuth();
  const { isConnected } = useSocket();
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [createDrawerOpen, setCreateDrawerOpen] = useState(false);

  useEffect(() => {
    fetchPosts();
  }, []);

  const fetchPosts = async (pageNum = 0) => {
    try {
      setLoading(true);
      const response = await PostsAPI.getFeed(pageNum, 10);
      
      if (pageNum === 0) {
        setPosts(response.posts);
      } else {
        setPosts(prev => [...prev, ...response.posts]);
      }
      
      setHasMore(response.hasMore);
      setPage(pageNum);
    } catch (error) {
      console.error('Error fetching posts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async (postId) => {
    try {
      await PostsAPI.likePost(postId);
      setPosts(prev => prev.map(post => 
        post.id === postId 
          ? { ...post, liked: !post.liked, likesCount: post.liked ? post.likesCount - 1 : post.likesCount + 1 }
          : post
      ));
    } catch (error) {
      console.error('Error liking post:', error);
    }
  };

  const handleShare = async (postId) => {
    try {
      await PostsAPI.sharePost(postId);
      // Handle share success
    } catch (error) {
      console.error('Error sharing post:', error);
    }
  };

  const formatTime = (timestamp) => {
    const now = new Date();
    const postTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now - postTime) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Sasa hivi';
    if (diffInMinutes < 60) return `${diffInMinutes} dakika zilizopita`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} saa zilizopita`;
    return `${Math.floor(diffInMinutes / 1440)} siku zilizopita`;
  };

  const PostCard = ({ post }) => (
    <Card sx={{ mb: 2, borderRadius: 2 }}>
      {/* Post Header */}
      <CardContent sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar 
            src={post.author.profilePicture} 
            alt={post.author.name}
            sx={{ mr: 2 }}
          >
            {post.author.name.charAt(0)}
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
              {post.author.name}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="caption" color="text.secondary">
                {formatTime(post.createdAt)}
              </Typography>
              {post.location && (
                <>
                  <Typography variant="caption" sx={{ mx: 0.5 }}>•</Typography>
                  <LocationOn sx={{ fontSize: 12, mr: 0.5 }} />
                  <Typography variant="caption" color="text.secondary">
                    {post.location}
                  </Typography>
                </>
              )}
            </Box>
          </Box>
          <IconButton size="small">
            <MoreVert />
          </IconButton>
        </Box>

        {/* Post Content */}
        <Typography variant="body2" sx={{ mb: 2 }}>
          {post.content}
        </Typography>

        {/* Post Tags */}
        {post.tags && post.tags.length > 0 && (
          <Box sx={{ mb: 2 }}>
            {post.tags.map((tag, index) => (
              <Chip 
                key={index}
                label={`#${tag}`}
                size="small"
                variant="outlined"
                sx={{ mr: 1, mb: 1 }}
              />
            ))}
          </Box>
        )}
      </CardContent>

      {/* Post Media */}
      {post.media && post.media.length > 0 && (
        <CardMedia
          component="img"
          image={post.media[0].url}
          alt="Post media"
          sx={{ 
            maxHeight: 400,
            objectFit: 'cover',
          }}
        />
      )}

      {/* Post Actions */}
      <CardContent sx={{ pt: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton 
              size="small" 
              onClick={() => handleLike(post.id)}
              sx={{ color: post.liked ? 'error.main' : 'text.secondary' }}
            >
              {post.liked ? <Favorite /> : <FavoriteBorder />}
            </IconButton>
            <Typography variant="caption" sx={{ mr: 2 }}>
              {post.likesCount}
            </Typography>

            <IconButton size="small" color="primary">
              <Comment />
            </IconButton>
            <Typography variant="caption" sx={{ mr: 2 }}>
              {post.commentsCount}
            </Typography>

            <IconButton size="small" onClick={() => handleShare(post.id)}>
              <Share />
            </IconButton>
            <Typography variant="caption">
              {post.sharesCount}
            </Typography>
          </Box>

          {post.views && (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Visibility sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
              <Typography variant="caption" color="text.secondary">
                {post.views}
              </Typography>
            </Box>
          )}
        </Box>

        {/* Recent Comments Preview */}
        {post.recentComments && post.recentComments.length > 0 && (
          <Box>
            <Divider sx={{ my: 1 }} />
            {post.recentComments.slice(0, 2).map((comment, index) => (
              <Box key={index} sx={{ display: 'flex', mb: 1 }}>
                <Avatar 
                  src={comment.author.profilePicture}
                  sx={{ width: 24, height: 24, mr: 1 }}
                >
                  {comment.author.name.charAt(0)}
                </Avatar>
                <Box>
                  <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                    {comment.author.name}
                  </Typography>
                  <Typography variant="caption" sx={{ ml: 1 }}>
                    {comment.content}
                  </Typography>
                </Box>
              </Box>
            ))}
            {post.commentsCount > 2 && (
              <Typography 
                variant="caption" 
                color="primary" 
                sx={{ cursor: 'pointer' }}
              >
                Ona maoni yote ({post.commentsCount})
              </Typography>
            )}
          </Box>
        )}
      </CardContent>
    </Card>
  );

  const CreateOptions = () => (
    <List>
      <ListItem button onClick={() => setCreateDrawerOpen(false)}>
        <ListItemIcon>
          <Photo color="primary" />
        </ListItemIcon>
        <ListItemText primary="Chapisho la Picha" />
      </ListItem>
      <ListItem button onClick={() => setCreateDrawerOpen(false)}>
        <ListItemIcon>
          <Videocam color="secondary" />
        </ListItemIcon>
        <ListItemText primary="Video" />
      </ListItem>
      <ListItem button onClick={() => setCreateDrawerOpen(false)}>
        <ListItemIcon>
          <Event color="warning" />
        </ListItemIcon>
        <ListItemText primary="Tukio" />
      </ListItem>
    </List>
  );

  return (
    <Box sx={{ pb: 2 }}>
      {/* Connection Status */}
      {!isConnected && (
        <Box 
          sx={{ 
            bgcolor: 'warning.light', 
            color: 'warning.contrastText',
            p: 1,
            textAlign: 'center',
            mb: 2,
          }}
        >
          <Typography variant="caption">
            Hakuna muunganiko wa mtandao. Unaona maudhui ya awali.
          </Typography>
        </Box>
      )}

      {/* Stories Section */}
      <Box sx={{ mb: 3, px: 2 }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
          Hadithi
        </Typography>
        <Box sx={{ display: 'flex', overflowX: 'auto', gap: 2, pb: 1 }}>
          {/* Add Story */}
          <Box sx={{ textAlign: 'center', minWidth: 80 }}>
            <Avatar 
              sx={{ 
                width: 60, 
                height: 60, 
                bgcolor: 'primary.main',
                border: '2px solid',
                borderColor: 'primary.main',
                mb: 1,
              }}
            >
              <Add />
            </Avatar>
            <Typography variant="caption">Ongeza</Typography>
          </Box>

          {/* Mock Stories */}
          {[1, 2, 3, 4, 5].map((story) => (
            <Box key={story} sx={{ textAlign: 'center', minWidth: 80 }}>
              <Avatar 
                sx={{ 
                  width: 60, 
                  height: 60,
                  border: '3px solid',
                  borderColor: 'primary.main',
                  mb: 1,
                }}
                src={`/avatars/user${story}.jpg`}
              >
                U{story}
              </Avatar>
              <Typography variant="caption">Mtumiaji {story}</Typography>
            </Box>
          ))}
        </Box>
      </Box>

      {/* Posts Feed */}
      <Box sx={{ px: 2 }}>
        {loading && posts.length === 0 ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {posts.map((post) => (
              <PostCard key={post.id} post={post} />
            ))}
            
            {loading && (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
                <CircularProgress size={24} />
              </Box>
            )}
            
            {!hasMore && posts.length > 0 && (
              <Typography 
                variant="body2" 
                color="text.secondary" 
                sx={{ textAlign: 'center', py: 2 }}
              >
                Umefika mwisho wa machapisho
              </Typography>
            )}
          </>
        )}
      </Box>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        sx={{
          position: 'fixed',
          bottom: 80,
          right: 16,
          zIndex: 1000,
        }}
        onClick={() => setCreateDrawerOpen(true)}
      >
        <Add />
      </Fab>

      {/* Create Content Drawer */}
      <SwipeableDrawer
        anchor="bottom"
        open={createDrawerOpen}
        onClose={() => setCreateDrawerOpen(false)}
        onOpen={() => setCreateDrawerOpen(true)}
        sx={{
          '& .MuiDrawer-paper': {
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
          },
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
            Unda Maudhui
          </Typography>
          <CreateOptions />
        </Box>
      </SwipeableDrawer>
    </Box>
  );
};

export default HomePage;
