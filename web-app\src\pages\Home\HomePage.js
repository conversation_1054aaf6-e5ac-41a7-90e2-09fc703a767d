import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Avatar,
  IconButton,
  Chip,
  Fab,
  SwipeableDrawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  CircularProgress,
  Button,
  Badge,
} from '@mui/material';
import {
  Favorite,
  FavoriteBorder,
  Comment,
  Share,
  MoreVert,
  Add,
  Photo,
  Videocam,
  Event,
  LocationOn,
  AccessTime,
  Visibility,
  CheckCircle,
  Repeat,
  Gift,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useSocket } from '../../contexts/SocketContext';
import { useDeviceDetection } from '../../hooks/useDeviceDetection';

// Mock data for posts (same as mobile app)
const mockPosts = [
  {
    id: '1',
    user: {
      name: '<PERSON>',
      username: '@john<PERSON><PERSON>i',
      avatar: 'https://via.placeholder.com/40',
      isVerified: true,
    },
    content: 'Habari za asubuhi! Leo ni siku nzuri ya kufanya kazi. #Motivation #Tanzania',
    images: ['https://via.placeholder.com/300x200'],
    timestamp: '2 saa zilizopita',
    likes: 45,
    comments: 12,
    shares: 8,
    gifts: 3,
    impressions: 234,
    isLiked: false,
  },
  {
    id: '2',
    user: {
      name: 'Mary Kimani',
      username: '@marykimani',
      avatar: 'https://via.placeholder.com/40',
      isVerified: false,
    },
    content: 'Nimepata kazi mpya! Asante kwa wote mlionisaidia. 🎉',
    images: [],
    timestamp: '4 saa zilizopita',
    likes: 128,
    comments: 34,
    shares: 15,
    gifts: 7,
    impressions: 567,
    isLiked: true,
  },
  {
    id: '3',
    user: {
      name: 'ProChat Official',
      username: '@prochat',
      avatar: 'https://via.placeholder.com/40',
      isVerified: true,
    },
    content: 'Tumefurahi kuwasilisha kipengele kipya cha ProPay! Sasa unaweza kulipa bili zako moja kwa moja kwenye app. 💰',
    images: ['https://via.placeholder.com/300x200', 'https://via.placeholder.com/300x200'],
    timestamp: '6 saa zilizopita',
    likes: 89,
    comments: 23,
    shares: 45,
    gifts: 12,
    impressions: 890,
    isLiked: false,
  },
];

const HomePage = () => {
  const { user } = useAuth();
  const { isConnected } = useSocket();
  const { isMobile } = useDeviceDetection();
  const [posts, setPosts] = useState(mockPosts);
  const [refreshing, setRefreshing] = useState(false);
  const [createDrawerOpen, setCreateDrawerOpen] = useState(false);

  const handleLike = (postId) => {
    setPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? {
              ...post,
              isLiked: !post.isLiked,
              likes: post.isLiked ? post.likes - 1 : post.likes + 1,
            }
          : post
      )
    );
  };

  const handleRefresh = () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const handleShare = (postId) => {
    console.log('Share post:', postId);
    // Handle share functionality
  };

  const formatTime = (timestamp) => {
    const now = new Date();
    const postTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now - postTime) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Sasa hivi';
    if (diffInMinutes < 60) return `${diffInMinutes} dakika zilizopita`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} saa zilizopita`;
    return `${Math.floor(diffInMinutes / 1440)} siku zilizopita`;
  };

  const PostCard = ({ post }) => (
    <Card sx={{ mb: isMobile ? 1 : 2, borderRadius: isMobile ? 1 : 2, boxShadow: isMobile ? 1 : 2 }}>
      {/* Post Header */}
      <CardContent sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
          <Avatar
            src={post.user.avatar}
            alt={post.user.name}
            sx={{ mr: 2, width: 40, height: 40 }}
          >
            {post.user.name.charAt(0)}
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mr: 0.5 }}>
                {post.user.name}
              </Typography>
              {post.user.isVerified && (
                <CheckCircle sx={{ fontSize: 16, color: 'primary.main' }} />
              )}
            </Box>
            <Typography variant="caption" color="text.secondary">
              {post.user.username}
            </Typography>
          </Box>
          <Box sx={{ textAlign: 'right' }}>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
              {post.timestamp}
            </Typography>
            <IconButton size="small">
              <MoreVert />
            </IconButton>
          </Box>
        </Box>

        {/* Post Content */}
        <Typography variant="body2" sx={{ mb: 2, lineHeight: 1.4 }}>
          {post.content}
        </Typography>
      </CardContent>

      {/* Post Images */}
      {post.images && post.images.length > 0 && (
        <Box sx={{ px: 0 }}>
          {post.images.length === 1 ? (
            <CardMedia
              component="img"
              image={post.images[0]}
              alt="Post image"
              sx={{
                width: '100%',
                height: 200,
                objectFit: 'cover',
              }}
            />
          ) : (
            <Box sx={{ display: 'flex', height: 200 }}>
              {post.images.slice(0, 2).map((image, index) => (
                <Box key={index} sx={{ flex: 1, position: 'relative', mr: index === 0 ? 0.5 : 0 }}>
                  <CardMedia
                    component="img"
                    image={image}
                    alt={`Post image ${index + 1}`}
                    sx={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }}
                  />
                  {index === 1 && post.images.length > 2 && (
                    <Box sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      bgcolor: 'rgba(0,0,0,0.6)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                      <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold' }}>
                        +{post.images.length - 2}
                      </Typography>
                    </Box>
                  )}
                </Box>
              ))}
            </Box>
          )}
        </Box>
      )}

      {/* Post Stats */}
      <CardContent sx={{ pt: 1, pb: 1 }}>
        <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
          {post.impressions} maoni • {post.likes} likes • {post.comments} majibu
        </Typography>

        {/* Post Actions */}
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          pt: 1,
          borderTop: '1px solid',
          borderColor: 'divider',
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton
              size="small"
              onClick={() => handleLike(post.id)}
              sx={{ color: post.isLiked ? 'error.main' : 'text.secondary', mr: 0.5 }}
            >
              {post.isLiked ? <Favorite /> : <FavoriteBorder />}
            </IconButton>
            <Typography variant="caption" sx={{ mr: 2 }}>
              {post.likes}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton size="small" color="primary" sx={{ mr: 0.5 }}>
              <Comment />
            </IconButton>
            <Typography variant="caption" sx={{ mr: 2 }}>
              {post.comments}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton size="small" onClick={() => handleShare(post.id)} sx={{ mr: 0.5 }}>
              <Repeat />
            </IconButton>
            <Typography variant="caption" sx={{ mr: 2 }}>
              {post.shares}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton size="small" sx={{ mr: 0.5 }}>
              <Gift />
            </IconButton>
            <Typography variant="caption" sx={{ mr: 2 }}>
              {post.gifts}
            </Typography>
          </Box>

          <IconButton size="small" onClick={() => handleShare(post.id)}>
            <Share />
          </IconButton>
        </Box>
      </CardContent>
    </Card>
  );

  const CreateOptions = () => (
    <List>
      <ListItem button onClick={() => setCreateDrawerOpen(false)}>
        <ListItemIcon>
          <Photo color="primary" />
        </ListItemIcon>
        <ListItemText primary="Chapisho la Picha" />
      </ListItem>
      <ListItem button onClick={() => setCreateDrawerOpen(false)}>
        <ListItemIcon>
          <Videocam color="secondary" />
        </ListItemIcon>
        <ListItemText primary="Video" />
      </ListItem>
      <ListItem button onClick={() => setCreateDrawerOpen(false)}>
        <ListItemIcon>
          <Event color="warning" />
        </ListItemIcon>
        <ListItemText primary="Tukio" />
      </ListItem>
    </List>
  );

  return (
    <Box sx={{ pb: isMobile ? 2 : 0 }}>
      {/* Connection Status */}
      {!isConnected && (
        <Box
          sx={{
            bgcolor: 'warning.light',
            color: 'warning.contrastText',
            p: 1,
            textAlign: 'center',
            mb: 2,
          }}
        >
          <Typography variant="caption">
            Hakuna muunganiko wa mtandao. Unaona maudhui ya awali.
          </Typography>
        </Box>
      )}

      {/* Stories Section (Mobile only) */}
      {isMobile && (
        <Box sx={{ mb: 2, px: 2 }}>
          <Box sx={{ display: 'flex', overflowX: 'auto', gap: 2, pb: 1 }}>
            {/* Add Story */}
            <Box sx={{ textAlign: 'center', minWidth: 70 }}>
              <Avatar
                sx={{
                  width: 56,
                  height: 56,
                  bgcolor: 'primary.main',
                  border: '2px solid',
                  borderColor: 'primary.main',
                  mb: 1,
                  mx: 'auto',
                }}
              >
                <Add />
              </Avatar>
              <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                Hadithi yako
              </Typography>
            </Box>

            {/* Mock Stories */}
            {[1, 2, 3, 4, 5, 6, 7].map((story) => (
              <Box key={story} sx={{ textAlign: 'center', minWidth: 70 }}>
                <Avatar
                  sx={{
                    width: 56,
                    height: 56,
                    border: '3px solid',
                    borderColor: 'primary.main',
                    mb: 1,
                    mx: 'auto',
                  }}
                  src={`https://via.placeholder.com/56?text=U${story}`}
                >
                  U{story}
                </Avatar>
                <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                  User {story}
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>
      )}

      {/* Posts Feed */}
      <Box sx={{ px: isMobile ? 1 : 2 }}>
        {refreshing ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
            <CircularProgress size={24} />
          </Box>
        ) : (
          <>
            {posts.map((post) => (
              <PostCard key={post.id} post={post} />
            ))}

            <Box sx={{ textAlign: 'center', py: 2 }}>
              <Button
                variant="outlined"
                onClick={handleRefresh}
                disabled={refreshing}
              >
                {refreshing ? 'Inapakia...' : 'Pakia zaidi'}
              </Button>
            </Box>
          </>
        )}
      </Box>

      {/* Floating Action Button (Mobile only) */}
      {isMobile && (
        <Fab
          color="primary"
          sx={{
            position: 'fixed',
            bottom: 80,
            right: 16,
            zIndex: 1000,
          }}
          onClick={() => setCreateDrawerOpen(true)}
        >
          <Add />
        </Fab>
      )}

      {/* Create Content Drawer */}
      <SwipeableDrawer
        anchor="bottom"
        open={createDrawerOpen}
        onClose={() => setCreateDrawerOpen(false)}
        onOpen={() => setCreateDrawerOpen(true)}
        sx={{
          '& .MuiDrawer-paper': {
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
          },
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
            Unda Maudhui
          </Typography>
          <CreateOptions />
        </Box>
      </SwipeableDrawer>
    </Box>
  );
};

export default HomePage;
