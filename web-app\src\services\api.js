import axios from 'axios';
import toast from 'react-hot-toast';

// Create axios instance
export const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('prochat_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add request timestamp
    config.metadata = { startTime: new Date() };

    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response) => {
    // Calculate request duration
    const duration = new Date() - response.config.metadata.startTime;
    console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`);
    
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, error.response?.data);

    // Handle 401 Unauthorized
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        // Try to refresh token
        const refreshResponse = await apiClient.post('/auth/refresh');
        const { token } = refreshResponse.data;
        
        // Update token
        localStorage.setItem('prochat_token', token);
        apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        originalRequest.headers['Authorization'] = `Bearer ${token}`;
        
        // Retry original request
        return apiClient(originalRequest);
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('prochat_token');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    // Handle network errors
    if (!error.response) {
      toast.error('Hakuna muunganiko wa mtandao');
    } else {
      // Handle other errors
      const message = error.response.data?.message || 'Hitilafu imetokea';
      
      // Don't show toast for certain endpoints
      const silentEndpoints = ['/auth/me', '/auth/refresh'];
      const isSilent = silentEndpoints.some(endpoint => 
        error.config?.url?.includes(endpoint)
      );
      
      if (!isSilent) {
        toast.error(message);
      }
    }

    return Promise.reject(error);
  }
);

// API Service Classes
export class AuthAPI {
  static async login(credentials) {
    const response = await apiClient.post('/auth/login', credentials);
    return response.data;
  }

  static async register(userData) {
    const response = await apiClient.post('/auth/register', userData);
    return response.data;
  }

  static async logout() {
    const response = await apiClient.post('/auth/logout');
    return response.data;
  }

  static async getProfile() {
    const response = await apiClient.get('/auth/me');
    return response.data;
  }

  static async updateProfile(profileData) {
    const response = await apiClient.put('/auth/profile', profileData);
    return response.data;
  }

  static async changePassword(passwordData) {
    const response = await apiClient.put('/auth/password', passwordData);
    return response.data;
  }

  static async refreshToken() {
    const response = await apiClient.post('/auth/refresh');
    return response.data;
  }
}

export class PostsAPI {
  static async getFeed(page = 0, size = 20) {
    const response = await apiClient.get(`/posts/feed?page=${page}&size=${size}`);
    return response.data;
  }

  static async createPost(postData) {
    const response = await apiClient.post('/posts', postData);
    return response.data;
  }

  static async updatePost(postId, postData) {
    const response = await apiClient.put(`/posts/${postId}`, postData);
    return response.data;
  }

  static async deletePost(postId) {
    const response = await apiClient.delete(`/posts/${postId}`);
    return response.data;
  }

  static async likePost(postId) {
    const response = await apiClient.post(`/posts/${postId}/like`);
    return response.data;
  }

  static async commentOnPost(postId, comment) {
    const response = await apiClient.post(`/posts/${postId}/comments`, { content: comment });
    return response.data;
  }

  static async sharePost(postId) {
    const response = await apiClient.post(`/posts/${postId}/share`);
    return response.data;
  }
}

export class ChatAPI {
  static async getConversations() {
    const response = await apiClient.get('/chat/conversations');
    return response.data;
  }

  static async getMessages(conversationId, page = 0, size = 50) {
    const response = await apiClient.get(`/chat/conversations/${conversationId}/messages?page=${page}&size=${size}`);
    return response.data;
  }

  static async sendMessage(conversationId, messageData) {
    const response = await apiClient.post(`/chat/conversations/${conversationId}/messages`, messageData);
    return response.data;
  }

  static async createConversation(participantIds) {
    const response = await apiClient.post('/chat/conversations', { participantIds });
    return response.data;
  }

  static async markAsRead(conversationId) {
    const response = await apiClient.put(`/chat/conversations/${conversationId}/read`);
    return response.data;
  }
}

export class EventsAPI {
  static async getEvents(page = 0, size = 20, category = null) {
    const params = new URLSearchParams({ page, size });
    if (category) params.append('category', category);
    
    const response = await apiClient.get(`/events?${params}`);
    return response.data;
  }

  static async getEventById(eventId) {
    const response = await apiClient.get(`/events/${eventId}`);
    return response.data;
  }

  static async createEvent(eventData) {
    const response = await apiClient.post('/events', eventData);
    return response.data;
  }

  static async attendEvent(eventId) {
    const response = await apiClient.post(`/events/${eventId}/attend`);
    return response.data;
  }

  static async cancelAttendance(eventId) {
    const response = await apiClient.delete(`/events/${eventId}/attend`);
    return response.data;
  }
}

export class JobsAPI {
  static async getJobs(page = 0, size = 20, category = null, location = null) {
    const params = new URLSearchParams({ page, size });
    if (category) params.append('category', category);
    if (location) params.append('location', location);
    
    const response = await apiClient.get(`/jobs?${params}`);
    return response.data;
  }

  static async getJobById(jobId) {
    const response = await apiClient.get(`/jobs/${jobId}`);
    return response.data;
  }

  static async applyForJob(jobId, applicationData) {
    const response = await apiClient.post(`/jobs/${jobId}/apply`, applicationData);
    return response.data;
  }

  static async saveJob(jobId) {
    const response = await apiClient.post(`/jobs/${jobId}/save`);
    return response.data;
  }
}

export class NewsAPI {
  static async getNews(page = 0, size = 20, category = null) {
    const params = new URLSearchParams({ page, size });
    if (category) params.append('category', category);
    
    const response = await apiClient.get(`/news?${params}`);
    return response.data;
  }

  static async getNewsById(newsId) {
    const response = await apiClient.get(`/news/${newsId}`);
    return response.data;
  }

  static async bookmarkNews(newsId) {
    const response = await apiClient.post(`/news/${newsId}/bookmark`);
    return response.data;
  }
}

export class ProPayAPI {
  static async getWallet() {
    const response = await apiClient.get('/propay/wallet');
    return response.data;
  }

  static async getTransactions(page = 0, size = 20) {
    const response = await apiClient.get(`/propay/transactions?page=${page}&size=${size}`);
    return response.data;
  }

  static async sendMoney(recipientData) {
    const response = await apiClient.post('/propay/send', recipientData);
    return response.data;
  }

  static async requestMoney(requestData) {
    const response = await apiClient.post('/propay/request', requestData);
    return response.data;
  }

  static async topUp(amount, method) {
    const response = await apiClient.post('/propay/topup', { amount, method });
    return response.data;
  }

  static async withdraw(amount, method) {
    const response = await apiClient.post('/propay/withdraw', { amount, method });
    return response.data;
  }
}

// File upload utility
export const uploadFile = async (file, type = 'general') => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', type);

  const response = await apiClient.post('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      console.log(`Upload progress: ${percentCompleted}%`);
    },
  });

  return response.data;
};

export default apiClient;
