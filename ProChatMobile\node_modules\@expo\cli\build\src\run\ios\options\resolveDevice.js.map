{"version": 3, "sources": ["../../../../../src/run/ios/options/resolveDevice.ts"], "sourcesContent": ["// import { resolveDestinationsAsync } from './appleDestinations';\nimport { promptDeviceAsync } from './promptDevice';\nimport * as Log from '../../../log';\nimport {\n  AppleDeviceManager,\n  ensureSimulatorOpenAsync,\n} from '../../../start/platforms/ios/AppleDeviceManager';\nimport { sortDefaultDeviceToBeginningAsync } from '../../../start/platforms/ios/promptAppleDevice';\nimport { OSType } from '../../../start/platforms/ios/simctl';\nimport * as SimControl from '../../../start/platforms/ios/simctl';\nimport { uniqBy } from '../../../utils/array';\nimport { CommandError } from '../../../utils/errors';\nimport { profile } from '../../../utils/profile';\nimport { logDeviceArgument } from '../../hints';\nimport { BuildProps } from '../XcodeBuild.types';\nimport * as AppleDevice from '../appleDevice/AppleDevice';\n\ntype AnyDevice = {\n  name: string;\n  osType: OSType;\n  osVersion: string;\n  udid: string;\n  deviceType?: string;\n};\n// type AnyDevice = SimControl.Device | AppleDevice.ConnectedDevice;\n\n/** Get a list of devices (called destinations) that are connected to the host machine. Filter by `osType` if defined. */\nasync function getDevicesAsync({\n  osType,\n  // ...buildProps\n}: { osType?: OSType } & Pick<BuildProps, 'xcodeProject' | 'scheme' | 'configuration'>): Promise<\n  AnyDevice[]\n> {\n  const devices = await sortDefaultDeviceToBeginningAsync(\n    uniqBy(\n      (\n        await Promise.all([\n          AppleDevice.getConnectedDevicesAsync(),\n          await profile(SimControl.getDevicesAsync)(),\n          // resolveDestinationsAsync(buildProps),\n        ])\n      ).flat(),\n      (item) => item.udid\n    ),\n    osType\n  );\n\n  // Sort devices to top of front of the list\n\n  const physical: AnyDevice[] = [];\n\n  const simulators = devices.filter((device) => {\n    if ('isAvailable' in device) {\n      return true;\n    } else {\n      physical.push(device);\n      return false;\n    }\n  });\n\n  const isPhone = (a: any) => a.osType === 'iOS';\n  const sorted = [\n    ...physical.sort((a, b) => {\n      const aPhone = isPhone(a);\n      const bPhone = isPhone(b);\n      if (aPhone && !bPhone) return -1;\n      if (!aPhone && bPhone) return 1;\n\n      return 0;\n    }),\n    ...simulators,\n  ];\n\n  // If osType is defined, then filter out ineligible simulators.\n  // Only do this inside of the device selection so users who pass the entire device udid can attempt to select any simulator (even if it's invalid).\n  return osType ? filterDevicesForOsType(sorted, osType) : sorted;\n}\n\n/** @returns a list of devices, filtered by the provided `osType`. */\nfunction filterDevicesForOsType<TDevice extends { osType: OSType }>(\n  devices: TDevice[],\n  osType: OSType\n): TDevice[] {\n  return devices.filter((device) => {\n    if (osType === 'iOS') {\n      // Compatible devices for iOS builds\n      return ['iOS', 'macOS', 'xrOS'].includes(device.osType);\n    }\n    return device.osType === osType;\n  });\n}\n\n/** Given a `device` argument from the CLI, parse and prompt our way to a usable device for building. */\nexport async function resolveDeviceAsync(\n  device: string | boolean | undefined,\n  buildProps: { osType?: OSType } & Pick<BuildProps, 'xcodeProject' | 'scheme' | 'configuration'>\n): Promise<AnyDevice> {\n  await AppleDeviceManager.assertSystemRequirementsAsync();\n\n  if (!device) {\n    /** Finds the first possible device and returns in a booted state. */\n    const manager = await AppleDeviceManager.resolveAsync({\n      device: {\n        osType: buildProps.osType,\n      },\n    });\n    Log.debug(\n      `Resolved default device (name: ${manager.device.name}, udid: ${manager.device.udid}, osType: ${buildProps.osType})`\n    );\n    return manager.device;\n  }\n\n  const devices = await getDevicesAsync(buildProps);\n\n  const resolved =\n    device === true\n      ? // `--device` (no props after)\n        // @ts-expect-error\n        await promptDeviceAsync(devices)\n      : // `--device <name|udid>`\n        findDeviceFromSearchValue(devices, device.toLowerCase());\n\n  return ensureBootedAsync(resolved);\n}\n\n/** @returns `true` if the given device is a simulator. */\nexport function isSimulatorDevice(device: AnyDevice): boolean {\n  return (\n    !('deviceType' in device) ||\n    !!device.deviceType?.startsWith?.('com.apple.CoreSimulator.SimDeviceType.')\n  );\n}\n\n/** @returns device matching the `searchValue` against name or UDID. */\nfunction findDeviceFromSearchValue(devices: AnyDevice[], searchValue: string): AnyDevice {\n  const device = devices.find(\n    (device) =>\n      device.udid.toLowerCase() === searchValue || device.name.toLowerCase() === searchValue\n  );\n  if (!device) {\n    throw new CommandError('BAD_ARGS', `No device UDID or name matching \"${searchValue}\"`);\n  }\n  return device;\n}\n\n/** Ensures the device is booted if it's a simulator. */\nasync function ensureBootedAsync(device: AnyDevice): Promise<AnyDevice> {\n  // --device with no props after\n  logDeviceArgument(device.udid);\n  if (isSimulatorDevice(device)) {\n    return ensureSimulatorOpenAsync({ udid: device.udid });\n  }\n  return device;\n}\n"], "names": ["isSimulatorDevice", "resolveDeviceAsync", "getDevicesAsync", "osType", "devices", "sortDefaultDeviceToBeginningAsync", "uniqBy", "Promise", "all", "AppleDevice", "getConnectedDevicesAsync", "profile", "SimControl", "flat", "item", "udid", "physical", "simulators", "filter", "device", "push", "isPhone", "a", "sorted", "sort", "b", "aPhone", "bPhone", "filterDevicesForOsType", "includes", "buildProps", "AppleDeviceManager", "assertSystemRequirementsAsync", "manager", "resolveAsync", "Log", "debug", "name", "resolved", "promptDeviceAsync", "findDeviceFromSearchValue", "toLowerCase", "ensureBootedAsync", "deviceType", "startsWith", "searchValue", "find", "CommandError", "logDeviceArgument", "ensureSimulatorOpenAsync"], "mappings": "AAAA,kEAAkE;;;;;;;;;;;;IA8HlDA,iBAAiB;eAAjBA;;IAjCMC,kBAAkB;eAAlBA;;;8BA5FY;6DACb;oCAId;mCAC2C;gEAEtB;uBACL;wBACM;yBACL;uBACU;qEAEL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7B,oEAAoE;AAEpE,uHAAuH,GACvH,eAAeC,gBAAgB,EAC7BC,MAAM,EAE8E;IAGpF,MAAMC,UAAU,MAAMC,IAAAA,oDAAiC,EACrDC,IAAAA,aAAM,EACJ,AACE,CAAA,MAAMC,QAAQC,GAAG,CAAC;QAChBC,aAAYC,wBAAwB;QACpC,MAAMC,IAAAA,gBAAO,EAACC,QAAWV,eAAe;KAEzC,CAAA,EACDW,IAAI,IACN,CAACC,OAASA,KAAKC,IAAI,GAErBZ;IAGF,2CAA2C;IAE3C,MAAMa,WAAwB,EAAE;IAEhC,MAAMC,aAAab,QAAQc,MAAM,CAAC,CAACC;QACjC,IAAI,iBAAiBA,QAAQ;YAC3B,OAAO;QACT,OAAO;YACLH,SAASI,IAAI,CAACD;YACd,OAAO;QACT;IACF;IAEA,MAAME,UAAU,CAACC,IAAWA,EAAEnB,MAAM,KAAK;IACzC,MAAMoB,SAAS;WACVP,SAASQ,IAAI,CAAC,CAACF,GAAGG;YACnB,MAAMC,SAASL,QAAQC;YACvB,MAAMK,SAASN,QAAQI;YACvB,IAAIC,UAAU,CAACC,QAAQ,OAAO,CAAC;YAC/B,IAAI,CAACD,UAAUC,QAAQ,OAAO;YAE9B,OAAO;QACT;WACGV;KACJ;IAED,+DAA+D;IAC/D,mJAAmJ;IACnJ,OAAOd,SAASyB,uBAAuBL,QAAQpB,UAAUoB;AAC3D;AAEA,mEAAmE,GACnE,SAASK,uBACPxB,OAAkB,EAClBD,MAAc;IAEd,OAAOC,QAAQc,MAAM,CAAC,CAACC;QACrB,IAAIhB,WAAW,OAAO;YACpB,oCAAoC;YACpC,OAAO;gBAAC;gBAAO;gBAAS;aAAO,CAAC0B,QAAQ,CAACV,OAAOhB,MAAM;QACxD;QACA,OAAOgB,OAAOhB,MAAM,KAAKA;IAC3B;AACF;AAGO,eAAeF,mBACpBkB,MAAoC,EACpCW,UAA+F;IAE/F,MAAMC,sCAAkB,CAACC,6BAA6B;IAEtD,IAAI,CAACb,QAAQ;QACX,mEAAmE,GACnE,MAAMc,UAAU,MAAMF,sCAAkB,CAACG,YAAY,CAAC;YACpDf,QAAQ;gBACNhB,QAAQ2B,WAAW3B,MAAM;YAC3B;QACF;QACAgC,KAAIC,KAAK,CACP,CAAC,+BAA+B,EAAEH,QAAQd,MAAM,CAACkB,IAAI,CAAC,QAAQ,EAAEJ,QAAQd,MAAM,CAACJ,IAAI,CAAC,UAAU,EAAEe,WAAW3B,MAAM,CAAC,CAAC,CAAC;QAEtH,OAAO8B,QAAQd,MAAM;IACvB;IAEA,MAAMf,UAAU,MAAMF,gBAAgB4B;IAEtC,MAAMQ,WACJnB,WAAW,OAEP,mBAAmB;IACnB,MAAMoB,IAAAA,+BAAiB,EAACnC,WAExBoC,0BAA0BpC,SAASe,OAAOsB,WAAW;IAE3D,OAAOC,kBAAkBJ;AAC3B;AAGO,SAAStC,kBAAkBmB,MAAiB;QAG7CA,+BAAAA;IAFJ,OACE,CAAE,CAAA,gBAAgBA,MAAK,KACvB,CAAC,GAACA,qBAAAA,OAAOwB,UAAU,sBAAjBxB,gCAAAA,mBAAmByB,UAAU,qBAA7BzB,mCAAAA,oBAAgC;AAEtC;AAEA,qEAAqE,GACrE,SAASqB,0BAA0BpC,OAAoB,EAAEyC,WAAmB;IAC1E,MAAM1B,SAASf,QAAQ0C,IAAI,CACzB,CAAC3B,SACCA,OAAOJ,IAAI,CAAC0B,WAAW,OAAOI,eAAe1B,OAAOkB,IAAI,CAACI,WAAW,OAAOI;IAE/E,IAAI,CAAC1B,QAAQ;QACX,MAAM,IAAI4B,oBAAY,CAAC,YAAY,CAAC,iCAAiC,EAAEF,YAAY,CAAC,CAAC;IACvF;IACA,OAAO1B;AACT;AAEA,sDAAsD,GACtD,eAAeuB,kBAAkBvB,MAAiB;IAChD,+BAA+B;IAC/B6B,IAAAA,wBAAiB,EAAC7B,OAAOJ,IAAI;IAC7B,IAAIf,kBAAkBmB,SAAS;QAC7B,OAAO8B,IAAAA,4CAAwB,EAAC;YAAElC,MAAMI,OAAOJ,IAAI;QAAC;IACtD;IACA,OAAOI;AACT"}