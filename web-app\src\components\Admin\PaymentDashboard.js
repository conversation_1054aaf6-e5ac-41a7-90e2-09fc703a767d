// ProChat Payment Dashboard Component
// Admin dashboard for monitoring all payment activities

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  LinearProgress,
} from '@mui/material';
import {
  Payment,
  TrendingUp,
  TrendingDown,
  AccountBalance,
  Phone,
  CreditCard,
  CurrencyBitcoin,
  Refresh,
  Download,
  Visibility,
  Edit,
  Warning,
  CheckCircle,
  Error,
  Schedule,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Pie<PERSON>hart, Pie, Cell, BarChart, Bar } from 'recharts';
import paymentAPI from '../../api/paymentAPI';

const PaymentDashboard = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({});
  const [transactions, setTransactions] = useState([]);
  const [providers, setProviders] = useState([]);
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [showTransactionDialog, setShowTransactionDialog] = useState(false);
  const [filters, setFilters] = useState({
    provider: '',
    status: '',
    startDate: null,
    endDate: null,
    page: 1,
    limit: 20,
  });

  useEffect(() => {
    loadDashboardData();
  }, [filters]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load payment statistics
      const statsResponse = await paymentAPI.getPaymentStats(filters);
      setStats(statsResponse);

      // Load recent transactions
      const transactionsResponse = await paymentAPI.getPaymentHistory(filters);
      setTransactions(transactionsResponse.transactions || []);

      // Load provider performance
      const providersResponse = await paymentAPI.getAvailableProviders();
      setProviders(providersResponse.providers || {});

    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value,
      page: 1, // Reset to first page when filters change
    }));
  };

  const handleTransactionClick = (transaction) => {
    setSelectedTransaction(transaction);
    setShowTransactionDialog(true);
  };

  const getStatusColor = (status) => {
    const colors = {
      completed: 'success',
      pending: 'warning',
      processing: 'info',
      failed: 'error',
      cancelled: 'default',
      refunded: 'secondary',
    };
    return colors[status] || 'default';
  };

  const getStatusIcon = (status) => {
    const icons = {
      completed: <CheckCircle />,
      pending: <Schedule />,
      processing: <CircularProgress size={16} />,
      failed: <Error />,
      cancelled: <Warning />,
      refunded: <TrendingDown />,
    };
    return icons[status] || <Schedule />;
  };

  const getProviderIcon = (provider) => {
    if (['mpesa', 'airtel_money', 'tigo_pesa', 'halopesa', 'azampesa', 'nara'].includes(provider)) {
      return <Phone />;
    } else if (['crdb_bank', 'nmb_bank', 'nbc_bank'].includes(provider)) {
      return <AccountBalance />;
    } else if (['visa', 'mastercard', 'stripe'].includes(provider)) {
      return <CreditCard />;
    } else if (['bitcoin', 'usdt'].includes(provider)) {
      return <CurrencyBitcoin />;
    }
    return <Payment />;
  };

  const formatCurrency = (amount, currency = 'TZS') => {
    return `${currency} ${amount.toLocaleString()}`;
  };

  const exportTransactions = async () => {
    try {
      // Mock export functionality
      const csvData = transactions.map(t => ({
        ID: t.id,
        Amount: t.amount,
        Currency: t.currency,
        Provider: t.provider,
        Status: t.status,
        Date: new Date(t.createdAt).toLocaleDateString(),
      }));
      
      console.log('Exporting transactions:', csvData);
      alert('Export functionality would be implemented here');
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  // Mock chart data
  const chartData = [
    { name: 'Jan', amount: 4000000, transactions: 240 },
    { name: 'Feb', amount: 3000000, transactions: 198 },
    { name: 'Mar', amount: 5000000, transactions: 320 },
    { name: 'Apr', amount: 4500000, transactions: 280 },
    { name: 'May', amount: 6000000, transactions: 390 },
    { name: 'Jun', amount: 5500000, transactions: 350 },
  ];

  const providerData = [
    { name: 'M-Pesa', value: 35, color: '#00C851' },
    { name: 'Airtel Money', value: 25, color: '#FF4444' },
    { name: 'Tigo Pesa', value: 20, color: '#33B5E5' },
    { name: 'Banks', value: 15, color: '#FF8800' },
    { name: 'Cards', value: 5, color: '#AA66CC' },
  ];

  const renderOverviewTab = () => (
    <Grid container spacing={3}>
      {/* Summary Cards */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography color="textSecondary" gutterBottom>
                  Jumla ya Malipo
                </Typography>
                <Typography variant="h5">
                  {formatCurrency(stats.totalVolume || 0)}
                </Typography>
              </Box>
              <TrendingUp sx={{ fontSize: 40, color: 'success.main' }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography color="textSecondary" gutterBottom>
                  Miamala ya Leo
                </Typography>
                <Typography variant="h5">
                  {stats.todayTransactions || 0}
                </Typography>
              </Box>
              <Payment sx={{ fontSize: 40, color: 'primary.main' }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography color="textSecondary" gutterBottom>
                  Kiwango cha Mafanikio
                </Typography>
                <Typography variant="h5">
                  {((stats.successfulTransactions / stats.totalTransactions) * 100 || 0).toFixed(1)}%
                </Typography>
              </Box>
              <CheckCircle sx={{ fontSize: 40, color: 'success.main' }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography color="textSecondary" gutterBottom>
                  Miamala Inayosubiri
                </Typography>
                <Typography variant="h5">
                  {stats.pendingTransactions || 0}
                </Typography>
              </Box>
              <Schedule sx={{ fontSize: 40, color: 'warning.main' }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* Charts */}
      <Grid item xs={12} md={8}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Mwenendo wa Malipo
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value, name) => [
                  name === 'amount' ? formatCurrency(value) : value,
                  name === 'amount' ? 'Kiasi' : 'Miamala'
                ]} />
                <Legend />
                <Line type="monotone" dataKey="amount" stroke="#8884d8" name="Kiasi" />
                <Line type="monotone" dataKey="transactions" stroke="#82ca9d" name="Miamala" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Watoa Huduma
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={providerData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}%`}
                >
                  {providerData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderTransactionsTab = () => (
    <Box>
      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Mtoa Huduma</InputLabel>
                <Select
                  value={filters.provider}
                  onChange={(e) => handleFilterChange('provider', e.target.value)}
                  label="Mtoa Huduma"
                >
                  <MenuItem value="">Wote</MenuItem>
                  <MenuItem value="mpesa">M-Pesa</MenuItem>
                  <MenuItem value="airtel_money">Airtel Money</MenuItem>
                  <MenuItem value="tigo_pesa">Tigo Pesa</MenuItem>
                  <MenuItem value="crdb_bank">CRDB Bank</MenuItem>
                  <MenuItem value="visa">Visa</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Hali</InputLabel>
                <Select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  label="Hali"
                >
                  <MenuItem value="">Zote</MenuItem>
                  <MenuItem value="completed">Imekamilika</MenuItem>
                  <MenuItem value="pending">Inasubiri</MenuItem>
                  <MenuItem value="failed">Imeshindwa</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <Button
                variant="contained"
                startIcon={<Refresh />}
                onClick={loadDashboardData}
                disabled={loading}
              >
                Onyesha Upya
              </Button>
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <Button
                variant="outlined"
                startIcon={<Download />}
                onClick={exportTransactions}
              >
                Pakua
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Transactions Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Miamala ya Hivi Karibuni
          </Typography>
          
          {loading ? (
            <LinearProgress />
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>ID</TableCell>
                    <TableCell>Kiasi</TableCell>
                    <TableCell>Mtoa Huduma</TableCell>
                    <TableCell>Hali</TableCell>
                    <TableCell>Tarehe</TableCell>
                    <TableCell>Vitendo</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>{transaction.id}</TableCell>
                      <TableCell>
                        {formatCurrency(transaction.amount, transaction.currency)}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {getProviderIcon(transaction.provider)}
                          <Typography sx={{ ml: 1 }}>
                            {transaction.provider}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={getStatusIcon(transaction.status)}
                          label={transaction.status}
                          color={getStatusColor(transaction.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {new Date(transaction.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <Button
                          size="small"
                          startIcon={<Visibility />}
                          onClick={() => handleTransactionClick(transaction)}
                        >
                          Ona
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>
    </Box>
  );

  const renderProvidersTab = () => (
    <Grid container spacing={3}>
      {Object.entries(providers).map(([category, categoryProviders]) => (
        <Grid item xs={12} key={category}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {category === 'mobileMoneyProviders' && 'Malipo ya Simu'}
                {category === 'bankingProviders' && 'Malipo ya Benki'}
                {category === 'cardProviders' && 'Kadi za Kimataifa'}
                {category === 'cryptoProviders' && 'Sarafu za Kidijitali'}
              </Typography>
              <Grid container spacing={2}>
                {Object.keys(categoryProviders || {}).map((provider) => (
                  <Grid item xs={12} sm={6} md={4} key={provider}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          {getProviderIcon(provider)}
                          <Typography variant="h6" sx={{ ml: 1 }}>
                            {provider}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          Status: Inatumika
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Miamala ya Leo: 150
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Kiwango cha Mafanikio: 98%
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <Payment sx={{ mr: 2 }} />
        Dashibodi ya Malipo
      </Typography>

      <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)} sx={{ mb: 3 }}>
        <Tab label="Muhtasari" />
        <Tab label="Miamala" />
        <Tab label="Watoa Huduma" />
      </Tabs>

      {activeTab === 0 && renderOverviewTab()}
      {activeTab === 1 && renderTransactionsTab()}
      {activeTab === 2 && renderProvidersTab()}

      {/* Transaction Details Dialog */}
      <Dialog
        open={showTransactionDialog}
        onClose={() => setShowTransactionDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Maelezo ya Muamala
        </DialogTitle>
        <DialogContent>
          {selectedTransaction && (
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">ID ya Muamala</Typography>
                <Typography variant="body1">{selectedTransaction.id}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Kiasi</Typography>
                <Typography variant="body1">
                  {formatCurrency(selectedTransaction.amount, selectedTransaction.currency)}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Mtoa Huduma</Typography>
                <Typography variant="body1">{selectedTransaction.provider}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Hali</Typography>
                <Chip
                  label={selectedTransaction.status}
                  color={getStatusColor(selectedTransaction.status)}
                  size="small"
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">Maelezo</Typography>
                <Typography variant="body1">
                  {selectedTransaction.description || 'Hakuna maelezo'}
                </Typography>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowTransactionDialog(false)}>
            Funga
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PaymentDashboard;
