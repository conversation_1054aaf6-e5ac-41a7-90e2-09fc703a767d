import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline, Box } from '@mui/material';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';

// Context Providers
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { SocketProvider } from './contexts/SocketContext';

// Components
import LoadingScreen from './components/LoadingScreen/LoadingScreen';
import MobileLayout from './components/Layout/MobileLayout';
import DesktopLayout from './components/Layout/DesktopLayout';
import ErrorBoundary from './components/ErrorBoundary/ErrorBoundary';

// Admin Components
import AdminLogin from './components/Admin/AdminLogin';
import AdminDashboard from './components/Admin/AdminDashboard';
import AdminRoute from './components/Admin/AdminRoute';
import SecurityDashboard from './components/Security/SecurityDashboard';

// Engagement & Referral Components
import EngagementDashboard from './components/Engagement/EngagementDashboard';
import ReferralDashboard from './components/Referral/ReferralDashboard';
import NotificationSettings from './components/Settings/NotificationSettings';

// Super App Components
import SuperAppDashboard from './components/SuperApp/SuperAppDashboard';

// Global Super App Components
import GlobalSuperAppDashboard from './components/Global/GlobalSuperAppDashboard';

// Social Commerce Components
import SocialCommerceDashboard from './components/SocialCommerce/SocialCommerceDashboard';
import ProductPost from './components/SocialCommerce/ProductPost';

// Jobs Platform Components
import JobsPlatformDashboard from './components/Jobs/JobsPlatformDashboard';

// Hooks
import { useDeviceDetection } from './hooks/useDeviceDetection';

// Cross-Platform Configuration
import {
  createResponsiveTheme,
  detectDeviceType,
  SECURITY_HEADERS
} from './config/responsive';
import {
  registerServiceWorker,
  setupPWAInstallPrompt,
  setupNetworkDetection
} from './config/firebase';
import syncService from './services/syncService';
import analyticsService from './services/analyticsService';
import notificationService from './services/notificationService';
import offlineStorage from './services/offlineStorage';
import fraudDetectionService from './services/fraudDetection';
import walletSecurityService from './services/walletSecurity';
import threatIntelligenceService from './services/threatIntelligence';
import complianceAuditService from './services/complianceAudit';
import backupRecoveryService from './services/backupRecovery';
import adminSecurityService from './services/adminSecurity';
import intrusionDetectionService from './services/intrusionDetection';
import engagementRewardsService from './services/engagementRewards';
import smartNotificationService from './services/smartNotifications';
import referralBonusService from './services/referralBonusSystem';
import superAppIntegrationService from './services/superAppIntegration';
import globalSuperAppService from './services/globalSuperAppService';
import socialCommerceService from './services/socialCommerceService';
import jobsPlatformService from './services/jobsPlatformService';
import { SecurityLogger } from './config/security';
import {
  ipWhitelistManager,
  deviceAuthManager,
  rbacManager,
  emergencyModeManager
} from './config/bankingSecurity';

// Auth Pages
import LoginPage from './pages/Auth/LoginPage';
import RegisterPage from './pages/Auth/RegisterPage';
import WelcomePage from './pages/Auth/WelcomePage';
import ForgotPasswordPage from './pages/Auth/ForgotPasswordPage';

// Main Tab Pages (same as mobile)
import ChatsPage from './pages/Chats/ChatsPage';
import HomePage from './pages/Home/HomePage';
import DiscoverPage from './pages/Discover/DiscoverPage';
import MePage from './pages/Me/MePage';

// New Tabs Container
import TabsContainer from './components/Tabs/TabsContainer';

// Chat Pages
import ChatPage from './pages/Chat/ChatPage';
import ContactsPage from './pages/Contacts/ContactsPage';

// Home Pages
import CreatePostPage from './pages/Home/CreatePostPage';
import PostDetailsPage from './pages/Home/PostDetailsPage';

// Discover Pages
import NewsDetailsPage from './pages/Discover/NewsDetailsPage';
import VideoPlayerPage from './pages/Discover/VideoPlayerPage';
import LiveStreamPage from './pages/Discover/LiveStreamPage';
import EventDetailsPage from './pages/Discover/EventDetailsPage';
import TicketPage from './pages/Discover/TicketPage';
import JobDetailsPage from './pages/Discover/JobDetailsPage';

// Me/Profile Pages
import ProfilePage from './pages/Me/ProfilePage';
import EditProfilePage from './pages/Me/EditProfilePage';
import ProPayPage from './pages/Me/ProPayPage';
import ProZonePage from './pages/Me/ProZonePage';
import WalletPage from './pages/Me/WalletPage';
import TransactionHistoryPage from './pages/Me/TransactionHistoryPage';
import SettingsPage from './pages/Me/SettingsPage';
import NotificationsPage from './pages/Me/NotificationsPage';
import InviteFriendsPage from './pages/Me/InviteFriendsPage';
import HelpSupportPage from './pages/Me/HelpSupportPage';

// Wallet Pages
import SendMoneyPage from './pages/Wallet/SendMoneyPage';

// Social Pages
import GiftPage from './pages/Social/GiftPage';
import DonationPage from './pages/Social/DonationPage';

// Task Pages
import TaskPage from './pages/Tasks/TaskPage';

// Enhanced Desktop Dashboard (only for desktop)
import DashboardPage from './pages/Dashboard/DashboardPage';

// Cross-Platform Theme Configuration
const getTheme = (deviceType, darkMode = false) => {
  return createResponsiveTheme(deviceType, darkMode);
};

// React Query Client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Protected Route Component
function ProtectedRoute({ children }) {
  const { isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) {
    return <LoadingScreen />;
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return children;
}

// Public Route Component (redirect if authenticated)
function PublicRoute({ children }) {
  const { isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) {
    return <LoadingScreen />;
  }
  
  if (isAuthenticated) {
    return <Navigate to="/" replace />;
  }
  
  return children;
}

// Main App Content
function AppContent() {
  const { isMobile, isTablet, isDesktop } = useDeviceDetection();
  const { isAuthenticated, isLoading } = useAuth();
  const [deviceType, setDeviceType] = useState(detectDeviceType());
  const [darkMode, setDarkMode] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [syncStatus, setSyncStatus] = useState({ connected: false });

  useEffect(() => {
    // Initialize cross-platform features
    initializeCrossPlatformFeatures();

    // Setup device type detection
    const handleResize = () => {
      setDeviceType(detectDeviceType());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const initializeCrossPlatformFeatures = async () => {
    try {
      // Register service worker for PWA
      await registerServiceWorker();

      // Setup PWA install prompt
      setupPWAInstallPrompt();

      // Setup network detection
      const cleanup = setupNetworkDetection(
        () => setIsOnline(true),
        () => setIsOnline(false)
      );

      // Setup sync service listeners
      syncService.on('connection-status', setSyncStatus);

      // Initialize analytics
      if (isAuthenticated) {
        analyticsService.setUserId(localStorage.getItem('prochat_user_id'));
      }

      // Initialize notifications
      await notificationService.requestPermission();

      // Initialize offline storage
      await offlineStorage.init();

      // Initialize banking-grade security
      await this.initializeBankingSecurity();

      // Load user preferences
      const savedDarkMode = localStorage.getItem('prochat_dark_mode') === 'true';
      setDarkMode(savedDarkMode);

      SecurityLogger.logSecurityEvent('APP_INITIALIZED', {
        deviceType,
        userAgent: navigator.userAgent,
        platform: 'web',
        services: {
          sync: syncStatus.connected,
          notifications: notificationService.getPermissionStatus().granted,
          analytics: analyticsService.isEnabled,
          offlineStorage: offlineStorage.isInitialized,
        },
      });

      return cleanup;
    } catch (error) {
      console.error('Failed to initialize cross-platform features:', error);
    }
  };

  const initializeBankingSecurity = async () => {
    try {
      // Check IP access for current session
      const ipCheck = await ipWhitelistManager.checkIPAccess();
      console.log('IP Access Check:', ipCheck);

      // Authorize current device
      const deviceAuth = await deviceAuthManager.authorizeDevice();
      console.log('Device Authorization:', deviceAuth);

      // Setup user role if authenticated
      if (isAuthenticated) {
        const userId = localStorage.getItem('prochat_user_id');
        if (userId) {
          // Assign default role (in production, get from backend)
          rbacManager.assignRole(userId, 'user');

          // Track user login for fraud detection
          fraudDetectionService.trackUserAction(userId, 'login', {
            deviceId: deviceAuth.deviceId,
            ip: ipCheck.ip,
            platform: 'web',
          });
        }
      }

      // Check emergency mode status
      const emergencyStatus = emergencyModeManager.getEmergencyStatus();
      if (emergencyStatus.active) {
        console.warn('🚨 Emergency Mode Active:', emergencyStatus.reason);
      }

      // Initialize advanced security services
      await this.initializeAdvancedSecurity();

      SecurityLogger.logSecurityEvent('BANKING_SECURITY_INITIALIZED', {
        ipAllowed: ipCheck.allowed,
        deviceAuthorized: deviceAuth.authorized,
        emergencyMode: emergencyStatus.active,
        fraudDetectionEnabled: fraudDetectionService.isEnabled,
        threatIntelligenceEnabled: threatIntelligenceService.isMonitoring,
        complianceAuditEnabled: true,
        backupRecoveryEnabled: true,
      });

    } catch (error) {
      console.error('Failed to initialize banking security:', error);
      SecurityLogger.logSecurityEvent('BANKING_SECURITY_INIT_FAILED', {
        error: error.message,
      });
    }
  };

  const initializeAdvancedSecurity = async () => {
    try {
      // Initialize threat intelligence monitoring
      console.log('🛡️ Initializing Threat Intelligence...');

      // Initialize compliance audit system
      console.log('📋 Initializing Compliance Audit...');
      complianceAuditService.logAuditEvent('system_startup', {
        userId: localStorage.getItem('prochat_user_id'),
        sessionId: analyticsService.sessionId,
        ipAddress: await ipWhitelistManager.getCurrentIP(),
        userAgent: navigator.userAgent,
        platform: 'web',
        timestamp: new Date().toISOString(),
      });

      // Initialize backup and recovery system
      console.log('💾 Initializing Backup & Recovery...');
      const backupStatus = backupRecoveryService.getBackupStatus();
      console.log('Backup Status:', backupStatus);

      // Setup request monitoring for threat intelligence
      this.setupRequestMonitoring();

      // Setup compliance monitoring
      this.setupComplianceMonitoring();

      // Setup automated backups
      this.setupAutomatedBackups();

      // Initialize admin security monitoring
      console.log('👑 Initializing Admin Security...');
      const adminStats = adminSecurityService.getAdminSecurityStats();
      console.log('Admin Security Stats:', adminStats);

      // Initialize intrusion detection system
      console.log('🛡️ Initializing Anti-Intrusion System...');
      const intrusionStats = intrusionDetectionService.getIntrusionStatistics();
      console.log('Intrusion Detection Stats:', intrusionStats);

      // Initialize engagement & rewards system
      console.log('🎯 Initializing Engagement & Rewards...');
      const engagementStats = engagementRewardsService.getEngagementStats();
      console.log('Engagement Stats:', engagementStats);

      // Initialize smart notifications
      console.log('🔔 Initializing Smart Notifications...');
      const notificationStats = smartNotificationService.getNotificationStats();
      console.log('Notification Stats:', notificationStats);

      // Initialize referral & bonus system
      console.log('🤝 Initializing Referral & Bonus System...');
      const referralStats = await referralBonusService.getSystemReferralStats();
      console.log('Referral Stats:', referralStats);

      // Initialize super app integration
      console.log('🚀 Initializing Super App Integration...');
      const superAppStats = await superAppIntegrationService.getSuperAppStats();
      console.log('Super App Stats:', superAppStats);

      // Initialize global super app features
      console.log('🌍 Initializing Global Super App Features...');
      const globalStats = await globalSuperAppService.getGlobalStats();
      console.log('Global Stats:', globalStats);

      // Initialize social commerce
      console.log('🛒 Initializing Social Commerce...');
      const commerceStats = await socialCommerceService.getSocialCommerceStats();
      console.log('Commerce Stats:', commerceStats);

      // Initialize jobs platform
      console.log('💼 Initializing AI Jobs Platform...');
      const jobsStats = await jobsPlatformService.getJobsPlatformStats();
      console.log('Jobs Stats:', jobsStats);

      SecurityLogger.logSecurityEvent('ADVANCED_SECURITY_INITIALIZED', {
        threatIntelligence: true,
        complianceAudit: true,
        backupRecovery: true,
        adminSecurity: true,
        intrusionDetection: true,
        engagementRewards: true,
        smartNotifications: true,
        referralBonusSystem: true,
        superAppIntegration: true,
        adminSessions: adminStats.activeSessions,
        activeThreats: intrusionStats.activeAlerts,
        totalUsers: engagementStats.totalUsers,
        pendingNotifications: notificationStats.pendingNotifications,
        totalReferrals: referralStats.totalUsers,
        totalBonusesAwarded: referralStats.totalBonusesAwarded,
        totalSocialPayments: superAppStats.totalSocialPayments,
        activeGroupWallets: superAppStats.activeGroupWallets,
        totalCreators: superAppStats.totalCreators,
        supportedCurrencies: globalStats.supportedCurrencies,
        supportedLanguages: globalStats.supportedLanguages,
        crossBorderTransactions: globalStats.crossBorderTransactions,
        globalUsers: globalStats.globalUsers,
        totalProducts: commerceStats.totalProducts,
        totalBusinesses: commerceStats.totalBusinesses,
        totalOrders: commerceStats.totalOrders,
        socialCommerceEnabled: true,
        totalJobs: jobsStats.totalJobs,
        totalJobApplications: jobsStats.totalApplications,
        totalAIInterviews: jobsStats.totalInterviews,
        jobsPlatformEnabled: true,
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      console.error('Failed to initialize advanced security:', error);
      SecurityLogger.logSecurityEvent('ADVANCED_SECURITY_INIT_FAILED', {
        error: error.message,
      });
    }
  };

  const setupRequestMonitoring = () => {
    // Monitor all fetch requests for threats
    const originalFetch = window.fetch;

    window.fetch = async function(...args) {
      const [url, options = {}] = args;

      // Analyze request for threats
      const requestData = {
        url: typeof url === 'string' ? url : url.url,
        method: options.method || 'GET',
        headers: options.headers || {},
        body: options.body,
        ip: await ipWhitelistManager.getCurrentIP(),
        userAgent: navigator.userAgent,
        userId: localStorage.getItem('prochat_user_id'),
        timestamp: Date.now(),
      };

      const threatAnalysis = threatIntelligenceService.analyzeRequest(requestData);

      if (!threatAnalysis.safe) {
        console.warn('🚨 Threat detected in request:', threatAnalysis.threats);

        // Block high-risk requests
        if (threatAnalysis.riskScore > 0.8) {
          throw new Error('Request blocked due to security threat');
        }
      }

      // Proceed with original request
      return originalFetch.apply(this, args);
    };
  };

  const setupComplianceMonitoring = () => {
    // Monitor user actions for compliance
    const monitorUserAction = (action, details) => {
      complianceAuditService.logAuditEvent(action, {
        ...details,
        userId: localStorage.getItem('prochat_user_id'),
        sessionId: analyticsService.sessionId,
        ipAddress: 'pending', // Will be resolved async
        userAgent: navigator.userAgent,
        platform: 'web',
        timestamp: new Date().toISOString(),
      });
    };

    // Monitor clicks
    document.addEventListener('click', (event) => {
      const target = event.target;
      const userId = localStorage.getItem('prochat_user_id');

      if (target.dataset.sensitive === 'true') {
        monitorUserAction('sensitive_data_access', {
          element: target.tagName,
          elementId: target.id,
          elementClass: target.className,
        });
      }

      // Track engagement actions
      if (userId && target.dataset.action) {
        const actionType = target.dataset.action;
        engagementRewardsService.trackUserAction(userId, actionType, {
          element: target.tagName,
          elementId: target.id,
        });
      }
    });

    // Monitor form submissions
    document.addEventListener('submit', (event) => {
      const form = event.target;
      monitorUserAction('form_submission', {
        formId: form.id,
        formAction: form.action,
        formMethod: form.method,
      });
    });
  };

  const setupAutomatedBackups = () => {
    // Setup periodic data backup
    setInterval(() => {
      // Backup critical user data
      const criticalData = {
        userId: localStorage.getItem('prochat_user_id'),
        sessionData: {
          sessionId: analyticsService.sessionId,
          startTime: analyticsService.startTime,
          deviceType: deviceType,
        },
        securityData: {
          deviceId: localStorage.getItem('prochat_device_id'),
          lastLogin: localStorage.getItem('prochat_last_login'),
        },
      };

      // Store in offline storage as backup
      offlineStorage.add('session_backups', {
        ...criticalData,
        timestamp: new Date().toISOString(),
        backupType: 'session_data',
      });

    }, 300000); // Every 5 minutes
  };

  if (isLoading) {
    return <LoadingScreen />;
  }

  // Choose layout based on device
  const Layout = isMobile ? MobileLayout : DesktopLayout;

  return (
    <Box sx={{ height: '100vh', overflow: 'hidden' }}>
      <Routes>
        {/* Admin Routes */}
        <Route path="/admin/login" element={<AdminLogin />} />
        <Route path="/admin/dashboard" element={
          <AdminRoute>
            <AdminDashboard />
          </AdminRoute>
        } />
        <Route path="/admin/security" element={
          <AdminRoute requiredPermissions={['admin.security.view']}>
            <SecurityDashboard />
          </AdminRoute>
        } />

        {/* Public Routes */}
        <Route path="/welcome" element={
          <PublicRoute>
            <WelcomePage />
          </PublicRoute>
        } />
        <Route path="/login" element={
          <PublicRoute>
            <LoginPage />
          </PublicRoute>
        } />
        <Route path="/register" element={
          <PublicRoute>
            <RegisterPage />
          </PublicRoute>
        } />
        <Route path="/forgot-password" element={
          <PublicRoute>
            <ForgotPasswordPage />
          </PublicRoute>
        } />

        {/* Protected Routes */}
        <Route path="/*" element={
          <ProtectedRoute>
            <Routes>
              {/* Main App with Tabs */}
              <Route path="/" element={<TabsContainer />} />
              <Route path="/home" element={<TabsContainer />} />
              <Route path="/chats" element={<TabsContainer />} />
              <Route path="/discover" element={<TabsContainer />} />
              <Route path="/me" element={<TabsContainer />} />

              {/* Legacy routes for compatibility */}
              <Route path="/legacy/*" element={
                <Layout>
                  <Routes>
                    <Route path="/dashboard" element={<DashboardPage />} />

                    {/* Chat Routes */}
                    <Route path="/chat/:chatId" element={<ChatPage />} />
                    <Route path="/contacts" element={<ContactsPage />} />

                {/* Home Routes */}
                <Route path="/create-post" element={<CreatePostPage />} />
                <Route path="/post/:postId" element={<PostDetailsPage />} />

                {/* Discover Routes */}
                <Route path="/news/:newsId" element={<NewsDetailsPage />} />
                <Route path="/video/:videoId" element={<VideoPlayerPage />} />
                <Route path="/live/:streamId" element={<LiveStreamPage />} />
                <Route path="/event/:eventId" element={<EventDetailsPage />} />
                <Route path="/ticket/:ticketId" element={<TicketPage />} />
                <Route path="/job/:jobId" element={<JobDetailsPage />} />

                {/* Me/Profile Routes */}
                <Route path="/profile" element={<ProfilePage />} />
                <Route path="/edit-profile" element={<EditProfilePage />} />
                <Route path="/propay" element={<ProPayPage />} />
                <Route path="/prozone" element={<ProZonePage />} />
                <Route path="/wallet" element={<WalletPage />} />
                <Route path="/transaction-history" element={<TransactionHistoryPage />} />
                <Route path="/settings" element={<SettingsPage />} />
                <Route path="/notifications" element={<NotificationsPage />} />
                <Route path="/invite-friends" element={<InviteFriendsPage />} />
                <Route path="/help-support" element={<HelpSupportPage />} />

                {/* Wallet Routes */}
                <Route path="/send-money" element={<SendMoneyPage />} />

                {/* Social Routes */}
                <Route path="/gift" element={<GiftPage />} />
                <Route path="/donation" element={<DonationPage />} />

                {/* Referral & Bonus Routes */}
                <Route path="/referral" element={<ReferralDashboard />} />
                <Route path="/invite" element={<ReferralDashboard />} />
                <Route path="/join" element={<JoinWithReferralPage />} />
                <Route path="/engagement" element={<EngagementDashboard />} />
                <Route path="/notification-settings" element={<NotificationSettings />} />

                {/* Super App Routes */}
                <Route path="/super-app" element={<SuperAppDashboard />} />
                <Route path="/social-wallet" element={<SuperAppDashboard />} />
                <Route path="/creator-economy" element={<SuperAppDashboard />} />
                <Route path="/social-commerce" element={<SuperAppDashboard />} />
                <Route path="/group-finance" element={<SuperAppDashboard />} />

                {/* Global Super App Routes */}
                <Route path="/global" element={<GlobalSuperAppDashboard />} />
                <Route path="/global-features" element={<GlobalSuperAppDashboard />} />
                <Route path="/ai-features" element={<GlobalSuperAppDashboard />} />
                <Route path="/blockchain" element={<GlobalSuperAppDashboard />} />
                <Route path="/global-payments" element={<GlobalSuperAppDashboard />} />
                <Route path="/voice-ai" element={<GlobalSuperAppDashboard />} />
                <Route path="/offline-mode" element={<GlobalSuperAppDashboard />} />
                <Route path="/education" element={<GlobalSuperAppDashboard />} />
                <Route path="/global-marketplace" element={<GlobalSuperAppDashboard />} />

                {/* Social Commerce Routes */}
                <Route path="/social-commerce" element={<SocialCommerceDashboard />} />
                <Route path="/my-store" element={<SocialCommerceDashboard />} />
                <Route path="/business-dashboard" element={<SocialCommerceDashboard />} />
                <Route path="/product-management" element={<SocialCommerceDashboard />} />
                <Route path="/order-management" element={<SocialCommerceDashboard />} />
                <Route path="/commerce-analytics" element={<SocialCommerceDashboard />} />

                {/* Jobs Platform Routes */}
                <Route path="/jobs" element={<JobsPlatformDashboard />} />
                <Route path="/find-jobs" element={<JobsPlatformDashboard />} />
                <Route path="/post-job" element={<JobsPlatformDashboard />} />
                <Route path="/ai-interview" element={<JobsPlatformDashboard />} />
                <Route path="/career-development" element={<JobsPlatformDashboard />} />
                <Route path="/freelance" element={<JobsPlatformDashboard />} />
                <Route path="/job-applications" element={<JobsPlatformDashboard />} />
                <Route path="/interview-results" element={<JobsPlatformDashboard />} />

                {/* Task Routes */}
                <Route path="/tasks" element={<TaskPage />} />

                {/* Enhanced Desktop Dashboard */}
                <Route path="/dashboard" element={<DashboardPage />} />

                    {/* Fallback */}
                    <Route path="*" element={<Navigate to="/" replace />} />
                  </Routes>
                </Layout>
              } />

              {/* Fallback for main routes */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </ProtectedRoute>
        } />
      </Routes>
    </Box>
  );
}

// Main App Component with Cross-Platform Support
function App() {
  const [deviceType, setDeviceType] = useState(detectDeviceType());
  const [darkMode, setDarkMode] = useState(() => {
    const saved = localStorage.getItem('prochat_dark_mode');
    return saved ? JSON.parse(saved) : false;
  });

  useEffect(() => {
    // Apply security headers (for development)
    if (process.env.NODE_ENV === 'development') {
      Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
        document.querySelector('meta[http-equiv="' + key + '"]')?.setAttribute('content', value);
      });
    }

    // Device type detection
    const handleResize = () => {
      const newDeviceType = detectDeviceType();
      if (newDeviceType !== deviceType) {
        setDeviceType(newDeviceType);
        SecurityLogger.logSecurityEvent('DEVICE_TYPE_CHANGED', {
          from: deviceType,
          to: newDeviceType,
        });
      }
    };

    window.addEventListener('resize', handleResize);

    // Initialize app
    SecurityLogger.logSecurityEvent('APP_STARTED', {
      deviceType,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
    });

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [deviceType]);

  // Toggle dark mode
  const toggleDarkMode = () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);
    localStorage.setItem('prochat_dark_mode', JSON.stringify(newDarkMode));

    SecurityLogger.logSecurityEvent('THEME_CHANGED', {
      darkMode: newDarkMode,
      deviceType,
    });
  };

  // Get responsive theme
  const theme = getTheme(deviceType, darkMode);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider theme={theme}>
          <CssBaseline />
          <AuthProvider>
            <SocketProvider>
              <Router>
                <AppContent />
              </Router>
            </SocketProvider>
          </AuthProvider>

        {/* Enhanced Toast Notifications */}
        <Toaster
          position={deviceType === 'mobile' ? 'top-center' : 'top-right'}
          toastOptions={{
            duration: 4000,
            style: {
              background: darkMode ? '#2D2D2D' : '#363636',
              color: '#fff',
              borderRadius: deviceType === 'mobile' ? '12px' : '8px',
              fontSize: deviceType === 'mobile' ? '14px' : '16px',
            },
            success: {
              iconTheme: {
                primary: theme.palette.success.main,
                secondary: '#fff',
              },
            },
            error: {
              iconTheme: {
                primary: theme.palette.error.main,
                secondary: '#fff',
              },
            },
          }}
        />

        {/* PWA Install Button (hidden by default, shown by setupPWAInstallPrompt) */}
        <button
          id="pwa-install-button"
          style={{
            display: 'none',
            position: 'fixed',
            bottom: '20px',
            left: '20px',
            zIndex: 9999,
            padding: '12px 24px',
            backgroundColor: theme.palette.primary.main,
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: 'bold',
          }}
        >
          Install ProChat
        </button>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
