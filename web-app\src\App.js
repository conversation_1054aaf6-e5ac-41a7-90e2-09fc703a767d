import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline, Box } from '@mui/material';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';

// Context Providers
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { SocketProvider } from './contexts/SocketContext';

// Components
import LoadingScreen from './components/LoadingScreen/LoadingScreen';
import MobileLayout from './components/Layout/MobileLayout';
import DesktopLayout from './components/Layout/DesktopLayout';

// Hooks
import { useDeviceDetection } from './hooks/useDeviceDetection';

// Cross-Platform Configuration
import {
  createResponsiveTheme,
  detectDeviceType,
  SECURITY_HEADERS
} from './config/responsive';
import {
  registerServiceWorker,
  setupPWAInstallPrompt,
  setupNetworkDetection
} from './config/firebase';
import syncService from './services/syncService';
import { SecurityLogger } from './config/security';

// Auth Pages
import LoginPage from './pages/Auth/LoginPage';
import RegisterPage from './pages/Auth/RegisterPage';
import WelcomePage from './pages/Auth/WelcomePage';
import ForgotPasswordPage from './pages/Auth/ForgotPasswordPage';

// Main Tab Pages (same as mobile)
import ChatsPage from './pages/Chats/ChatsPage';
import HomePage from './pages/Home/HomePage';
import DiscoverPage from './pages/Discover/DiscoverPage';
import MePage from './pages/Me/MePage';

// Chat Pages
import ChatPage from './pages/Chat/ChatPage';
import ContactsPage from './pages/Contacts/ContactsPage';

// Home Pages
import CreatePostPage from './pages/Home/CreatePostPage';
import PostDetailsPage from './pages/Home/PostDetailsPage';

// Discover Pages
import NewsDetailsPage from './pages/Discover/NewsDetailsPage';
import VideoPlayerPage from './pages/Discover/VideoPlayerPage';
import LiveStreamPage from './pages/Discover/LiveStreamPage';
import EventDetailsPage from './pages/Discover/EventDetailsPage';
import TicketPage from './pages/Discover/TicketPage';
import JobDetailsPage from './pages/Discover/JobDetailsPage';

// Me/Profile Pages
import ProfilePage from './pages/Me/ProfilePage';
import EditProfilePage from './pages/Me/EditProfilePage';
import ProPayPage from './pages/Me/ProPayPage';
import ProZonePage from './pages/Me/ProZonePage';
import WalletPage from './pages/Me/WalletPage';
import TransactionHistoryPage from './pages/Me/TransactionHistoryPage';
import SettingsPage from './pages/Me/SettingsPage';
import NotificationsPage from './pages/Me/NotificationsPage';
import InviteFriendsPage from './pages/Me/InviteFriendsPage';
import HelpSupportPage from './pages/Me/HelpSupportPage';

// Wallet Pages
import SendMoneyPage from './pages/Wallet/SendMoneyPage';

// Social Pages
import GiftPage from './pages/Social/GiftPage';
import DonationPage from './pages/Social/DonationPage';

// Task Pages
import TaskPage from './pages/Tasks/TaskPage';

// Enhanced Desktop Dashboard (only for desktop)
import DashboardPage from './pages/Dashboard/DashboardPage';

// Cross-Platform Theme Configuration
const getTheme = (deviceType, darkMode = false) => {
  return createResponsiveTheme(deviceType, darkMode);
};

// React Query Client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Protected Route Component
function ProtectedRoute({ children }) {
  const { isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) {
    return <LoadingScreen />;
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return children;
}

// Public Route Component (redirect if authenticated)
function PublicRoute({ children }) {
  const { isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) {
    return <LoadingScreen />;
  }
  
  if (isAuthenticated) {
    return <Navigate to="/" replace />;
  }
  
  return children;
}

// Main App Content
function AppContent() {
  const { isMobile, isTablet, isDesktop } = useDeviceDetection();
  const { isAuthenticated, isLoading } = useAuth();
  const [deviceType, setDeviceType] = useState(detectDeviceType());
  const [darkMode, setDarkMode] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [syncStatus, setSyncStatus] = useState({ connected: false });

  useEffect(() => {
    // Initialize cross-platform features
    initializeCrossPlatformFeatures();

    // Setup device type detection
    const handleResize = () => {
      setDeviceType(detectDeviceType());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const initializeCrossPlatformFeatures = async () => {
    try {
      // Register service worker for PWA
      await registerServiceWorker();

      // Setup PWA install prompt
      setupPWAInstallPrompt();

      // Setup network detection
      const cleanup = setupNetworkDetection(
        () => setIsOnline(true),
        () => setIsOnline(false)
      );

      // Setup sync service listeners
      syncService.on('connection-status', setSyncStatus);

      // Load user preferences
      const savedDarkMode = localStorage.getItem('prochat_dark_mode') === 'true';
      setDarkMode(savedDarkMode);

      SecurityLogger.logSecurityEvent('APP_INITIALIZED', {
        deviceType,
        userAgent: navigator.userAgent,
        platform: 'web',
      });

      return cleanup;
    } catch (error) {
      console.error('Failed to initialize cross-platform features:', error);
    }
  };

  if (isLoading) {
    return <LoadingScreen />;
  }

  // Choose layout based on device
  const Layout = isMobile ? MobileLayout : DesktopLayout;

  return (
    <Box sx={{ height: '100vh', overflow: 'hidden' }}>
      <Routes>
        {/* Public Routes */}
        <Route path="/welcome" element={
          <PublicRoute>
            <WelcomePage />
          </PublicRoute>
        } />
        <Route path="/login" element={
          <PublicRoute>
            <LoginPage />
          </PublicRoute>
        } />
        <Route path="/register" element={
          <PublicRoute>
            <RegisterPage />
          </PublicRoute>
        } />
        <Route path="/forgot-password" element={
          <PublicRoute>
            <ForgotPasswordPage />
          </PublicRoute>
        } />

        {/* Protected Routes */}
        <Route path="/*" element={
          <ProtectedRoute>
            <Layout>
              <Routes>
                {/* Main Tab Routes (same as mobile) */}
                <Route path="/" element={isMobile ? <HomePage /> : <DashboardPage />} />
                <Route path="/chats" element={<ChatsPage />} />
                <Route path="/home" element={<HomePage />} />
                <Route path="/discover" element={<DiscoverPage />} />
                <Route path="/me" element={<MePage />} />

                {/* Chat Routes */}
                <Route path="/chat/:chatId" element={<ChatPage />} />
                <Route path="/contacts" element={<ContactsPage />} />

                {/* Home Routes */}
                <Route path="/create-post" element={<CreatePostPage />} />
                <Route path="/post/:postId" element={<PostDetailsPage />} />

                {/* Discover Routes */}
                <Route path="/news/:newsId" element={<NewsDetailsPage />} />
                <Route path="/video/:videoId" element={<VideoPlayerPage />} />
                <Route path="/live/:streamId" element={<LiveStreamPage />} />
                <Route path="/event/:eventId" element={<EventDetailsPage />} />
                <Route path="/ticket/:ticketId" element={<TicketPage />} />
                <Route path="/job/:jobId" element={<JobDetailsPage />} />

                {/* Me/Profile Routes */}
                <Route path="/profile" element={<ProfilePage />} />
                <Route path="/edit-profile" element={<EditProfilePage />} />
                <Route path="/propay" element={<ProPayPage />} />
                <Route path="/prozone" element={<ProZonePage />} />
                <Route path="/wallet" element={<WalletPage />} />
                <Route path="/transaction-history" element={<TransactionHistoryPage />} />
                <Route path="/settings" element={<SettingsPage />} />
                <Route path="/notifications" element={<NotificationsPage />} />
                <Route path="/invite-friends" element={<InviteFriendsPage />} />
                <Route path="/help-support" element={<HelpSupportPage />} />

                {/* Wallet Routes */}
                <Route path="/send-money" element={<SendMoneyPage />} />

                {/* Social Routes */}
                <Route path="/gift" element={<GiftPage />} />
                <Route path="/donation" element={<DonationPage />} />

                {/* Task Routes */}
                <Route path="/tasks" element={<TaskPage />} />

                {/* Enhanced Desktop Dashboard */}
                <Route path="/dashboard" element={<DashboardPage />} />

                {/* Fallback */}
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </Layout>
          </ProtectedRoute>
        } />
      </Routes>
    </Box>
  );
}

// Main App Component with Cross-Platform Support
function App() {
  const [deviceType, setDeviceType] = useState(detectDeviceType());
  const [darkMode, setDarkMode] = useState(() => {
    const saved = localStorage.getItem('prochat_dark_mode');
    return saved ? JSON.parse(saved) : false;
  });

  useEffect(() => {
    // Apply security headers (for development)
    if (process.env.NODE_ENV === 'development') {
      Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
        document.querySelector('meta[http-equiv="' + key + '"]')?.setAttribute('content', value);
      });
    }

    // Device type detection
    const handleResize = () => {
      const newDeviceType = detectDeviceType();
      if (newDeviceType !== deviceType) {
        setDeviceType(newDeviceType);
        SecurityLogger.logSecurityEvent('DEVICE_TYPE_CHANGED', {
          from: deviceType,
          to: newDeviceType,
        });
      }
    };

    window.addEventListener('resize', handleResize);

    // Initialize app
    SecurityLogger.logSecurityEvent('APP_STARTED', {
      deviceType,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
    });

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [deviceType]);

  // Toggle dark mode
  const toggleDarkMode = () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);
    localStorage.setItem('prochat_dark_mode', JSON.stringify(newDarkMode));

    SecurityLogger.logSecurityEvent('THEME_CHANGED', {
      darkMode: newDarkMode,
      deviceType,
    });
  };

  // Get responsive theme
  const theme = getTheme(deviceType, darkMode);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <AuthProvider>
          <SocketProvider>
            <Router>
              <AppContent />
            </Router>
          </SocketProvider>
        </AuthProvider>

        {/* Enhanced Toast Notifications */}
        <Toaster
          position={deviceType === 'mobile' ? 'top-center' : 'top-right'}
          toastOptions={{
            duration: 4000,
            style: {
              background: darkMode ? '#2D2D2D' : '#363636',
              color: '#fff',
              borderRadius: deviceType === 'mobile' ? '12px' : '8px',
              fontSize: deviceType === 'mobile' ? '14px' : '16px',
            },
            success: {
              iconTheme: {
                primary: theme.palette.success.main,
                secondary: '#fff',
              },
            },
            error: {
              iconTheme: {
                primary: theme.palette.error.main,
                secondary: '#fff',
              },
            },
          }}
        />

        {/* PWA Install Button (hidden by default, shown by setupPWAInstallPrompt) */}
        <button
          id="pwa-install-button"
          style={{
            display: 'none',
            position: 'fixed',
            bottom: '20px',
            left: '20px',
            zIndex: 9999,
            padding: '12px 24px',
            backgroundColor: theme.palette.primary.main,
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: 'bold',
          }}
        >
          Install ProChat
        </button>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
