import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  Container,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemText,
  useTheme,
  useMediaQuery,
  Avatar,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Close as CloseIcon,
  Home,
  Explore,
  Event,
  Work,
  Mail,
  GetApp,
} from '@mui/icons-material';

const navigationItems = [
  { label: 'Nyumbani', path: '/', icon: <Home /> },
  { label: 'Gundua', path: '/discover', icon: <Explore /> },
  { label: 'Matukio', path: '/events', icon: <Event /> },
  { label: 'Kazi 🤖', path: '/jobs', icon: <Work />, isNew: true },
  { label: 'Mialiko', path: '/invitations', icon: <Mail /> },
];

export default function Layout() {
  const [mobileOpen, setMobileOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleNavigation = (path) => {
    navigate(path);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const isActivePath = (path) => {
    return location.pathname === path;
  };

  const drawer = (
    <Box sx={{ width: 250 }}>
      <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6" color="primary" fontWeight="bold">
          ProChat
        </Typography>
        <IconButton onClick={handleDrawerToggle}>
          <CloseIcon />
        </IconButton>
      </Box>
      <List>
        {navigationItems.map((item) => (
          <ListItem
            button
            key={item.label}
            onClick={() => handleNavigation(item.path)}
            sx={{
              backgroundColor: isActivePath(item.path) ? 'primary.light' : 'transparent',
              color: isActivePath(item.path) ? 'white' : 'text.primary',
              position: 'relative',
              '&:hover': {
                backgroundColor: isActivePath(item.path) ? 'primary.main' : 'grey.100',
              },
              ...(item.isNew && {
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                '&:hover': {
                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                },
              }),
            }}
          >
            <Box sx={{ mr: 2, color: 'inherit' }}>
              {item.icon}
            </Box>
            <ListItemText primary={item.label} />
            {item.isNew && (
              <Box
                sx={{
                  bgcolor: 'error.main',
                  color: 'white',
                  borderRadius: '50%',
                  width: 20,
                  height: 20,
                  fontSize: 12,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontWeight: 'bold',
                  ml: 1,
                }}
              >
                NEW
              </Box>
            )}
          </ListItem>
        ))}
        <ListItem button onClick={() => handleNavigation('/download')}>
          <Box sx={{ mr: 2 }}>
            <GetApp />
          </Box>
          <ListItemText primary="Pakua App" />
        </ListItem>
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* Header */}
      <AppBar position="sticky" elevation={0} sx={{ backgroundColor: 'white', borderBottom: '1px solid #E0E0E0' }}>
        <Container maxWidth="lg">
          <Toolbar sx={{ justifyContent: 'space-between', py: 1 }}>
            {/* Logo */}
            <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }} onClick={() => navigate('/')}>
              <Avatar
                sx={{
                  width: 40,
                  height: 40,
                  mr: 2,
                  background: 'linear-gradient(135deg, #007AFF 0%, #4DA3FF 100%)',
                }}
              >
                P
              </Avatar>
              <Typography
                variant="h5"
                component="div"
                sx={{
                  fontWeight: 'bold',
                  background: 'linear-gradient(135deg, #007AFF 0%, #4DA3FF 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                ProChat
              </Typography>
            </Box>

            {/* Desktop Navigation */}
            {!isMobile && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {navigationItems.map((item) => (
                  <Button
                    key={item.label}
                    onClick={() => handleNavigation(item.path)}
                    sx={{
                      color: isActivePath(item.path) ? 'primary.main' : 'text.primary',
                      fontWeight: isActivePath(item.path) ? 600 : 400,
                      position: 'relative',
                      '&:hover': {
                        backgroundColor: 'primary.light',
                        color: 'white',
                      },
                      ...(item.isNew && {
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        color: 'white',
                        '&:hover': {
                          background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                        },
                      }),
                    }}
                  >
                    {item.label}
                    {item.isNew && (
                      <Box
                        sx={{
                          position: 'absolute',
                          top: -8,
                          right: -8,
                          bgcolor: 'error.main',
                          color: 'white',
                          borderRadius: '50%',
                          width: 16,
                          height: 16,
                          fontSize: 10,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontWeight: 'bold',
                        }}
                      >
                        !
                      </Box>
                    )}
                  </Button>
                ))}
              </Box>
            )}

            {/* Right Side Actions */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              {!isMobile && (
                <>
                  <Button
                    variant="outlined"
                    onClick={() => navigate('/download')}
                    startIcon={<GetApp />}
                  >
                    Pakua App
                  </Button>
                  <Button
                    variant="contained"
                    onClick={() => navigate('/login')}
                  >
                    Ingia
                  </Button>
                </>
              )}

              {/* Mobile Menu Button */}
              {isMobile && (
                <IconButton
                  color="primary"
                  aria-label="open drawer"
                  edge="start"
                  onClick={handleDrawerToggle}
                >
                  <MenuIcon />
                </IconButton>
              )}
            </Box>
          </Toolbar>
        </Container>
      </AppBar>

      {/* Mobile Drawer */}
      <Drawer
        variant="temporary"
        anchor="right"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
      >
        {drawer}
      </Drawer>

      {/* Main Content */}
      <Box component="main" sx={{ flexGrow: 1 }}>
        <Outlet />
      </Box>

      {/* Footer */}
      <Box
        component="footer"
        sx={{
          backgroundColor: 'grey.900',
          color: 'white',
          py: 6,
          mt: 'auto',
        }}
      >
        <Container maxWidth="lg">
          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 4 }}>
            {/* Company Info */}
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" gutterBottom fontWeight="bold">
                ProChat
              </Typography>
              <Typography variant="body2" sx={{ mb: 2, opacity: 0.8 }}>
                Jukwaa la kisasa la mitandao ya kijamii na miamala ya kifedha. 
                Tunachanganya mazungumzo, biashara, na burudani katika programu moja.
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.6 }}>
                © 2024 ProChat. Haki zote zimehifadhiwa.
              </Typography>
            </Box>

            {/* Quick Links */}
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" gutterBottom fontWeight="bold">
                Viungo vya Haraka
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button color="inherit" sx={{ justifyContent: 'flex-start', p: 0 }} onClick={() => navigate('/about')}>
                  Kuhusu Sisi
                </Button>
                <Button color="inherit" sx={{ justifyContent: 'flex-start', p: 0 }} onClick={() => navigate('/contact')}>
                  Wasiliana Nasi
                </Button>
                <Button color="inherit" sx={{ justifyContent: 'flex-start', p: 0 }} onClick={() => navigate('/download')}>
                  Pakua App
                </Button>
              </Box>
            </Box>

            {/* Contact Info */}
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" gutterBottom fontWeight="bold">
                Wasiliana Nasi
              </Typography>
              <Typography variant="body2" sx={{ mb: 1, opacity: 0.8 }}>
                Email: <EMAIL>
              </Typography>
              <Typography variant="body2" sx={{ mb: 1, opacity: 0.8 }}>
                Simu: +255 123 456 789
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.8 }}>
                Dar es Salaam, Tanzania
              </Typography>
            </Box>
          </Box>
        </Container>
      </Box>
    </Box>
  );
}
