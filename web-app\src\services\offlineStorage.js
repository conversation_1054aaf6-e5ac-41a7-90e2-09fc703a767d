// ProChat Advanced Offline Storage System
// IndexedDB-based storage for cross-platform data persistence

class OfflineStorageService {
  constructor() {
    this.dbName = 'ProChatDB';
    this.dbVersion = 1;
    this.db = null;
    this.isInitialized = false;
    
    this.stores = {
      messages: 'messages',
      posts: 'posts',
      users: 'users',
      chats: 'chats',
      media: 'media',
      walletTransactions: 'wallet_transactions',
      notifications: 'notifications',
      settings: 'settings',
      syncQueue: 'sync_queue',
    };
    
    this.init();
  }

  async init() {
    try {
      this.db = await this.openDatabase();
      this.isInitialized = true;
      console.log('ProChat Offline Storage: Initialized successfully');
    } catch (error) {
      console.error('ProChat Offline Storage: Initialization failed', error);
    }
  }

  openDatabase() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        reject(new Error('Failed to open database'));
      };

      request.onsuccess = (event) => {
        resolve(event.target.result);
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Create object stores
        this.createObjectStores(db);
      };
    });
  }

  createObjectStores(db) {
    // Messages store
    if (!db.objectStoreNames.contains(this.stores.messages)) {
      const messagesStore = db.createObjectStore(this.stores.messages, { 
        keyPath: 'id', 
        autoIncrement: true 
      });
      messagesStore.createIndex('conversationId', 'conversationId', { unique: false });
      messagesStore.createIndex('timestamp', 'timestamp', { unique: false });
      messagesStore.createIndex('synced', 'synced', { unique: false });
    }

    // Posts store
    if (!db.objectStoreNames.contains(this.stores.posts)) {
      const postsStore = db.createObjectStore(this.stores.posts, { 
        keyPath: 'id', 
        autoIncrement: true 
      });
      postsStore.createIndex('userId', 'userId', { unique: false });
      postsStore.createIndex('timestamp', 'timestamp', { unique: false });
      postsStore.createIndex('synced', 'synced', { unique: false });
    }

    // Users store
    if (!db.objectStoreNames.contains(this.stores.users)) {
      const usersStore = db.createObjectStore(this.stores.users, { 
        keyPath: 'id' 
      });
      usersStore.createIndex('phoneNumber', 'phoneNumber', { unique: true });
      usersStore.createIndex('email', 'email', { unique: true });
    }

    // Chats store
    if (!db.objectStoreNames.contains(this.stores.chats)) {
      const chatsStore = db.createObjectStore(this.stores.chats, { 
        keyPath: 'id' 
      });
      chatsStore.createIndex('lastMessageTime', 'lastMessageTime', { unique: false });
      chatsStore.createIndex('isGroup', 'isGroup', { unique: false });
    }

    // Media store
    if (!db.objectStoreNames.contains(this.stores.media)) {
      const mediaStore = db.createObjectStore(this.stores.media, { 
        keyPath: 'id', 
        autoIncrement: true 
      });
      mediaStore.createIndex('type', 'type', { unique: false });
      mediaStore.createIndex('uploaded', 'uploaded', { unique: false });
    }

    // Wallet transactions store
    if (!db.objectStoreNames.contains(this.stores.walletTransactions)) {
      const walletStore = db.createObjectStore(this.stores.walletTransactions, { 
        keyPath: 'id', 
        autoIncrement: true 
      });
      walletStore.createIndex('type', 'type', { unique: false });
      walletStore.createIndex('timestamp', 'timestamp', { unique: false });
      walletStore.createIndex('synced', 'synced', { unique: false });
    }

    // Notifications store
    if (!db.objectStoreNames.contains(this.stores.notifications)) {
      const notificationsStore = db.createObjectStore(this.stores.notifications, { 
        keyPath: 'id', 
        autoIncrement: true 
      });
      notificationsStore.createIndex('read', 'read', { unique: false });
      notificationsStore.createIndex('timestamp', 'timestamp', { unique: false });
    }

    // Settings store
    if (!db.objectStoreNames.contains(this.stores.settings)) {
      db.createObjectStore(this.stores.settings, { 
        keyPath: 'key' 
      });
    }

    // Sync queue store
    if (!db.objectStoreNames.contains(this.stores.syncQueue)) {
      const syncStore = db.createObjectStore(this.stores.syncQueue, { 
        keyPath: 'id', 
        autoIncrement: true 
      });
      syncStore.createIndex('type', 'type', { unique: false });
      syncStore.createIndex('priority', 'priority', { unique: false });
      syncStore.createIndex('timestamp', 'timestamp', { unique: false });
    }
  }

  // Generic CRUD operations
  async add(storeName, data) {
    if (!this.isInitialized) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      
      const request = store.add({
        ...data,
        createdAt: new Date().toISOString(),
        synced: false,
      });

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async get(storeName, id) {
    if (!this.isInitialized) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(id);

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async getAll(storeName, limit = null) {
    if (!this.isInitialized) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = limit ? store.getAll(null, limit) : store.getAll();

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async update(storeName, data) {
    if (!this.isInitialized) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      
      const request = store.put({
        ...data,
        updatedAt: new Date().toISOString(),
      });

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async delete(storeName, id) {
    if (!this.isInitialized) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(id);

      request.onsuccess = () => resolve(true);
      request.onerror = () => reject(request.error);
    });
  }

  async clear(storeName) {
    if (!this.isInitialized) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onsuccess = () => resolve(true);
      request.onerror = () => reject(request.error);
    });
  }

  // Specialized methods for different data types

  // Messages
  async saveMessage(message) {
    return this.add(this.stores.messages, message);
  }

  async getMessagesByConversation(conversationId, limit = 50) {
    if (!this.isInitialized) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.stores.messages], 'readonly');
      const store = transaction.objectStore(storeName);
      const index = store.index('conversationId');
      const request = index.getAll(conversationId, limit);

      request.onsuccess = () => {
        const messages = request.result.sort((a, b) => 
          new Date(b.timestamp) - new Date(a.timestamp)
        );
        resolve(messages);
      };
      request.onerror = () => reject(request.error);
    });
  }

  async getUnsyncedMessages() {
    return this.getByIndex(this.stores.messages, 'synced', false);
  }

  // Posts
  async savePost(post) {
    return this.add(this.stores.posts, post);
  }

  async getUnsyncedPosts() {
    return this.getByIndex(this.stores.posts, 'synced', false);
  }

  // Wallet transactions
  async saveWalletTransaction(transaction) {
    return this.add(this.stores.walletTransactions, transaction);
  }

  async getUnsyncedTransactions() {
    return this.getByIndex(this.stores.walletTransactions, 'synced', false);
  }

  // Sync queue management
  async addToSyncQueue(type, data, priority = 1) {
    return this.add(this.stores.syncQueue, {
      type,
      data,
      priority,
      attempts: 0,
      maxAttempts: 3,
      timestamp: new Date().toISOString(),
    });
  }

  async getSyncQueue() {
    const items = await this.getAll(this.stores.syncQueue);
    return items.sort((a, b) => b.priority - a.priority);
  }

  async removeSyncQueueItem(id) {
    return this.delete(this.stores.syncQueue, id);
  }

  async incrementSyncAttempts(id) {
    const item = await this.get(this.stores.syncQueue, id);
    if (item) {
      item.attempts += 1;
      item.lastAttempt = new Date().toISOString();
      return this.update(this.stores.syncQueue, item);
    }
  }

  // Settings management
  async saveSetting(key, value) {
    return this.update(this.stores.settings, { key, value });
  }

  async getSetting(key, defaultValue = null) {
    const setting = await this.get(this.stores.settings, key);
    return setting ? setting.value : defaultValue;
  }

  // Utility methods
  async getByIndex(storeName, indexName, value) {
    if (!this.isInitialized) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const index = store.index(indexName);
      const request = index.getAll(value);

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async getStorageUsage() {
    if (!navigator.storage || !navigator.storage.estimate) {
      return { used: 0, quota: 0, percentage: 0 };
    }

    try {
      const estimate = await navigator.storage.estimate();
      return {
        used: estimate.usage || 0,
        quota: estimate.quota || 0,
        percentage: estimate.quota ? (estimate.usage / estimate.quota) * 100 : 0,
      };
    } catch (error) {
      console.error('Failed to get storage usage:', error);
      return { used: 0, quota: 0, percentage: 0 };
    }
  }

  async clearAllData() {
    if (!this.isInitialized) await this.init();
    
    const storeNames = Object.values(this.stores);
    const promises = storeNames.map(storeName => this.clear(storeName));
    
    try {
      await Promise.all(promises);
      console.log('ProChat Offline Storage: All data cleared');
      return true;
    } catch (error) {
      console.error('ProChat Offline Storage: Failed to clear data', error);
      return false;
    }
  }

  // Export/Import for backup
  async exportData() {
    const data = {};
    
    for (const [key, storeName] of Object.entries(this.stores)) {
      data[key] = await this.getAll(storeName);
    }
    
    return {
      version: this.dbVersion,
      timestamp: new Date().toISOString(),
      data,
    };
  }

  async importData(exportedData) {
    if (!exportedData.data) {
      throw new Error('Invalid export data format');
    }
    
    // Clear existing data
    await this.clearAllData();
    
    // Import new data
    for (const [key, items] of Object.entries(exportedData.data)) {
      const storeName = this.stores[key];
      if (storeName && Array.isArray(items)) {
        for (const item of items) {
          await this.add(storeName, item);
        }
      }
    }
    
    console.log('ProChat Offline Storage: Data imported successfully');
  }
}

// Create singleton instance
const offlineStorage = new OfflineStorageService();

export default offlineStorage;
