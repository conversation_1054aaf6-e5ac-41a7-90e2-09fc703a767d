# 🚀 ProChat Payment Integration Guide

Mwongozo kamili wa kuunganisha mifumo yote ya malipo kwenye ProChat super app.

## 📋 Orodha ya Mifumo ya Malipo

### 📱 MOBILE MONEY PROVIDERS
- ✅ **M-Pesa** - Vodacom Tanzania
- ✅ **Airtel Money** - Airtel Tanzania  
- ✅ **Tigo Pesa (MIXX BY YAS)** - Tigo Tanzania
- ✅ **HaloPesa** - Halotel Tanzania
- ✅ **AzamPesa** - Azam Telecom
- ✅ **NARA** - NARA Financial Services

### 🏦 BANKING PROVIDERS
- ✅ **CRDB Bank** - CRDB Bank PLC
- ✅ **NMB Bank** - National Microfinance Bank
- ✅ **NBC Bank** - National Bank of Commerce

### 💳 INTERNATIONAL CARDS
- ✅ **Visa** - Visa Inc.
- ✅ **Mastercard** - Mastercard Inc.
- ✅ **Stripe** - International card processing

### ₿ CRYPTOCURRENCY
- ✅ **Bitcoin** - BTC payments
- ✅ **USDT** - Tether USD payments

## 🔧 Hatua za Kuanzisha

### 1. 📋 Mahitaji ya Awali

```bash
# Clone repository
git clone https://github.com/your-repo/prochat.git
cd prochat/web-app

# Install dependencies
npm install

# Copy environment variables
cp .env.example .env
```

### 2. 🔑 Kuongeza API Keys

Fungua faili `.env` na jaza taarifa zako za API:

```env
# M-Pesa Configuration
MPESA_API_KEY=your_actual_mpesa_api_key
MPESA_PUBLIC_KEY=your_actual_mpesa_public_key
MPESA_SERVICE_PROVIDER_CODE=your_service_provider_code

# Airtel Money Configuration
AIRTEL_CLIENT_ID=your_actual_airtel_client_id
AIRTEL_CLIENT_SECRET=your_actual_airtel_client_secret
AIRTEL_API_KEY=your_actual_airtel_api_key

# Continue for all other providers...
```

### 3. 🏗️ Kuanzisha Database

```bash
# Start MongoDB (for transaction storage)
mongod --dbpath ./data/db

# Start Redis (for caching and sessions)
redis-server

# Run database migrations
npm run migrate
```

### 4. 🚀 Kuanzisha Application

```bash
# Start development server
npm start

# Application will be available at:
# http://localhost:3000
```

## 📱 Jinsi ya Kutumia Payment Components

### 1. 💰 Basic Payment Form

```jsx
import React from 'react';
import PaymentForm from './components/Payment/PaymentForm';

function MyPaymentPage() {
  const handlePaymentSuccess = (result) => {
    console.log('Payment successful:', result);
    // Handle successful payment
  };

  const handlePaymentError = (error) => {
    console.error('Payment failed:', error);
    // Handle payment error
  };

  return (
    <PaymentForm
      amount={50000} // TZS 50,000
      currency="TZS"
      description="Malipo ya huduma za ProChat"
      onSuccess={handlePaymentSuccess}
      onError={handlePaymentError}
      metadata={{
        userId: 'user123',
        orderId: 'order456',
      }}
    />
  );
}
```

### 2. 🎯 Payment Method Selector Only

```jsx
import React, { useState } from 'react';
import PaymentMethodSelector from './components/Payment/PaymentMethodSelector';

function MyMethodSelector() {
  const [selectedMethod, setSelectedMethod] = useState('');

  return (
    <PaymentMethodSelector
      amount={25000}
      currency="TZS"
      onMethodSelect={setSelectedMethod}
      selectedMethod={selectedMethod}
      showFees={true}
      excludeProviders={['bitcoin', 'usdt']} // Hide crypto options
    />
  );
}
```

### 3. 🔄 Direct API Usage

```jsx
import paymentAPI from './api/paymentAPI';

// Process M-Pesa payment
const processMpesaPayment = async () => {
  try {
    const result = await paymentAPI.processMpesaPayment({
      amount: 10000,
      currency: 'TZS',
      customerPhone: '+255712345678',
      description: 'Malipo ya bidhaa',
    });
    
    console.log('Payment result:', result);
  } catch (error) {
    console.error('Payment error:', error);
  }
};

// Process Visa payment
const processVisaPayment = async () => {
  try {
    const result = await paymentAPI.processVisaPayment({
      amount: 50000,
      currency: 'TZS',
      cardData: {
        number: '****************',
        expiryDate: '12/25',
        cvv: '123',
        holderName: 'John Doe',
      },
      description: 'International payment',
    });
    
    console.log('Payment result:', result);
  } catch (error) {
    console.error('Payment error:', error);
  }
};
```

## 🔐 Usalama na Security

### 1. 🛡️ Environment Variables Security

```bash
# NEVER commit .env files to version control
echo ".env" >> .gitignore

# Use different .env files for different environments
cp .env.example .env.development
cp .env.example .env.production
cp .env.example .env.staging
```

### 2. 🔒 API Key Management

```javascript
// Use environment-specific configurations
const getApiConfig = () => {
  if (process.env.NODE_ENV === 'production') {
    return {
      mpesaApiKey: process.env.MPESA_API_KEY_PROD,
      baseUrl: 'https://openapi.m-pesa.com/ipg/v2',
    };
  } else {
    return {
      mpesaApiKey: process.env.MPESA_API_KEY_SANDBOX,
      baseUrl: 'https://openapi.m-pesa.com/sandbox/ipg/v2',
    };
  }
};
```

### 3. 🔐 Webhook Security

```javascript
// Verify webhook signatures
const verifyWebhookSignature = (payload, signature, secret) => {
  const crypto = require('crypto');
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
};
```

## 📊 Monitoring na Analytics

### 1. 📈 Payment Statistics

```javascript
import paymentAPI from './api/paymentAPI';

// Get payment statistics
const getPaymentStats = async () => {
  try {
    const stats = await paymentAPI.getPaymentStats({
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      provider: 'mpesa', // Optional: filter by provider
    });
    
    console.log('Payment stats:', stats);
    /*
    {
      totalTransactions: 1500,
      successfulTransactions: 1425,
      failedTransactions: 75,
      totalVolume: 45000000, // TZS
      averageAmount: 30000,
      successRate: 0.95
    }
    */
  } catch (error) {
    console.error('Failed to get stats:', error);
  }
};
```

### 2. 📊 Provider Performance

```javascript
// Get provider performance metrics
const getProviderPerformance = async () => {
  try {
    const performance = await paymentAPI.getProviderPerformance('mpesa', '30d');
    
    console.log('M-Pesa performance:', performance);
    /*
    {
      provider: 'mpesa',
      successRate: 0.98,
      averageResponseTime: 2500, // milliseconds
      totalTransactions: 500,
      totalVolume: 15000000,
      uptime: 0.999
    }
    */
  } catch (error) {
    console.error('Failed to get performance:', error);
  }
};
```

## 🔄 Webhook Handling

### 1. 📡 Setting Up Webhooks

```javascript
// Register webhook endpoints
const registerWebhooks = async () => {
  const webhooks = [
    {
      provider: 'mpesa',
      url: 'https://api.prochat.co.tz/webhooks/mpesa',
      events: ['payment.completed', 'payment.failed'],
    },
    {
      provider: 'airtel_money',
      url: 'https://api.prochat.co.tz/webhooks/airtel',
      events: ['payment.completed', 'payment.failed'],
    },
    // Add other providers...
  ];

  for (const webhook of webhooks) {
    try {
      await paymentAPI.registerWebhook(webhook);
      console.log(`Webhook registered for ${webhook.provider}`);
    } catch (error) {
      console.error(`Failed to register webhook for ${webhook.provider}:`, error);
    }
  }
};
```

### 2. 🎯 Handling Webhook Events

```javascript
// Express.js webhook handler example
app.post('/webhooks/:provider', (req, res) => {
  const { provider } = req.params;
  const payload = req.body;
  const signature = req.headers['x-signature'];

  // Verify signature
  if (!verifyWebhookSignature(JSON.stringify(payload), signature, process.env.WEBHOOK_SECRET)) {
    return res.status(401).json({ error: 'Invalid signature' });
  }

  // Process webhook based on provider
  switch (provider) {
    case 'mpesa':
      handleMpesaWebhook(payload);
      break;
    case 'airtel':
      handleAirtelWebhook(payload);
      break;
    // Add other providers...
    default:
      console.log(`Unknown provider: ${provider}`);
  }

  res.status(200).json({ status: 'success' });
});
```

## 🚨 Error Handling

### 1. ⚠️ Common Error Scenarios

```javascript
// Handle different types of payment errors
const handlePaymentError = (error) => {
  switch (error.code) {
    case 'INSUFFICIENT_FUNDS':
      showUserMessage('Huna pesa za kutosha kwenye akaunti yako');
      break;
    case 'INVALID_PHONE_NUMBER':
      showUserMessage('Nambari ya simu si sahihi');
      break;
    case 'TRANSACTION_TIMEOUT':
      showUserMessage('Muda wa malipo umekwisha. Jaribu tena');
      break;
    case 'PROVIDER_UNAVAILABLE':
      showUserMessage('Huduma ya malipo haipatikani kwa sasa');
      break;
    default:
      showUserMessage('Kuna tatizo la kiufundi. Jaribu tena');
  }
};
```

### 2. 🔄 Retry Logic

```javascript
// Implement retry logic for failed payments
const retryPayment = async (paymentData, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await paymentAPI.processPayment(paymentData);
      return result; // Success, return result
    } catch (error) {
      console.log(`Payment attempt ${attempt} failed:`, error.message);
      
      if (attempt === maxRetries) {
        throw error; // Final attempt failed
      }
      
      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
};
```

## 📱 Testing

### 1. 🧪 Test Credentials

```javascript
// Use test credentials for development
const TEST_CREDENTIALS = {
  mpesa: {
    testPhone: '+255000000001',
    testAmount: 1000,
  },
  airtel: {
    testPhone: '+255000000002',
    testAmount: 1000,
  },
  visa: {
    testCard: '****************',
    testCvv: '123',
    testExpiry: '12/25',
  },
};
```

### 2. 🔍 Testing Payments

```javascript
// Test payment flow
const testPaymentFlow = async () => {
  try {
    // Test M-Pesa payment
    const mpesaResult = await paymentAPI.processMpesaPayment({
      amount: TEST_CREDENTIALS.mpesa.testAmount,
      customerPhone: TEST_CREDENTIALS.mpesa.testPhone,
      description: 'Test payment',
    });
    console.log('M-Pesa test result:', mpesaResult);

    // Test Visa payment
    const visaResult = await paymentAPI.processVisaPayment({
      amount: 5000,
      cardData: {
        number: TEST_CREDENTIALS.visa.testCard,
        cvv: TEST_CREDENTIALS.visa.testCvv,
        expiryDate: TEST_CREDENTIALS.visa.testExpiry,
        holderName: 'Test User',
      },
      description: 'Test card payment',
    });
    console.log('Visa test result:', visaResult);

  } catch (error) {
    console.error('Test failed:', error);
  }
};
```

## 🎯 Best Practices

### 1. ✅ Do's

- ✅ Always validate payment data before processing
- ✅ Use HTTPS for all payment communications
- ✅ Implement proper error handling and user feedback
- ✅ Log all payment transactions for audit purposes
- ✅ Use environment variables for sensitive configuration
- ✅ Implement rate limiting to prevent abuse
- ✅ Verify webhook signatures for security
- ✅ Use proper encryption for sensitive data storage

### 2. ❌ Don'ts

- ❌ Never store credit card details in your database
- ❌ Don't commit API keys or secrets to version control
- ❌ Don't process payments without proper user authentication
- ❌ Don't ignore webhook signature verification
- ❌ Don't use production credentials in development
- ❌ Don't skip transaction logging and monitoring
- ❌ Don't process payments without proper validation

## 🆘 Support na Msaada

### 📞 Mawasiliano ya Msaada

- **Email:** <EMAIL>
- **Simu:** +255 123 456 789
- **Muda:** 24/7

### 📚 Rasilimali za Ziada

- [M-Pesa API Documentation](https://developer.vodacom.co.tz)
- [Airtel Money API Documentation](https://developers.airtel.africa)
- [Stripe API Documentation](https://stripe.com/docs/api)
- [Visa Developer Documentation](https://developer.visa.com)
- [Mastercard Developer Documentation](https://developer.mastercard.com)

### 🐛 Kuripoti Matatizo

Ikiwa unapata matatizo, tafadhali:

1. Angalia logs za application
2. Thibitisha API credentials zako
3. Jaribu test environment kwanza
4. Wasiliana na timu ya msaada

---

**🎉 Hongera! Sasa una mfumo kamili wa malipo kwenye ProChat super app yako!**
