// ProChat Cross-Platform Sync Service
// Real-time synchronization between Web + Mobile App

import { io } from 'socket.io-client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SecurityLogger } from '../config/security';

class CrossPlatformSyncService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.syncQueue = [];
    this.lastSyncTime = null;
    this.platform = this.detectPlatform();
    this.deviceId = null;
    this.listeners = new Map();
    
    this.init();
  }

  detectPlatform() {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (/android/.test(userAgent)) return 'android';
    if (/iphone|ipad|ipod/.test(userAgent)) return 'ios';
    if (/mobile/.test(userAgent)) return 'mobile-web';
    return 'desktop-web';
  }

  async init() {
    try {
      // Generate device ID
      this.deviceId = await this.generateDeviceId();
      
      // Initialize socket connection
      this.initializeSocket();
      
      // Setup sync listeners
      this.setupSyncListeners();
      
      // Setup offline detection
      this.setupOfflineDetection();
      
      // Start periodic sync
      this.startPeriodicSync();
      
      SecurityLogger.logSecurityEvent('SYNC_SERVICE_INITIALIZED', {
        platform: this.platform,
        deviceId: this.deviceId
      });
      
    } catch (error) {
      console.error('Sync Service initialization failed:', error);
    }
  }

  async generateDeviceId() {
    // Create unique device identifier
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('ProChat Sync ID', 2, 2);
    
    const fingerprint = {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      screen: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      canvas: canvas.toDataURL(),
    };

    const deviceString = JSON.stringify(fingerprint);
    const encoder = new TextEncoder();
    const data = encoder.encode(deviceString);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('').substring(0, 32);
  }

  initializeSocket() {
    const token = TokenManager.getAccessToken();
    if (!token) return;

    this.socket = io(process.env.REACT_APP_SOCKET_URL, {
      auth: {
        token,
        platform: this.platform,
        deviceId: this.deviceId,
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
    });

    this.socket.on('connect', () => {
      this.isConnected = true;
      this.processSyncQueue();
      this.emit('connection-status', { connected: true });
      
      SecurityLogger.logSecurityEvent('SYNC_CONNECTED', {
        platform: this.platform,
        deviceId: this.deviceId
      });
    });

    this.socket.on('disconnect', () => {
      this.isConnected = false;
      this.emit('connection-status', { connected: false });
      
      SecurityLogger.logSecurityEvent('SYNC_DISCONNECTED', {
        platform: this.platform
      });
    });

    this.socket.on('sync-data', (data) => {
      this.handleIncomingSync(data);
    });

    this.socket.on('force-logout', (reason) => {
      this.handleForceLogout(reason);
    });
  }

  setupSyncListeners() {
    // Listen for data changes that need syncing
    window.addEventListener('storage', (e) => {
      if (e.key && e.key.startsWith('prochat_')) {
        this.queueSync({
          type: 'storage-change',
          key: e.key,
          oldValue: e.oldValue,
          newValue: e.newValue,
          timestamp: Date.now(),
        });
      }
    });

    // Listen for user actions
    this.on('user-action', (action) => {
      this.queueSync({
        type: 'user-action',
        action,
        platform: this.platform,
        deviceId: this.deviceId,
        timestamp: Date.now(),
      });
    });
  }

  setupOfflineDetection() {
    window.addEventListener('online', () => {
      this.emit('network-status', { online: true });
      if (this.socket && !this.isConnected) {
        this.socket.connect();
      }
      this.processSyncQueue();
    });

    window.addEventListener('offline', () => {
      this.emit('network-status', { online: false });
    });
  }

  startPeriodicSync() {
    // Sync every 30 seconds when connected
    setInterval(() => {
      if (this.isConnected && navigator.onLine) {
        this.performPeriodicSync();
      }
    }, 30000);
  }

  queueSync(data) {
    this.syncQueue.push({
      ...data,
      id: this.generateSyncId(),
      queued: Date.now(),
    });

    // Process immediately if connected
    if (this.isConnected) {
      this.processSyncQueue();
    }
  }

  generateSyncId() {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  processSyncQueue() {
    if (!this.isConnected || this.syncQueue.length === 0) return;

    const batch = this.syncQueue.splice(0, 10); // Process in batches of 10
    
    this.socket.emit('sync-batch', {
      platform: this.platform,
      deviceId: this.deviceId,
      batch,
      timestamp: Date.now(),
    });
  }

  handleIncomingSync(data) {
    const { type, payload, sourceDevice, sourcePlatform } = data;

    // Don't sync data from same device
    if (sourceDevice === this.deviceId) return;

    switch (type) {
      case 'wallet-update':
        this.syncWalletData(payload);
        break;
      case 'message-received':
        this.syncMessageData(payload);
        break;
      case 'notification':
        this.syncNotification(payload);
        break;
      case 'profile-update':
        this.syncProfileData(payload);
        break;
      case 'settings-change':
        this.syncSettingsData(payload);
        break;
      default:
        console.log('Unknown sync type:', type);
    }

    this.emit('data-synced', { type, payload, sourcePlatform });
  }

  syncWalletData(payload) {
    // Update wallet balance and transactions
    const currentWallet = JSON.parse(localStorage.getItem('prochat_wallet') || '{}');
    const updatedWallet = { ...currentWallet, ...payload };
    
    localStorage.setItem('prochat_wallet', JSON.stringify(updatedWallet));
    this.emit('wallet-synced', updatedWallet);
  }

  syncMessageData(payload) {
    // Update chat messages
    const { conversationId, message } = payload;
    
    // Update conversation list
    const conversations = JSON.parse(localStorage.getItem('prochat_conversations') || '[]');
    const conversationIndex = conversations.findIndex(c => c.id === conversationId);
    
    if (conversationIndex >= 0) {
      conversations[conversationIndex].lastMessage = message.content;
      conversations[conversationIndex].timestamp = message.timestamp;
      conversations[conversationIndex].unreadCount = (conversations[conversationIndex].unreadCount || 0) + 1;
    }
    
    localStorage.setItem('prochat_conversations', JSON.stringify(conversations));
    this.emit('message-synced', payload);
  }

  syncNotification(payload) {
    // Show notification across platforms
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(payload.title, {
        body: payload.body,
        icon: '/logo192.png',
        badge: '/badge-72x72.png',
        tag: payload.tag || 'prochat-sync',
      });
    }
    
    this.emit('notification-synced', payload);
  }

  syncProfileData(payload) {
    // Update user profile
    const currentUser = JSON.parse(localStorage.getItem('prochat_user') || '{}');
    const updatedUser = { ...currentUser, ...payload };
    
    localStorage.setItem('prochat_user', JSON.stringify(updatedUser));
    this.emit('profile-synced', updatedUser);
  }

  syncSettingsData(payload) {
    // Update app settings
    const currentSettings = JSON.parse(localStorage.getItem('prochat_settings') || '{}');
    const updatedSettings = { ...currentSettings, ...payload };
    
    localStorage.setItem('prochat_settings', JSON.stringify(updatedSettings));
    this.emit('settings-synced', updatedSettings);
  }

  performPeriodicSync() {
    // Get data that needs periodic sync
    const syncData = {
      lastActive: Date.now(),
      platform: this.platform,
      deviceId: this.deviceId,
      userStatus: 'online',
    };

    this.queueSync({
      type: 'heartbeat',
      data: syncData,
    });
  }

  handleForceLogout(reason) {
    SecurityLogger.logSecurityEvent('FORCE_LOGOUT', {
      reason,
      platform: this.platform,
      deviceId: this.deviceId
    });

    // Clear all local data
    TokenManager.clearTokens();
    localStorage.clear();
    sessionStorage.clear();

    // Redirect to login
    window.location.href = '/login';
  }

  // Event system
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Sync event callback error:', error);
        }
      });
    }
  }

  // Public methods
  syncUserAction(action, data) {
    this.queueSync({
      type: 'user-action',
      action,
      data,
      platform: this.platform,
      deviceId: this.deviceId,
      timestamp: Date.now(),
    });
  }

  syncWalletTransaction(transaction) {
    this.queueSync({
      type: 'wallet-transaction',
      transaction,
      platform: this.platform,
      deviceId: this.deviceId,
      timestamp: Date.now(),
    });
  }

  syncMessageSent(conversationId, message) {
    this.queueSync({
      type: 'message-sent',
      conversationId,
      message,
      platform: this.platform,
      deviceId: this.deviceId,
      timestamp: Date.now(),
    });
  }

  getConnectionStatus() {
    return {
      connected: this.isConnected,
      platform: this.platform,
      deviceId: this.deviceId,
      queueLength: this.syncQueue.length,
      lastSync: this.lastSyncTime,
    };
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;
  }
}

// Create singleton instance
const syncService = new CrossPlatformSyncService();

export default syncService;
