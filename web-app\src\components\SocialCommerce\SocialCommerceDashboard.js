// ProChat Social Commerce Dashboard
// Manage products, orders, and social sales

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Avatar,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  LinearProgress,
  Alert,
  Fab,
} from '@mui/material';
import {
  Store,
  Add,
  TrendingUp,
  ShoppingCart,
  MonetizationOn,
  Inventory,
  Analytics,
  Edit,
  Delete,
  Visibility,
  Share,
  Star,
  LocalShipping,
  CheckCircle,
  Cancel,
  Pending,
} from '@mui/icons-material';

import { useAuth } from '../../contexts/AuthContext';
import { useDeviceDetection } from '../../hooks/useDeviceDetection';
import socialCommerceService from '../../services/socialCommerceService';

const SocialCommerceDashboard = () => {
  const { user } = useAuth();
  const { isMobile } = useDeviceDetection();
  const [activeTab, setActiveTab] = useState(0);
  const [commerceData, setCommerceData] = useState({});
  const [loading, setLoading] = useState(true);
  const [productDialog, setProductDialog] = useState(false);
  const [newProduct, setNewProduct] = useState({
    name: '',
    description: '',
    price: '',
    category: '',
    stock: '',
  });

  useEffect(() => {
    loadCommerceData();
  }, [user]);

  const loadCommerceData = async () => {
    try {
      setLoading(true);
      const [stats, analytics] = await Promise.all([
        socialCommerceService.getSocialCommerceStats(),
        socialCommerceService.getSocialCommerceAnalytics(user.id),
      ]);

      setCommerceData({ stats, analytics: analytics.analytics || {} });
    } catch (error) {
      console.error('Failed to load commerce data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProduct = async () => {
    try {
      const result = await socialCommerceService.createProductPost(user.id, {
        text: `🎉 New product available! ${newProduct.name}`,
        hashtags: ['#NewProduct', '#Shopping', `#${newProduct.category}`],
      }, {
        name: newProduct.name,
        description: newProduct.description,
        price: parseFloat(newProduct.price),
        currency: 'TZS',
        category: newProduct.category,
        stock: parseInt(newProduct.stock),
      });

      if (result.success) {
        alert('Product posted successfully!');
        setProductDialog(false);
        setNewProduct({ name: '', description: '', price: '', category: '', stock: '' });
        loadCommerceData();
      } else {
        alert('Failed to create product: ' + result.reason);
      }
    } catch (error) {
      alert('Error: ' + error.message);
    }
  };

  const StatCard = ({ title, value, subtitle, icon, color = 'primary', trend }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {icon}
          <Typography variant="h6" sx={{ ml: 1, flexGrow: 1 }}>
            {title}
          </Typography>
          {trend && (
            <Chip 
              label={`${trend > 0 ? '+' : ''}${trend}%`}
              color={trend > 0 ? 'success' : 'error'}
              size="small"
            />
          )}
        </Box>
        <Typography variant="h3" color={`${color}.main`} sx={{ fontWeight: 'bold', mb: 1 }}>
          {value}
        </Typography>
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );

  const ProductRow = ({ product }) => (
    <TableRow>
      <TableCell>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Avatar src={product.image} sx={{ mr: 2 }}>
            <Inventory />
          </Avatar>
          <Box>
            <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
              {product.name}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {product.category}
            </Typography>
          </Box>
        </Box>
      </TableCell>
      <TableCell>TZS {product.price?.toLocaleString()}</TableCell>
      <TableCell>
        <Chip 
          label={product.stock > 0 ? 'In Stock' : 'Out of Stock'}
          color={product.stock > 0 ? 'success' : 'error'}
          size="small"
        />
      </TableCell>
      <TableCell>{product.sold || 0}</TableCell>
      <TableCell>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Star sx={{ color: 'gold', fontSize: 16, mr: 0.5 }} />
          {product.rating || 0}
        </Box>
      </TableCell>
      <TableCell>
        <IconButton size="small" color="primary">
          <Visibility />
        </IconButton>
        <IconButton size="small" color="secondary">
          <Edit />
        </IconButton>
        <IconButton size="small" color="error">
          <Delete />
        </IconButton>
      </TableCell>
    </TableRow>
  );

  const OrderRow = ({ order }) => {
    const getStatusColor = (status) => {
      switch (status) {
        case 'confirmed': return 'success';
        case 'pending': return 'warning';
        case 'cancelled': return 'error';
        case 'delivered': return 'info';
        default: return 'default';
      }
    };

    const getStatusIcon = (status) => {
      switch (status) {
        case 'confirmed': return <CheckCircle />;
        case 'pending': return <Pending />;
        case 'cancelled': return <Cancel />;
        case 'delivered': return <LocalShipping />;
        default: return <Pending />;
      }
    };

    return (
      <TableRow>
        <TableCell>#{order.id?.slice(-8)}</TableCell>
        <TableCell>{order.productName}</TableCell>
        <TableCell>{order.quantity}</TableCell>
        <TableCell>TZS {order.total?.toLocaleString()}</TableCell>
        <TableCell>
          <Chip 
            icon={getStatusIcon(order.status)}
            label={order.status}
            color={getStatusColor(order.status)}
            size="small"
          />
        </TableCell>
        <TableCell>{new Date(order.createdAt).toLocaleDateString()}</TableCell>
        <TableCell>
          <IconButton size="small" color="primary">
            <Visibility />
          </IconButton>
        </TableCell>
      </TableRow>
    );
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" sx={{ mb: 2 }}>Social Commerce</Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: isMobile ? 2 : 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Store sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          🛒 Social Commerce
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setProductDialog(true)}
        >
          Add Product
        </Button>
      </Box>

      {/* Welcome Message */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="h6">
          🎉 Turn your social posts into sales!
        </Typography>
        <Typography variant="body2">
          Create product posts that your followers can buy directly from their feed. No need to leave the app! 💰
        </Typography>
      </Alert>

      {/* Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Products"
            value={commerceData.analytics?.overview?.totalProducts || 0}
            subtitle="Products in your catalog"
            icon={<Inventory color="primary" />}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Sales"
            value={commerceData.analytics?.overview?.totalSales || 0}
            subtitle="Orders completed"
            icon={<ShoppingCart color="success" />}
            color="success"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Revenue"
            value={`TZS ${(commerceData.analytics?.overview?.totalRevenue || 0).toLocaleString()}`}
            subtitle="Total earnings"
            icon={<MonetizationOn color="warning" />}
            color="warning"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Conversion Rate"
            value={`${(commerceData.analytics?.conversion?.conversionRate || 0).toFixed(1)}%`}
            subtitle="Views to purchases"
            icon={<TrendingUp color="info" />}
            color="info"
          />
        </Grid>
      </Grid>

      {/* Main Content Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={activeTab} 
            onChange={(e, newValue) => setActiveTab(newValue)}
            variant={isMobile ? 'scrollable' : 'fullWidth'}
            scrollButtons="auto"
          >
            <Tab label="📦 Products" />
            <Tab label="📋 Orders" />
            <Tab label="📊 Analytics" />
            <Tab label="⭐ Reviews" />
          </Tabs>
        </Box>

        <CardContent>
          {/* Products Tab */}
          {activeTab === 0 && (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">Your Products</Typography>
                <Button
                  variant="outlined"
                  startIcon={<Add />}
                  onClick={() => setProductDialog(true)}
                >
                  Add New Product
                </Button>
              </Box>

              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Product</TableCell>
                      <TableCell>Price</TableCell>
                      <TableCell>Stock</TableCell>
                      <TableCell>Sold</TableCell>
                      <TableCell>Rating</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {/* Mock products */}
                    <ProductRow product={{
                      name: 'Smartphone Case',
                      category: 'Electronics',
                      price: 15000,
                      stock: 25,
                      sold: 12,
                      rating: 4.5,
                    }} />
                    <ProductRow product={{
                      name: 'Leather Shoes',
                      category: 'Fashion',
                      price: 85000,
                      stock: 8,
                      sold: 5,
                      rating: 4.8,
                    }} />
                    <ProductRow product={{
                      name: 'Coffee Mug',
                      category: 'Home',
                      price: 8000,
                      stock: 0,
                      sold: 20,
                      rating: 4.2,
                    }} />
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* Orders Tab */}
          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>Recent Orders</Typography>

              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Order ID</TableCell>
                      <TableCell>Product</TableCell>
                      <TableCell>Quantity</TableCell>
                      <TableCell>Total</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Date</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {/* Mock orders */}
                    <OrderRow order={{
                      id: 'order_12345678',
                      productName: 'Smartphone Case',
                      quantity: 2,
                      total: 30000,
                      status: 'confirmed',
                      createdAt: Date.now() - 86400000,
                    }} />
                    <OrderRow order={{
                      id: 'order_87654321',
                      productName: 'Leather Shoes',
                      quantity: 1,
                      total: 85000,
                      status: 'delivered',
                      createdAt: Date.now() - 172800000,
                    }} />
                    <OrderRow order={{
                      id: 'order_11223344',
                      productName: 'Coffee Mug',
                      quantity: 3,
                      total: 24000,
                      status: 'pending',
                      createdAt: Date.now() - 3600000,
                    }} />
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* Analytics Tab */}
          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>Performance Analytics</Typography>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        📈 Engagement Metrics
                      </Typography>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Post Views
                        </Typography>
                        <Typography variant="h4" color="primary.main">
                          {(commerceData.analytics?.engagement?.totalViews || 0).toLocaleString()}
                        </Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Engagement Rate
                        </Typography>
                        <Typography variant="h4" color="success.main">
                          {(commerceData.analytics?.engagement?.engagementRate || 0).toFixed(1)}%
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        💰 Sales Metrics
                      </Typography>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Average Order Value
                        </Typography>
                        <Typography variant="h4" color="warning.main">
                          TZS {(commerceData.analytics?.overview?.averageOrderValue || 0).toLocaleString()}
                        </Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Return Customer Rate
                        </Typography>
                        <Typography variant="h4" color="info.main">
                          {(commerceData.analytics?.conversion?.returnCustomerRate || 0).toFixed(1)}%
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Reviews Tab */}
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>Customer Reviews</Typography>

              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  Customer reviews help build trust and improve your products. Respond to reviews to show you care!
                </Typography>
              </Alert>

              {/* Mock reviews */}
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {[
                  { customer: 'John Doe', rating: 5, comment: 'Excellent product! Fast delivery.', product: 'Smartphone Case' },
                  { customer: 'Mary Smith', rating: 4, comment: 'Good quality, but a bit expensive.', product: 'Leather Shoes' },
                  { customer: 'Peter Johnson', rating: 5, comment: 'Love this mug! Perfect size.', product: 'Coffee Mug' },
                ].map((review, index) => (
                  <Card key={index} variant="outlined">
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar sx={{ mr: 2 }}>{review.customer.charAt(0)}</Avatar>
                          <Box>
                            <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                              {review.customer}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {review.product}
                            </Typography>
                          </Box>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Star sx={{ color: 'gold', mr: 0.5 }} />
                          <Typography variant="body1">{review.rating}</Typography>
                        </Box>
                      </Box>
                      <Typography variant="body2">
                        {review.comment}
                      </Typography>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Add Product Dialog */}
      <Dialog open={productDialog} onClose={() => setProductDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add New Product</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Product Name"
            value={newProduct.name}
            onChange={(e) => setNewProduct({ ...newProduct, name: e.target.value })}
            sx={{ mb: 2, mt: 1 }}
          />
          <TextField
            fullWidth
            label="Description"
            multiline
            rows={3}
            value={newProduct.description}
            onChange={(e) => setNewProduct({ ...newProduct, description: e.target.value })}
            sx={{ mb: 2 }}
          />
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Price (TZS)"
                type="number"
                value={newProduct.price}
                onChange={(e) => setNewProduct({ ...newProduct, price: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Stock Quantity"
                type="number"
                value={newProduct.stock}
                onChange={(e) => setNewProduct({ ...newProduct, stock: e.target.value })}
              />
            </Grid>
          </Grid>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Category</InputLabel>
            <Select
              value={newProduct.category}
              onChange={(e) => setNewProduct({ ...newProduct, category: e.target.value })}
              label="Category"
            >
              <MenuItem value="Electronics">Electronics</MenuItem>
              <MenuItem value="Fashion">Fashion</MenuItem>
              <MenuItem value="Home">Home & Garden</MenuItem>
              <MenuItem value="Sports">Sports</MenuItem>
              <MenuItem value="Books">Books</MenuItem>
              <MenuItem value="Other">Other</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setProductDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleCreateProduct}>
            Create Product Post
          </Button>
        </DialogActions>
      </Dialog>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => setProductDialog(true)}
      >
        <Add />
      </Fab>
    </Box>
  );
};

export default SocialCommerceDashboard;
