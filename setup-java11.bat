@echo off
echo ========================================
echo ProChat Java 11 Setup Script
echo ========================================

echo.
echo Current Java version:
java -version

echo.
echo Setting JAVA_HOME to use Java 11...

REM Check if Java 11 is already installed
if exist "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\java.exe" (
    echo Java 11 found! Setting environment variables...
    set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
    set PATH=%JAVA_HOME%\bin;%PATH%
    
    echo.
    echo Testing Java 11:
    "%JAVA_HOME%\bin\java.exe" -version
    
    echo.
    echo Testing Maven with Java 11:
    "%JAVA_HOME%\bin\java.exe" -cp "%MAVEN_HOME%\boot\plexus-classworlds-2.6.0.jar" org.codehaus.plexus.classworlds.launcher.Launcher -version
    
) else (
    echo Java 11 not found in expected location.
    echo Please install Java 11 manually from:
    echo https://adoptium.net/temurin/releases/?version=11
    echo.
    echo Or use the following command:
    echo winget install EclipseAdoptium.Temurin.11.JDK
)

echo.
echo ========================================
echo Java 11 setup completed!
echo ========================================
pause
