import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView } from 'react-native';

export default function App() {
  const [activeTab, setActiveTab] = React.useState('Home');

  const tabs = [
    { name: 'Home', icon: '🏠', color: '#667eea' },
    { name: 'Chats', icon: '💬', color: '#764ba2' },
    { name: 'Discover', icon: '🔍', color: '#f093fb' },
    { name: 'Me', icon: '👤', color: '#f5576c' }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'Home':
        return (
          <ScrollView style={styles.content}>
            <View style={styles.card}>
              <Text style={styles.cardTitle}>📱 Machapisho ya Kijamii</Text>
              <View style={styles.post}>
                <Text style={styles.postAuthor}>👤 <PERSON></Text>
                <Text style={styles.postContent}>Habari za asubuhi! Leo ni siku nzuri ya kufanya biashara.</Text>
                <View style={styles.postActions}>
                  <Text style={styles.postAction}>❤️ 15</Text>
                  <Text style={styles.postAction}>💬 3</Text>
                  <Text style={styles.postAction}>📤 Share</Text>
                </View>
              </View>
              <View style={styles.post}>
                <Text style={styles.postAuthor}>👤 Jane Smith</Text>
                <Text style={styles.postContent}>Nimepata kazi mpya kupitia ProChat Jobs! Asante sana.</Text>
                <View style={styles.postActions}>
                  <Text style={styles.postAction}>❤️ 28</Text>
                  <Text style={styles.postAction}>💬 7</Text>
                  <Text style={styles.postAction}>📤 Share</Text>
                </View>
              </View>
            </View>
          </ScrollView>
        );
      case 'Chats':
        return (
          <ScrollView style={styles.content}>
            <View style={styles.card}>
              <Text style={styles.cardTitle}>💬 Mazungumzo</Text>
              <View style={styles.chatItem}>
                <Text style={styles.chatName}>👥 Family Group</Text>
                <Text style={styles.chatMessage}>Habari za leo?</Text>
                <View style={styles.badge}>
                  <Text style={styles.badgeText}>2</Text>
                </View>
              </View>
              <View style={styles.chatItem}>
                <Text style={styles.chatName}>👤 John Doe</Text>
                <Text style={styles.chatMessage}>Tutaonana kesho</Text>
              </View>
              <View style={styles.chatItem}>
                <Text style={styles.chatName}>👥 Work Team</Text>
                <Text style={styles.chatMessage}>Meeting at 2 PM</Text>
                <View style={styles.badge}>
                  <Text style={styles.badgeText}>5</Text>
                </View>
              </View>
            </View>
          </ScrollView>
        );
      case 'Discover':
        return (
          <ScrollView style={styles.content}>
            <View style={styles.card}>
              <Text style={styles.cardTitle}>🎫 Matukio</Text>
              <View style={styles.listItem}>
                <Text style={styles.itemTitle}>Muziki wa Bongo Flava</Text>
                <Text style={styles.itemDetails}>📅 2024-12-15 | 📍 Mlimani City | 💰 TZS 15,000</Text>
              </View>
              <View style={styles.listItem}>
                <Text style={styles.itemTitle}>Tech Conference Dar</Text>
                <Text style={styles.itemDetails}>📅 2024-12-20 | 📍 UDSM | 💰 TZS 25,000</Text>
              </View>
            </View>
            <View style={styles.card}>
              <Text style={styles.cardTitle}>💼 Fursa za Kazi</Text>
              <View style={styles.listItem}>
                <Text style={styles.itemTitle}>Software Developer</Text>
                <Text style={styles.itemDetails}>🏢 TechCorp TZ | 💰 TZS 800,000 - 1,200,000</Text>
              </View>
              <View style={styles.listItem}>
                <Text style={styles.itemTitle}>Marketing Manager</Text>
                <Text style={styles.itemDetails}>🏢 BrandCo | 💰 TZS 600,000 - 900,000</Text>
              </View>
            </View>
          </ScrollView>
        );
      case 'Me':
        return (
          <ScrollView style={styles.content}>
            <View style={[styles.card, styles.walletCard]}>
              <Text style={styles.walletTitle}>💰 ProPay Wallet</Text>
              <Text style={styles.balance}>TZS 250,000</Text>
              <View style={styles.walletActions}>
                <TouchableOpacity style={styles.walletBtn}>
                  <Text style={styles.walletBtnText}>📤 Tuma Pesa</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.walletBtn}>
                  <Text style={styles.walletBtnText}>📥 Pokea Pesa</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.walletBtn}>
                  <Text style={styles.walletBtnText}>🧾 Lipa Bill</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.walletBtn}>
                  <Text style={styles.walletBtnText}>📱 Airtime</Text>
                </TouchableOpacity>
              </View>
            </View>
            <View style={styles.card}>
              <Text style={styles.cardTitle}>📊 Historia ya Malipo</Text>
              <View style={styles.listItem}>
                <Text style={styles.itemTitle}>Malipo ya bidhaa</Text>
                <Text style={styles.itemDetails}>SEND_MONEY | TZS 50,000 | COMPLETED</Text>
              </View>
              <View style={styles.listItem}>
                <Text style={styles.itemTitle}>Mshahara</Text>
                <Text style={styles.itemDetails}>RECEIVE_MONEY | TZS 75,000 | COMPLETED</Text>
              </View>
            </View>
          </ScrollView>
        );
      default:
        return <Text>Tab not found</Text>;
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>🚀 ProChat</Text>
        <Text style={styles.headerSubtitle}>Tanzania's Super App</Text>
      </View>

      {/* Content */}
      <View style={styles.body}>
        {renderTabContent()}
      </View>

      {/* Bottom Tabs */}
      <View style={styles.tabBar}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.name}
            style={[
              styles.tab,
              activeTab === tab.name && { backgroundColor: tab.color }
            ]}
            onPress={() => setActiveTab(tab.name)}
          >
            <Text style={styles.tabIcon}>{tab.icon}</Text>
            <Text style={[
              styles.tabLabel,
              activeTab === tab.name && { color: 'white' }
            ]}>
              {tab.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}
