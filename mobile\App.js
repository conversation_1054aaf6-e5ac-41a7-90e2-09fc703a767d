import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { StatusBar } from 'expo-status-bar';
import { Provider as PaperProvider } from 'react-native-paper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { Alert, View, Text, Platform } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Web-specific imports
import { responsiveDimensions, deviceType } from './src/config/responsive';

// Auth Screens
import WelcomeScreen from './src/screens/auth/WelcomeScreen';
import LoginScreen from './src/screens/auth/LoginScreen';
import RegisterScreen from './src/screens/auth/RegisterScreen';
import ForgotPasswordScreen from './src/screens/auth/ForgotPasswordScreen';

// Main Tab Screens
import ChatsScreen from './src/screens/tabs/ChatsScreen';
import HomeScreen from './src/screens/tabs/HomeScreen';
import DiscoverScreen from './src/screens/tabs/DiscoverScreen';
import MeScreen from './src/screens/tabs/MeScreen';

// Chat Screens
import ChatScreen from './src/screens/chat/ChatScreen';
import ContactsScreen from './src/screens/chats/ContactsScreen';

// Home Screens
import CreatePostScreen from './src/screens/home/<USER>';
import PostDetailsScreen from './src/screens/home/<USER>';

// Discover Screens
import NewsDetailsScreen from './src/screens/discover/NewsDetailsScreen';
import VideoPlayerScreen from './src/screens/discover/VideoPlayerScreen';
import LiveStreamScreen from './src/screens/discover/LiveStreamScreen';
import EventDetailsScreen from './src/screens/discover/EventDetailsScreen';
import TicketScreen from './src/screens/discover/TicketScreen';
import JobDetailsScreen from './src/screens/discover/JobDetailsScreen';

// Me/Profile Screens
import ProfileScreen from './src/screens/me/ProfileScreen';
import EditProfileScreen from './src/screens/me/EditProfileScreen';
import ProPayScreen from './src/screens/me/ProPayScreen';
import ProZoneScreen from './src/screens/me/ProZoneScreen';
import WalletScreen from './src/screens/me/WalletScreen';
import TransactionHistoryScreen from './src/screens/me/TransactionHistoryScreen';
import SettingsScreen from './src/screens/me/SettingsScreen';
import NotificationsScreen from './src/screens/me/NotificationsScreen';
import InviteFriendsScreen from './src/screens/me/InviteFriendsScreen';
import HelpSupportScreen from './src/screens/me/HelpSupportScreen';

// Wallet Screens
import SendMoneyScreen from './src/screens/wallet/SendMoneyScreen';

// Social Screens
import GiftScreen from './src/screens/social/GiftScreen';
import DonationScreen from './src/screens/social/DonationScreen';

// Task Screens
import TaskScreen from './src/screens/tasks/TaskScreen';

// Contexts
import { AuthProvider, useAuth } from './src/contexts/AuthContext';
import { ThemeProvider } from './src/contexts/ThemeContext';
import { SocketProvider } from './src/contexts/SocketContext';

// Theme
import { theme } from './src/theme/theme';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Main Tab Navigator
function MainTabs() {
  const currentDevice = deviceType();
  const isWebDesktop = Platform.OS === 'web' && (currentDevice === 'desktop' || currentDevice === 'largeDesktop');

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Chats') {
            iconName = 'chat';
          } else if (route.name === 'Home') {
            iconName = 'home';
          } else if (route.name === 'Discover') {
            iconName = 'explore';
          } else if (route.name === 'Me') {
            iconName = 'person';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#007AFF',
        tabBarInactiveTintColor: 'gray',
        tabBarStyle: {
          backgroundColor: 'white',
          borderTopWidth: 1,
          borderTopColor: '#E0E0E0',
          height: Platform.OS === 'web' ? (isWebDesktop ? 80 : 60) : 60,
          paddingBottom: Platform.OS === 'web' ? 10 : 5,
          paddingHorizontal: isWebDesktop ? 20 : 0,
        },
        tabBarLabelStyle: {
          fontSize: Platform.OS === 'web' ? (isWebDesktop ? 14 : 12) : 12,
        },
        headerShown: false,
      })}
    >
      <Tab.Screen
        name="Chats"
        component={ChatsScreen}
        options={{ tabBarLabel: 'Mazungumzo' }}
      />
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{ tabBarLabel: 'Nyumbani' }}
      />
      <Tab.Screen
        name="Discover"
        component={DiscoverScreen}
        options={{ tabBarLabel: 'Gundua' }}
      />
      <Tab.Screen
        name="Me"
        component={MeScreen}
        options={{ tabBarLabel: 'Mimi' }}
      />
    </Tab.Navigator>
  );
}

// Auth Stack Navigator
function AuthStack() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Welcome" component={WelcomeScreen} />
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Register" component={RegisterScreen} />
      <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
    </Stack.Navigator>
  );
}

// Main App Stack
function AppStack() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="MainTabs" component={MainTabs} />

      {/* Chat Screens */}
      <Stack.Screen name="Chat" component={ChatScreen} />
      <Stack.Screen name="Contacts" component={ContactsScreen} />

      {/* Home Screens */}
      <Stack.Screen name="CreatePost" component={CreatePostScreen} />
      <Stack.Screen name="PostDetails" component={PostDetailsScreen} />

      {/* Discover Screens */}
      <Stack.Screen name="NewsDetails" component={NewsDetailsScreen} />
      <Stack.Screen name="VideoPlayer" component={VideoPlayerScreen} />
      <Stack.Screen name="LiveStream" component={LiveStreamScreen} />
      <Stack.Screen name="EventDetails" component={EventDetailsScreen} />
      <Stack.Screen name="Ticket" component={TicketScreen} />
      <Stack.Screen name="JobDetails" component={JobDetailsScreen} />

      {/* Profile/Me Screens */}
      <Stack.Screen name="Profile" component={ProfileScreen} />
      <Stack.Screen name="EditProfile" component={EditProfileScreen} />
      <Stack.Screen name="ProPay" component={ProPayScreen} />
      <Stack.Screen name="ProZone" component={ProZoneScreen} />
      <Stack.Screen name="Wallet" component={WalletScreen} />
      <Stack.Screen name="TransactionHistory" component={TransactionHistoryScreen} />
      <Stack.Screen name="Settings" component={SettingsScreen} />
      <Stack.Screen name="Notifications" component={NotificationsScreen} />
      <Stack.Screen name="InviteFriends" component={InviteFriendsScreen} />
      <Stack.Screen name="HelpSupport" component={HelpSupportScreen} />

      {/* Wallet Screens */}
      <Stack.Screen name="SendMoney" component={SendMoneyScreen} />

      {/* Social Screens */}
      <Stack.Screen name="Gift" component={GiftScreen} />
      <Stack.Screen name="Donation" component={DonationScreen} />

      {/* Task Screens */}
      <Stack.Screen name="Tasks" component={TaskScreen} />
    </Stack.Navigator>
  );
}

// Main App Component
function AppContent() {
  const { isAuthenticated, isLoading } = useAuth();
  const [isConnected, setIsConnected] = useState(true);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);
      if (!state.isConnected) {
        Alert.alert('Hakuna Mtandao', 'Hakikisha una muunganiko wa mtandao.');
      }
    });
    return () => unsubscribe();
  }, []);

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>ProChat inapakia...</Text>
      </View>
    );
  }

  return (
    <NavigationContainer>
      {isAuthenticated ? <AppStack /> : <AuthStack />}
    </NavigationContainer>
  );
}

// Root App Component
export default function App() {
  return (
    <PaperProvider theme={theme}>
      <AuthProvider>
        <ThemeProvider>
          <SocketProvider>
            <StatusBar style="auto" />
            <AppContent />
          </SocketProvider>
        </ThemeProvider>
      </AuthProvider>
    </PaperProvider>
  );
}
