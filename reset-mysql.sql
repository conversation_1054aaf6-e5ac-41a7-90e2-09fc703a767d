-- Reset MySQL password and create ProChat database
FLUSH PRIVILEGES;
ALTER USER 'root'@'localhost' IDENTIFIED BY 'Ram$0101';
FLUSH PRIVILEGES;

-- Create ProChat database
CREATE DATABASE IF NOT EXISTS prochat_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create ProChat user
CREATE USER IF NOT EXISTS 'prochat'@'localhost' IDENTIFIED BY 'Ram$0101';
GRANT ALL PRIVILEGES ON prochat_db.* TO 'prochat'@'localhost';
FLUSH PRIVILEGES;

-- Show databases to confirm
SHOW DATABASES;

-- Use ProChat database
USE prochat_db;

-- Show status
SELECT 'MySQL setup completed successfully!' as status;
