// ProChat Payment API
// Complete API endpoints for all payment operations

import axios from 'axios';
import { PAYMENT_CONFIG } from '../config/paymentConfig';
import { SecurityLogger } from '../config/security';
import walletSecurityService from '../services/walletSecurity';

class PaymentAPI {
  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
    this.apiClient = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'X-API-Version': '1.0',
      },
    });

    this.setupInterceptors();
  }

  setupInterceptors() {
    // Request interceptor
    this.apiClient.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('authToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        
        // Add request ID for tracking
        config.headers['X-Request-ID'] = this.generateRequestId();
        
        // Add timestamp
        config.headers['X-Timestamp'] = Date.now();
        
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.apiClient.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        SecurityLogger.logSecurityEvent('API_ERROR', {
          endpoint: error.config?.url,
          method: error.config?.method,
          status: error.response?.status,
          message: error.message,
        });
        return Promise.reject(error);
      }
    );
  }

  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 💰 PAYMENT OPERATIONS

  // Process payment
  async processPayment(paymentData) {
    try {
      const response = await this.apiClient.post('/payments/process', {
        ...paymentData,
        timestamp: Date.now(),
      });

      SecurityLogger.logSecurityEvent('PAYMENT_INITIATED', {
        amount: paymentData.amount,
        currency: paymentData.currency,
        provider: paymentData.provider,
        transactionId: response.data.transactionId,
      });

      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Payment processing failed');
    }
  }

  // Process withdrawal
  async processWithdrawal(withdrawalData) {
    try {
      const response = await this.apiClient.post('/payments/withdraw', {
        ...withdrawalData,
        timestamp: Date.now(),
      });

      SecurityLogger.logSecurityEvent('WITHDRAWAL_INITIATED', {
        amount: withdrawalData.amount,
        currency: withdrawalData.currency,
        provider: withdrawalData.provider,
        transactionId: response.data.transactionId,
      });

      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Withdrawal processing failed');
    }
  }

  // Get transaction status
  async getTransactionStatus(transactionId) {
    try {
      const response = await this.apiClient.get(`/payments/status/${transactionId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to get transaction status');
    }
  }

  // Get payment history
  async getPaymentHistory(filters = {}) {
    try {
      const response = await this.apiClient.get('/payments/history', {
        params: filters,
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to get payment history');
    }
  }

  // 📱 MOBILE MONEY OPERATIONS

  // M-Pesa payment
  async processMpesaPayment(paymentData) {
    try {
      const response = await this.apiClient.post('/payments/mpesa/payment', {
        ...paymentData,
        provider: 'mpesa',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'M-Pesa payment failed');
    }
  }

  // M-Pesa withdrawal
  async processMpesaWithdrawal(withdrawalData) {
    try {
      const response = await this.apiClient.post('/payments/mpesa/withdrawal', {
        ...withdrawalData,
        provider: 'mpesa',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'M-Pesa withdrawal failed');
    }
  }

  // Airtel Money payment
  async processAirtelPayment(paymentData) {
    try {
      const response = await this.apiClient.post('/payments/airtel/payment', {
        ...paymentData,
        provider: 'airtel_money',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Airtel Money payment failed');
    }
  }

  // Airtel Money withdrawal
  async processAirtelWithdrawal(withdrawalData) {
    try {
      const response = await this.apiClient.post('/payments/airtel/withdrawal', {
        ...withdrawalData,
        provider: 'airtel_money',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Airtel Money withdrawal failed');
    }
  }

  // Tigo Pesa payment
  async processTigoPayment(paymentData) {
    try {
      const response = await this.apiClient.post('/payments/tigo/payment', {
        ...paymentData,
        provider: 'tigo_pesa',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Tigo Pesa payment failed');
    }
  }

  // HaloPesa payment
  async processHaloPayment(paymentData) {
    try {
      const response = await this.apiClient.post('/payments/halopesa/payment', {
        ...paymentData,
        provider: 'halopesa',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'HaloPesa payment failed');
    }
  }

  // AzamPesa payment
  async processAzamPayment(paymentData) {
    try {
      const response = await this.apiClient.post('/payments/azampesa/payment', {
        ...paymentData,
        provider: 'azampesa',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'AzamPesa payment failed');
    }
  }

  // NARA payment
  async processNaraPayment(paymentData) {
    try {
      const response = await this.apiClient.post('/payments/nara/payment', {
        ...paymentData,
        provider: 'nara',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'NARA payment failed');
    }
  }

  // 🏦 BANKING OPERATIONS

  // CRDB Bank payment
  async processCrdbPayment(paymentData) {
    try {
      const response = await this.apiClient.post('/payments/crdb/payment', {
        ...paymentData,
        provider: 'crdb_bank',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'CRDB Bank payment failed');
    }
  }

  // NMB Bank payment
  async processNmbPayment(paymentData) {
    try {
      const response = await this.apiClient.post('/payments/nmb/payment', {
        ...paymentData,
        provider: 'nmb_bank',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'NMB Bank payment failed');
    }
  }

  // NBC Bank payment
  async processNbcPayment(paymentData) {
    try {
      const response = await this.apiClient.post('/payments/nbc/payment', {
        ...paymentData,
        provider: 'nbc_bank',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'NBC Bank payment failed');
    }
  }

  // Bank account verification
  async verifyBankAccount(accountData) {
    try {
      const response = await this.apiClient.post('/payments/bank/verify', accountData);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Bank account verification failed');
    }
  }

  // 💳 CARD OPERATIONS

  // Visa payment
  async processVisaPayment(paymentData) {
    try {
      const response = await this.apiClient.post('/payments/visa/payment', {
        ...paymentData,
        provider: 'visa',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Visa payment failed');
    }
  }

  // Mastercard payment
  async processMastercardPayment(paymentData) {
    try {
      const response = await this.apiClient.post('/payments/mastercard/payment', {
        ...paymentData,
        provider: 'mastercard',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Mastercard payment failed');
    }
  }

  // Stripe payment
  async processStripePayment(paymentData) {
    try {
      const response = await this.apiClient.post('/payments/stripe/payment', {
        ...paymentData,
        provider: 'stripe',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Stripe payment failed');
    }
  }

  // Create payment intent (for card payments)
  async createPaymentIntent(intentData) {
    try {
      const response = await this.apiClient.post('/payments/intent/create', intentData);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to create payment intent');
    }
  }

  // Confirm payment intent
  async confirmPaymentIntent(intentId, confirmationData) {
    try {
      const response = await this.apiClient.post(`/payments/intent/${intentId}/confirm`, confirmationData);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to confirm payment intent');
    }
  }

  // ₿ CRYPTOCURRENCY OPERATIONS

  // Bitcoin payment
  async processBitcoinPayment(paymentData) {
    try {
      const response = await this.apiClient.post('/payments/bitcoin/payment', {
        ...paymentData,
        provider: 'bitcoin',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Bitcoin payment failed');
    }
  }

  // USDT payment
  async processUsdtPayment(paymentData) {
    try {
      const response = await this.apiClient.post('/payments/usdt/payment', {
        ...paymentData,
        provider: 'usdt',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'USDT payment failed');
    }
  }

  // Get crypto wallet address
  async getCryptoWalletAddress(currency) {
    try {
      const response = await this.apiClient.get(`/payments/crypto/address/${currency}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to get crypto wallet address');
    }
  }

  // 📊 ANALYTICS AND REPORTING

  // Get payment statistics
  async getPaymentStats(filters = {}) {
    try {
      const response = await this.apiClient.get('/payments/stats', {
        params: filters,
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to get payment statistics');
    }
  }

  // Get provider performance
  async getProviderPerformance(provider, timeRange = '7d') {
    try {
      const response = await this.apiClient.get(`/payments/providers/${provider}/performance`, {
        params: { timeRange },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to get provider performance');
    }
  }

  // Get transaction analytics
  async getTransactionAnalytics(filters = {}) {
    try {
      const response = await this.apiClient.get('/payments/analytics', {
        params: filters,
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to get transaction analytics');
    }
  }

  // 🔧 UTILITY OPERATIONS

  // Get available payment providers
  async getAvailableProviders() {
    try {
      const response = await this.apiClient.get('/payments/providers');
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to get available providers');
    }
  }

  // Get provider information
  async getProviderInfo(provider) {
    try {
      const response = await this.apiClient.get(`/payments/providers/${provider}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to get provider information');
    }
  }

  // Calculate fees
  async calculateFees(amount, currency, provider) {
    try {
      const response = await this.apiClient.post('/payments/fees/calculate', {
        amount,
        currency,
        provider,
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to calculate fees');
    }
  }

  // Get exchange rates
  async getExchangeRates(fromCurrency, toCurrency) {
    try {
      const response = await this.apiClient.get('/payments/rates', {
        params: { from: fromCurrency, to: toCurrency },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to get exchange rates');
    }
  }

  // Validate payment data
  async validatePaymentData(paymentData) {
    try {
      const response = await this.apiClient.post('/payments/validate', paymentData);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Payment validation failed');
    }
  }

  // 🔒 SECURITY OPERATIONS

  // Verify transaction signature
  async verifyTransactionSignature(transactionId, signature) {
    try {
      const response = await this.apiClient.post('/payments/verify-signature', {
        transactionId,
        signature,
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Signature verification failed');
    }
  }

  // Report suspicious transaction
  async reportSuspiciousTransaction(transactionId, reason) {
    try {
      const response = await this.apiClient.post('/payments/report-suspicious', {
        transactionId,
        reason,
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to report suspicious transaction');
    }
  }

  // Get fraud detection results
  async getFraudDetectionResults(transactionId) {
    try {
      const response = await this.apiClient.get(`/payments/fraud-detection/${transactionId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to get fraud detection results');
    }
  }

  // 🔄 REFUND OPERATIONS

  // Process refund
  async processRefund(refundData) {
    try {
      const response = await this.apiClient.post('/payments/refund', {
        ...refundData,
        timestamp: Date.now(),
      });

      SecurityLogger.logSecurityEvent('REFUND_INITIATED', {
        originalTransactionId: refundData.originalTransactionId,
        amount: refundData.amount,
        reason: refundData.reason,
        refundId: response.data.refundId,
      });

      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Refund processing failed');
    }
  }

  // Get refund status
  async getRefundStatus(refundId) {
    try {
      const response = await this.apiClient.get(`/payments/refund/${refundId}/status`);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to get refund status');
    }
  }

  // 🔔 WEBHOOK OPERATIONS

  // Register webhook
  async registerWebhook(webhookData) {
    try {
      const response = await this.apiClient.post('/payments/webhooks/register', webhookData);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to register webhook');
    }
  }

  // Test webhook
  async testWebhook(webhookId) {
    try {
      const response = await this.apiClient.post(`/payments/webhooks/${webhookId}/test`);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to test webhook');
    }
  }

  // Error handling
  handleError(error, defaultMessage) {
    const errorMessage = error.response?.data?.message || error.message || defaultMessage;
    const errorCode = error.response?.data?.code || error.response?.status || 'UNKNOWN_ERROR';

    return {
      message: errorMessage,
      code: errorCode,
      details: error.response?.data?.details || null,
    };
  }
}

// Create singleton instance
const paymentAPI = new PaymentAPI();

export default paymentAPI;
