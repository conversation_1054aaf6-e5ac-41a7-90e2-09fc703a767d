# 🚀 ProChat Payment Integration Setup

**<PERSON><PERSON><PERSON><PERSON> kamili wa kuanzisha mifumo yote ya malipo kwenye ProChat super app.**

## 📋 <PERSON><PERSON><PERSON><PERSON> wa Mifumo ya Malipo

### ✅ MIFUMO INAYOTUMIKA

#### 📱 **MOBILE MONEY PROVIDERS**
- **M-Pesa** - Vodacom Tanzania (Mkuu wa soko)
- **Airtel Money** - Airtel Tanzania 
- **Tigo Pesa (MIXX BY YAS)** - Tigo Tanzania
- **HaloPesa** - Halotel Tanzania
- **AzamPesa** - Azam Telecom
- **NARA** - NARA Financial Services

#### 🏦 **BANKING PROVIDERS**
- **CRDB Bank** - CRDB Bank PLC
- **NMB Bank** - National Microfinance Bank
- **NBC Bank** - National Bank of Commerce

#### 💳 **INTERNATIONAL CARDS**
- **Visa** - Visa Inc. (Kimataifa)
- **Mastercard** - Mastercard Inc. (Kimataifa)
- **Stripe** - <PERSON><PERSON>kata kadi za kimataifa

#### ₿ **CRYPTOCURRENCY**
- **Bitcoin** - BTC payments
- **USDT** - Tether USD payments

---

## 🔧 Hatua za Kuanzisha

### 1. 📋 **Mahitaji ya Awali**

```bash
# 1. Clone repository
git clone https://github.com/your-repo/prochat.git
cd prochat

# 2. Install dependencies
cd web-app && npm install
cd ../backend && npm install

# 3. Setup environment variables
cp web-app/.env.example web-app/.env
cp backend/.env.example backend/.env
```

### 2. 🔑 **Kuongeza API Credentials**

#### **M-Pesa Setup:**
```env
# M-Pesa Configuration
MPESA_API_KEY=your_mpesa_api_key_here
MPESA_PUBLIC_KEY=your_mpesa_public_key_here
MPESA_SERVICE_PROVIDER_CODE=171717
MPESA_INITIATOR_IDENTIFIER=your_initiator_identifier
MPESA_SECURITY_CREDENTIAL=your_security_credential
```

**Jinsi ya kupata M-Pesa API Keys:**
1. Tembelea [Vodacom Developer Portal](https://developer.vodacom.co.tz)
2. Jisajili kama developer
3. Tengeneza application mpya
4. Pata API keys kutoka dashboard

#### **Airtel Money Setup:**
```env
# Airtel Money Configuration
AIRTEL_CLIENT_ID=your_airtel_client_id_here
AIRTEL_CLIENT_SECRET=your_airtel_client_secret_here
AIRTEL_API_KEY=your_airtel_api_key_here
```

**Jinsi ya kupata Airtel Money API Keys:**
1. Tembelea [Airtel Developer Portal](https://developers.airtel.africa)
2. Jisajili na tengeneza application
3. Pata credentials kutoka dashboard

#### **Tigo Pesa Setup:**
```env
# Tigo Pesa Configuration
TIGO_USERNAME=your_tigo_username_here
TIGO_PASSWORD=your_tigo_password_here
TIGO_API_KEY=your_tigo_api_key_here
TIGO_BRAND_ID=your_tigo_brand_id
```

#### **Banking Setup:**
```env
# CRDB Bank Configuration
CRDB_BANK_CODE=your_crdb_bank_code_here
CRDB_API_KEY=your_crdb_api_key_here
CRDB_SECRET_KEY=your_crdb_secret_key_here
CRDB_MERCHANT_ID=your_crdb_merchant_id

# NMB Bank Configuration
NMB_BANK_CODE=your_nmb_bank_code_here
NMB_API_KEY=your_nmb_api_key_here
NMB_SECRET_KEY=your_nmb_secret_key_here

# NBC Bank Configuration
NBC_BANK_CODE=your_nbc_bank_code_here
NBC_API_KEY=your_nbc_api_key_here
NBC_SECRET_KEY=your_nbc_secret_key_here
```

#### **International Cards Setup:**
```env
# Visa Configuration
VISA_USER_ID=your_visa_user_id_here
VISA_PASSWORD=your_visa_password_here
VISA_KEY_ID=your_visa_key_id_here

# Mastercard Configuration
MASTERCARD_CONSUMER_KEY=your_mastercard_consumer_key_here
MASTERCARD_PRIVATE_KEY=your_mastercard_private_key_here

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
```

### 3. 🏗️ **Database Setup**

```bash
# Start MongoDB
mongod --dbpath ./data/db

# Start Redis
redis-server

# Run database migrations
cd backend
npm run migrate
```

### 4. 🚀 **Kuanzisha Application**

```bash
# Terminal 1: Start backend
cd backend
npm run dev

# Terminal 2: Start frontend
cd web-app
npm start

# Application itakuwa available:
# Frontend: http://localhost:3000
# Backend: http://localhost:3001
```

---

## 💻 Jinsi ya Kutumia

### 1. **Basic Payment Integration**

```jsx
import React from 'react';
import PaymentForm from './components/Payment/PaymentForm';

function CheckoutPage() {
  const handlePaymentSuccess = (result) => {
    console.log('Payment successful:', result);
    // Redirect to success page
    window.location.href = '/payment-success';
  };

  const handlePaymentError = (error) => {
    console.error('Payment failed:', error);
    // Show error message to user
    alert('Malipo yameshindwa: ' + error.message);
  };

  return (
    <PaymentForm
      amount={50000} // TZS 50,000
      currency="TZS"
      description="Malipo ya bidhaa za ProChat"
      onSuccess={handlePaymentSuccess}
      onError={handlePaymentError}
      metadata={{
        userId: 'user123',
        orderId: 'order456',
        productId: 'product789',
      }}
    />
  );
}
```

### 2. **Direct API Usage**

```jsx
import paymentAPI from './api/paymentAPI';

// M-Pesa Payment
const payWithMpesa = async () => {
  try {
    const result = await paymentAPI.processMpesaPayment({
      amount: 25000,
      currency: 'TZS',
      customerPhone: '+255712345678',
      description: 'Malipo ya huduma za ProChat',
    });
    
    if (result.success) {
      console.log('Payment initiated:', result.transactionId);
    }
  } catch (error) {
    console.error('Payment error:', error);
  }
};

// Visa Card Payment
const payWithVisa = async () => {
  try {
    const result = await paymentAPI.processVisaPayment({
      amount: 100000,
      currency: 'TZS',
      cardData: {
        number: '****************',
        expiryDate: '12/25',
        cvv: '123',
        holderName: 'John Doe',
      },
      description: 'International payment',
    });
    
    if (result.success) {
      console.log('Card payment successful:', result.transactionId);
    }
  } catch (error) {
    console.error('Card payment error:', error);
  }
};
```

### 3. **Admin Dashboard Integration**

```jsx
import React from 'react';
import PaymentDashboard from './components/Admin/PaymentDashboard';

function AdminPage() {
  return (
    <div>
      <h1>ProChat Admin Panel</h1>
      <PaymentDashboard />
    </div>
  );
}
```

---

## 🔐 Usalama na Security

### 1. **Environment Variables Security**

```bash
# NEVER commit .env files to version control
echo ".env" >> .gitignore
echo "backend/.env" >> .gitignore

# Use different environments
NODE_ENV=development  # For development
NODE_ENV=production   # For production
NODE_ENV=staging      # For staging
```

### 2. **API Security Best Practices**

```javascript
// Rate limiting
const rateLimit = require('express-rate-limit');

const paymentRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // limit each IP to 50 requests per windowMs
  message: 'Too many payment requests'
});

// Input validation
const { body, validationResult } = require('express-validator');

const paymentValidation = [
  body('amount').isNumeric().withMessage('Amount must be a number'),
  body('currency').isIn(['TZS', 'USD', 'EUR']).withMessage('Invalid currency'),
  body('provider').notEmpty().withMessage('Provider required'),
];
```

### 3. **Webhook Security**

```javascript
// Verify webhook signatures
const crypto = require('crypto');

const verifyWebhookSignature = (payload, signature, secret) => {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
};
```

---

## 📊 Monitoring na Analytics

### 1. **Payment Statistics**

```javascript
// Get payment statistics
const getPaymentStats = async () => {
  try {
    const stats = await paymentAPI.getPaymentStats({
      startDate: '2024-01-01',
      endDate: '2024-12-31',
    });
    
    console.log('Payment Statistics:', {
      totalTransactions: stats.totalTransactions,
      totalVolume: stats.totalVolume,
      successRate: stats.successRate,
      averageAmount: stats.averageAmount,
    });
  } catch (error) {
    console.error('Failed to get stats:', error);
  }
};
```

### 2. **Provider Performance Monitoring**

```javascript
// Monitor provider performance
const monitorProviders = async () => {
  const providers = ['mpesa', 'airtel_money', 'tigo_pesa', 'crdb_bank'];
  
  for (const provider of providers) {
    try {
      const performance = await paymentAPI.getProviderPerformance(provider, '7d');
      
      console.log(`${provider} Performance:`, {
        successRate: performance.successRate,
        averageResponseTime: performance.averageResponseTime,
        uptime: performance.uptime,
      });
      
      // Alert if performance is below threshold
      if (performance.successRate < 0.95) {
        console.warn(`⚠️ ${provider} success rate is below 95%`);
      }
    } catch (error) {
      console.error(`Failed to get ${provider} performance:`, error);
    }
  }
};
```

---

## 🚨 Error Handling

### 1. **Common Error Scenarios**

```javascript
const handlePaymentError = (error) => {
  switch (error.code) {
    case 'INSUFFICIENT_FUNDS':
      return 'Huna pesa za kutosha kwenye akaunti yako';
    case 'INVALID_PHONE_NUMBER':
      return 'Nambari ya simu si sahihi';
    case 'TRANSACTION_TIMEOUT':
      return 'Muda wa malipo umekwisha. Jaribu tena';
    case 'PROVIDER_UNAVAILABLE':
      return 'Huduma ya malipo haipatikani kwa sasa';
    case 'NETWORK_ERROR':
      return 'Kuna tatizo la mtandao. Jaribu tena';
    default:
      return 'Kuna tatizo la kiufundi. Jaribu tena';
  }
};
```

### 2. **Retry Logic**

```javascript
const retryPayment = async (paymentData, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await paymentAPI.processPayment(paymentData);
      return result; // Success
    } catch (error) {
      console.log(`Payment attempt ${attempt} failed:`, error.message);
      
      if (attempt === maxRetries) {
        throw error; // Final attempt failed
      }
      
      // Exponential backoff
      await new Promise(resolve => 
        setTimeout(resolve, Math.pow(2, attempt) * 1000)
      );
    }
  }
};
```

---

## 🧪 Testing

### 1. **Test Credentials**

```javascript
const TEST_CREDENTIALS = {
  mpesa: {
    testPhone: '+255000000001',
    testAmount: 1000,
  },
  airtel: {
    testPhone: '+255000000002',
    testAmount: 1000,
  },
  visa: {
    testCard: '****************',
    testCvv: '123',
    testExpiry: '12/25',
  },
};
```

### 2. **Automated Testing**

```javascript
// Jest test example
describe('Payment API', () => {
  test('should process M-Pesa payment successfully', async () => {
    const paymentData = {
      amount: 1000,
      currency: 'TZS',
      customerPhone: '+255000000001',
      description: 'Test payment',
    };
    
    const result = await paymentAPI.processMpesaPayment(paymentData);
    
    expect(result.success).toBe(true);
    expect(result.transactionId).toBeDefined();
  });
});
```

---

## 📞 Msaada na Support

### **Mawasiliano ya Msaada:**
- **Email:** <EMAIL>
- **Simu:** +255 123 456 789
- **WhatsApp:** +255 987 654 321
- **Muda:** 24/7

### **Rasilimali za Ziada:**
- [M-Pesa API Documentation](https://developer.vodacom.co.tz)
- [Airtel Money API Documentation](https://developers.airtel.africa)
- [Stripe Documentation](https://stripe.com/docs)
- [ProChat Developer Portal](https://developers.prochat.co.tz)

### **GitHub Issues:**
Ikiwa unapata matatizo, tafadhali:
1. Angalia [GitHub Issues](https://github.com/your-repo/prochat/issues)
2. Tengeneza issue mpya na maelezo kamili
3. Ongeza logs na error messages

---

## ✅ Checklist ya Kumaliza Setup

- [ ] ✅ API credentials zimeongezwa kwenye .env files
- [ ] ✅ Database (MongoDB & Redis) inaendesha
- [ ] ✅ Backend server inaendesha (port 3001)
- [ ] ✅ Frontend server inaendesha (port 3000)
- [ ] ✅ Payment form inafanya kazi
- [ ] ✅ Admin dashboard inaonyesha data
- [ ] ✅ Webhooks zimesajiliwa
- [ ] ✅ Test payments zinafanya kazi
- [ ] ✅ Error handling inafanya kazi
- [ ] ✅ Security measures zimeongezwa

---

**🎉 Hongera! Mfumo wako wa malipo sasa uko tayari kutumika!**

**ProChat Payment Integration ni mfumo kamili wa malipo unaoweza kuchakata malipo kutoka kwa watoa huduma wote wakuu wa Tanzania na kimataifa. Mfumo huu una usalama wa hali ya juu, unaongozwa na AI, na una uwezo wa kukua haraka.**
