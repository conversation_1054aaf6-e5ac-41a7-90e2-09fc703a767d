// ProChat Smart Notification & Motivation System
// Keep users ENGAGED and MOTIVATED with intelligent notifications

import { SecurityLogger } from '../config/security';
import engagementRewardsService from './engagementRewards';
import offlineStorage from './offlineStorage';

class SmartNotificationService {
  constructor() {
    this.notificationQueue = new Map();
    this.userPreferences = new Map();
    this.notificationTemplates = new Map();
    this.motivationStrategies = new Map();
    this.pushSubscriptions = new Map();
    
    this.initializeNotificationSystem();
    this.startSmartNotifications();
  }

  initializeNotificationSystem() {
    // Initialize notification templates
    this.setupNotificationTemplates();
    
    // Initialize motivation strategies
    this.setupMotivationStrategies();
    
    // Initialize user preferences
    this.setupDefaultPreferences();
    
    // Request notification permission
    this.requestNotificationPermission();
  }

  setupNotificationTemplates() {
    // 💰 MONEY MOTIVATION - Notifications za pesa!
    this.notificationTemplates.set('money_earned', {
      title: '💰 Umepata Pesa!',
      templates: [
        'Hongera! Umepata TZS {amount} kwa {action}! 🎉',
        'Pesa imeingia! TZS {amount} kutoka {action} 💸',
        'Vizuri! Umepata TZS {amount} leo! 🔥',
        'Bahati nzuri! TZS {amount} imeongezeka kwenye wallet yako! 💳',
      ],
      icon: '💰',
      priority: 'high',
      sound: 'money_sound.mp3',
    });

    // 🎯 MISSION MOTIVATION
    this.notificationTemplates.set('mission_reminder', {
      title: '🎯 Kazi za Leo',
      templates: [
        'Kuna TZS {reward} zinakungoja! Maliza mission "{mission}" 🚀',
        'Karibu umaliza! Mission "{mission}" ina TZS {reward} 💪',
        'Dakika chache tu! Pata TZS {reward} kwa "{mission}" ⏰',
        'Usisahau! TZS {reward} zinakungoja mission "{mission}" 🎁',
      ],
      icon: '🎯',
      priority: 'medium',
    });

    // 🔥 STREAK MOTIVATION
    this.notificationTemplates.set('streak_motivation', {
      title: '🔥 Streak Yako!',
      templates: [
        'Siku {days} mfululizo! Endelea kupata TZS {bonus} 🔥',
        'Streak ya siku {days}! Usivunje - pata TZS {bonus} 💪',
        'Umefika siku {days}! Bonus ya TZS {bonus} inakuja! 🎉',
        'Streak nzuri! Siku {days} = TZS {bonus} bonus! 🚀',
      ],
      icon: '🔥',
      priority: 'high',
    });

    // 📈 LEVEL UP MOTIVATION
    this.notificationTemplates.set('level_up', {
      title: '🎉 Level Up!',
      templates: [
        'Hongera! Umefika Level {level} - {name}! 👑',
        'Level Up! Sasa wewe ni {name} Level {level}! 🎊',
        'Vizuri sana! Level {level} - {name} achieved! 🏆',
        'Amazing! Umepanda Level {level} - {name}! ⭐',
      ],
      icon: '🎉',
      priority: 'high',
      sound: 'level_up.mp3',
    });

    // 👥 SOCIAL MOTIVATION
    this.notificationTemplates.set('social_activity', {
      title: '👥 Shughuli za Kijamii',
      templates: [
        '{user} amependa post yako! Umepata TZS {amount} 💖',
        'Post yako imepata like {count}! TZS {amount} earned! 🔥',
        '{user} amecomment post yako! +TZS {amount} 💬',
        'Post yako inazidi kupendwa! TZS {amount} total! 📈',
      ],
      icon: '👥',
      priority: 'medium',
    });

    // 🛒 MARKETPLACE MOTIVATION
    this.notificationTemplates.set('marketplace_activity', {
      title: '🛒 Marketplace',
      templates: [
        'Bidhaa yako "{product}" imeuzwa! Umepata TZS {amount} 💰',
        'Ongeza bidhaa zaidi - pata TZS {potential} zaidi! 📈',
        'Bidhaa {count} zimeuzwa leo! Total TZS {amount} 🎉',
        'Duka lako linafanya vizuri! TZS {amount} leo! 🏪',
      ],
      icon: '🛒',
      priority: 'high',
    });

    // ⚡ URGENCY MOTIVATION
    this.notificationTemplates.set('urgency', {
      title: '⚡ Haraka!',
      templates: [
        'Masaa {hours} tu zimebaki! Pata TZS {amount} 🏃‍♂️',
        'Muda unakwisha! TZS {amount} zinakungoja! ⏰',
        'Dakika {minutes} tu! Usipoteze TZS {amount}! 🚨',
        'Last chance! TZS {amount} zinaisha leo! 💨',
      ],
      icon: '⚡',
      priority: 'urgent',
      sound: 'urgent.mp3',
    });

    // 🎁 SURPRISE REWARDS
    this.notificationTemplates.set('surprise_reward', {
      title: '🎁 Zawadi ya Ghafla!',
      templates: [
        'Surprise! Umepata TZS {amount} bila kufanya chochote! 🎊',
        'Lucky day! Bonus ya TZS {amount} kutoka ProChat! 🍀',
        'Gift! TZS {amount} kwa kuwa mtumiaji mzuri! 🎁',
        'Bahati nzuri! Random reward ya TZS {amount}! ✨',
      ],
      icon: '🎁',
      priority: 'high',
      sound: 'surprise.mp3',
    });
  }

  setupMotivationStrategies() {
    // 🧠 PSYCHOLOGICAL MOTIVATION STRATEGIES
    this.motivationStrategies.set('loss_aversion', {
      name: 'Fear of Missing Out (FOMO)',
      description: 'Watu hawapendi kupoteza fursa',
      triggers: ['streak_about_to_break', 'mission_expiring', 'limited_offer'],
      templates: [
        'Usipoteze streak ya siku {days}! Login sasa! 😱',
        'Mission inaisha masaa {hours}! TZS {amount} zitapotea! 💸',
        'Offer ya mwisho! TZS {amount} zinaisha leo! 🚨',
      ],
    });

    this.motivationStrategies.set('social_proof', {
      name: 'Social Proof',
      description: 'Watu wanafuata wengine',
      triggers: ['friend_earned', 'trending_activity', 'community_milestone'],
      templates: [
        'Rafiki yako {friend} amepata TZS {amount} leo! 👀',
        'Watu {count} wamepata TZS {total} leo! Wewe je? 🤔',
        'Trending: "{activity}" - watu wanapata TZS {amount}! 📈',
      ],
    });

    this.motivationStrategies.set('achievement_unlocked', {
      name: 'Achievement Psychology',
      description: 'Watu wanapenda kufikia malengo',
      triggers: ['milestone_reached', 'badge_earned', 'record_broken'],
      templates: [
        'Achievement Unlocked: {achievement}! TZS {amount} bonus! 🏆',
        'New Record! Umevunja rekodi yako! +TZS {amount} 📊',
        'Badge earned: {badge}! Umepata TZS {amount}! 🎖️',
      ],
    });

    this.motivationStrategies.set('progress_motivation', {
      name: 'Progress Motivation',
      description: 'Kuonyesha maendeleo',
      triggers: ['halfway_to_goal', 'almost_complete', 'daily_progress'],
      templates: [
        'Nusu njia! {progress}% ya lengo lako! TZS {remaining} zimebaki! 📊',
        'Karibu! {progress}% complete - TZS {amount} zinakuja! 🎯',
        'Maendeleo mazuri! Leo umepata TZS {amount}! 📈',
      ],
    });

    this.motivationStrategies.set('scarcity', {
      name: 'Scarcity Principle',
      description: 'Vitu vichache vinaonekana vya thamani',
      triggers: ['limited_slots', 'few_remaining', 'exclusive_offer'],
      templates: [
        'Nafasi {slots} tu zimebaki! Pata TZS {amount}! 🏃‍♂️',
        'Watu {count} tu wamebaki! TZS {amount} exclusive! 👑',
        'VIP offer kwa watu {limit} tu! TZS {amount}! ⭐',
      ],
    });
  }

  setupDefaultPreferences() {
    this.defaultPreferences = {
      money_notifications: true,
      mission_reminders: true,
      streak_alerts: true,
      social_updates: true,
      marketplace_alerts: true,
      surprise_rewards: true,
      quiet_hours: { start: 22, end: 7 }, // 10 PM to 7 AM
      frequency: 'optimal', // low, optimal, high
      motivation_style: 'balanced', // aggressive, balanced, gentle
    };
  }

  async requestNotificationPermission() {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      console.log('Notification permission:', permission);
      return permission === 'granted';
    }
    return false;
  }

  startSmartNotifications() {
    // Smart notification scheduler
    setInterval(() => {
      this.processNotificationQueue();
      this.generateSmartNotifications();
    }, 30000); // Every 30 seconds

    // Daily motivation cycle
    setInterval(() => {
      this.sendDailyMotivation();
    }, 60 * 60 * 1000); // Every hour

    // Weekly engagement boost
    setInterval(() => {
      this.sendWeeklyEngagementBoost();
    }, 7 * 24 * 60 * 60 * 1000); // Every week

    console.log('🔔 Smart Notification System: Started');
  }

  // Generate intelligent notifications based on user behavior
  async generateSmartNotifications() {
    const activeUsers = await this.getActiveUsers();
    
    for (const userId of activeUsers) {
      await this.generateUserNotifications(userId);
    }
  }

  async generateUserNotifications(userId) {
    try {
      const userEngagement = await engagementRewardsService.getUserEngagement(userId);
      const userPrefs = this.getUserPreferences(userId);
      
      // Skip if in quiet hours
      if (this.isQuietHours(userPrefs)) return;

      // Generate different types of notifications
      await this.checkMissionReminders(userId, userEngagement);
      await this.checkStreakMotivation(userId, userEngagement);
      await this.checkSocialActivity(userId);
      await this.checkMarketplaceActivity(userId);
      await this.checkSurpriseRewards(userId);
      await this.checkUrgentOpportunities(userId);

    } catch (error) {
      console.error('Failed to generate notifications for user:', userId, error);
    }
  }

  async checkMissionReminders(userId, userEngagement) {
    const dailyMissions = await engagementRewardsService.getDailyMissions(userId);
    const incompleteMissions = dailyMissions.filter(m => !m.completed);
    
    if (incompleteMissions.length > 0) {
      const mission = incompleteMissions[0]; // Pick first incomplete mission
      
      await this.queueNotification(userId, 'mission_reminder', {
        mission: mission.name,
        reward: mission.reward.cash,
        progress: mission.progress,
        required: mission.requirement.count,
      });
    }
  }

  async checkStreakMotivation(userId, userEngagement) {
    const streaks = userEngagement.streaks;
    
    // Login streak motivation
    if (streaks.login >= 3) {
      const nextBonus = this.getNextStreakBonus(streaks.login);
      
      await this.queueNotification(userId, 'streak_motivation', {
        days: streaks.login,
        bonus: nextBonus,
        type: 'login',
      });
    }

    // Streak about to break (loss aversion)
    const lastLogin = new Date(streaks.lastLogin);
    const hoursSinceLogin = (Date.now() - lastLogin.getTime()) / (1000 * 60 * 60);
    
    if (hoursSinceLogin > 20 && streaks.login >= 3) {
      await this.queueNotification(userId, 'urgency', {
        hours: Math.round(24 - hoursSinceLogin),
        amount: this.getStreakValue(streaks.login),
        type: 'streak_breaking',
      });
    }
  }

  async checkSocialActivity(userId) {
    // Check for recent likes, comments, shares on user's posts
    const recentActivity = await this.getRecentSocialActivity(userId);
    
    if (recentActivity.length > 0) {
      const totalEarnings = recentActivity.reduce((sum, activity) => sum + activity.earnings, 0);
      
      await this.queueNotification(userId, 'social_activity', {
        count: recentActivity.length,
        amount: totalEarnings,
        type: 'recent_activity',
      });
    }
  }

  async checkMarketplaceActivity(userId) {
    const marketplaceStats = await this.getMarketplaceStats(userId);
    
    if (marketplaceStats.recentSales > 0) {
      await this.queueNotification(userId, 'marketplace_activity', {
        count: marketplaceStats.recentSales,
        amount: marketplaceStats.recentEarnings,
        product: marketplaceStats.topProduct,
      });
    }
  }

  async checkSurpriseRewards(userId) {
    // Random surprise rewards to keep users engaged
    const shouldGiveSurprise = Math.random() < 0.05; // 5% chance
    
    if (shouldGiveSurprise) {
      const surpriseAmount = Math.floor(Math.random() * 500) + 100; // TZS 100-600
      
      // Actually give the reward
      await engagementRewardsService.trackUserAction(userId, 'surprise_reward', {
        amount: surpriseAmount,
      });
      
      await this.queueNotification(userId, 'surprise_reward', {
        amount: surpriseAmount,
      });
    }
  }

  async checkUrgentOpportunities(userId) {
    // Check for time-sensitive opportunities
    const urgentOpps = await this.getUrgentOpportunities(userId);
    
    for (const opp of urgentOpps) {
      await this.queueNotification(userId, 'urgency', {
        hours: opp.hoursRemaining,
        minutes: opp.minutesRemaining,
        amount: opp.potentialEarnings,
        type: opp.type,
      });
    }
  }

  async queueNotification(userId, templateType, data) {
    const userPrefs = this.getUserPreferences(userId);
    
    // Check if user wants this type of notification
    if (!this.shouldSendNotification(templateType, userPrefs)) return;

    const template = this.notificationTemplates.get(templateType);
    if (!template) return;

    // Select random template message
    const messageTemplate = template.templates[Math.floor(Math.random() * template.templates.length)];
    
    // Replace placeholders
    const message = this.replacePlaceholders(messageTemplate, data);
    
    const notification = {
      id: this.generateNotificationId(),
      userId,
      type: templateType,
      title: template.title,
      message,
      icon: template.icon,
      priority: template.priority,
      sound: template.sound,
      data,
      createdAt: Date.now(),
      scheduledFor: this.calculateOptimalTime(userId, template.priority),
      sent: false,
    };

    this.notificationQueue.set(notification.id, notification);
  }

  async processNotificationQueue() {
    const now = Date.now();
    
    for (const [notificationId, notification] of this.notificationQueue) {
      if (!notification.sent && notification.scheduledFor <= now) {
        await this.sendNotification(notification);
        notification.sent = true;
        notification.sentAt = now;
      }
    }

    // Clean up old notifications
    this.cleanupOldNotifications();
  }

  async sendNotification(notification) {
    try {
      // Browser notification
      if ('Notification' in window && Notification.permission === 'granted') {
        const browserNotification = new Notification(notification.title, {
          body: notification.message,
          icon: '/icons/prochat-icon.png',
          badge: '/icons/badge.png',
          tag: notification.type,
          data: notification.data,
        });

        browserNotification.onclick = () => {
          window.focus();
          this.handleNotificationClick(notification);
        };
      }

      // In-app notification
      await this.showInAppNotification(notification);

      // Push notification (if service worker available)
      if ('serviceWorker' in navigator) {
        await this.sendPushNotification(notification);
      }

      // Log notification
      SecurityLogger.logSecurityEvent('NOTIFICATION_SENT', {
        userId: notification.userId,
        type: notification.type,
        priority: notification.priority,
      });

    } catch (error) {
      console.error('Failed to send notification:', error);
    }
  }

  async showInAppNotification(notification) {
    // Create in-app notification element
    const notificationElement = document.createElement('div');
    notificationElement.className = 'smart-notification';
    notificationElement.innerHTML = `
      <div class="notification-content">
        <span class="notification-icon">${notification.icon}</span>
        <div class="notification-text">
          <div class="notification-title">${notification.title}</div>
          <div class="notification-message">${notification.message}</div>
        </div>
        <button class="notification-close">×</button>
      </div>
    `;

    // Add styles
    notificationElement.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 16px;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      z-index: 10000;
      max-width: 350px;
      animation: slideIn 0.3s ease-out;
    `;

    // Add to page
    document.body.appendChild(notificationElement);

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (notificationElement.parentNode) {
        notificationElement.remove();
      }
    }, 5000);

    // Close button
    notificationElement.querySelector('.notification-close').onclick = () => {
      notificationElement.remove();
    };

    // Click handler
    notificationElement.onclick = () => {
      this.handleNotificationClick(notification);
      notificationElement.remove();
    };
  }

  handleNotificationClick(notification) {
    // Handle different notification types
    switch (notification.type) {
      case 'mission_reminder':
        // Navigate to missions page
        window.location.hash = '#/missions';
        break;
      case 'marketplace_activity':
        // Navigate to marketplace
        window.location.hash = '#/marketplace';
        break;
      case 'social_activity':
        // Navigate to notifications
        window.location.hash = '#/notifications';
        break;
      default:
        // Navigate to home
        window.location.hash = '#/';
    }
  }

  // Utility methods
  replacePlaceholders(template, data) {
    let message = template;
    
    Object.keys(data).forEach(key => {
      const placeholder = `{${key}}`;
      message = message.replace(new RegExp(placeholder, 'g'), data[key]);
    });
    
    return message;
  }

  calculateOptimalTime(userId, priority) {
    const userPrefs = this.getUserPreferences(userId);
    const now = Date.now();
    
    // Immediate for urgent notifications
    if (priority === 'urgent') return now;
    
    // Check quiet hours
    if (this.isQuietHours(userPrefs)) {
      // Schedule for after quiet hours
      const tomorrow = new Date();
      tomorrow.setHours(userPrefs.quiet_hours.end, 0, 0, 0);
      return tomorrow.getTime();
    }
    
    // Optimal timing based on user activity patterns
    return now + (Math.random() * 300000); // 0-5 minutes delay
  }

  isQuietHours(userPrefs) {
    const now = new Date();
    const currentHour = now.getHours();
    const { start, end } = userPrefs.quiet_hours;
    
    if (start > end) {
      // Quiet hours cross midnight
      return currentHour >= start || currentHour < end;
    } else {
      return currentHour >= start && currentHour < end;
    }
  }

  shouldSendNotification(templateType, userPrefs) {
    const typeMap = {
      'money_earned': 'money_notifications',
      'mission_reminder': 'mission_reminders',
      'streak_motivation': 'streak_alerts',
      'social_activity': 'social_updates',
      'marketplace_activity': 'marketplace_alerts',
      'surprise_reward': 'surprise_rewards',
    };
    
    const prefKey = typeMap[templateType];
    return prefKey ? userPrefs[prefKey] : true;
  }

  getUserPreferences(userId) {
    return this.userPreferences.get(userId) || this.defaultPreferences;
  }

  generateNotificationId() {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  cleanupOldNotifications() {
    const dayAgo = Date.now() - 24 * 60 * 60 * 1000;
    
    for (const [notificationId, notification] of this.notificationQueue) {
      if (notification.createdAt < dayAgo) {
        this.notificationQueue.delete(notificationId);
      }
    }
  }

  // Mock data methods (replace with real implementations)
  async getActiveUsers() { return ['user1', 'user2', 'user3']; }
  getNextStreakBonus(streak) { return streak * 100; }
  getStreakValue(streak) { return streak * 50; }
  async getRecentSocialActivity(userId) { return []; }
  async getMarketplaceStats(userId) { return { recentSales: 0, recentEarnings: 0 }; }
  async getUrgentOpportunities(userId) { return []; }
  async sendPushNotification(notification) { /* Push notification */ }
  async sendDailyMotivation() { /* Daily motivation */ }
  async sendWeeklyEngagementBoost() { /* Weekly boost */ }

  // Public API methods
  async updateUserPreferences(userId, preferences) {
    const currentPrefs = this.getUserPreferences(userId);
    const updatedPrefs = { ...currentPrefs, ...preferences };
    this.userPreferences.set(userId, updatedPrefs);
    
    await offlineStorage.add('notification_preferences', {
      id: userId,
      preferences: updatedPrefs,
      updatedAt: Date.now(),
    });
  }

  getNotificationStats() {
    const totalNotifications = this.notificationQueue.size;
    const sentNotifications = Array.from(this.notificationQueue.values())
      .filter(n => n.sent).length;
    
    return {
      totalNotifications,
      sentNotifications,
      pendingNotifications: totalNotifications - sentNotifications,
      queueSize: totalNotifications,
    };
  }

  async testNotification(userId, type = 'money_earned') {
    await this.queueNotification(userId, type, {
      amount: 1000,
      action: 'test',
      user: 'Test User',
      count: 5,
    });
  }
}

// Create singleton instance
const smartNotificationService = new SmartNotificationService();

export default smartNotificationService;
