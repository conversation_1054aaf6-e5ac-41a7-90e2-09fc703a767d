// ProChat Enhanced Wallet Security System
// Banking-grade security for financial operations

import { SecurityLogger } from '../config/security';
import {
  ipW<PERSON>elistManager,
  deviceAuthManager,
  rbacManager,
  emergencyModeManager,
  SECURITY_LEVELS
} from '../config/bankingSecurity';
import fraudDetectionService from './fraudDetection';
import adminFinancialControlService from './adminFinancialControl';
import smartNotificationService from './smartNotifications';
import { OTPManager, EncryptionUtils } from '../config/security';

class WalletSecurityService {
  constructor() {
    this.transactionLimits = new Map();
    this.pendingTransactions = new Map();
    this.secureVault = new Map();
    this.auditTrail = [];
    this.suspendedWallets = new Set();
    
    this.initializeSecurityConfig();
  }

  initializeSecurityConfig() {
    // Default transaction limits by user level
    this.transactionLimits.set('basic', {
      dailyLimit: 100000, // TZS 100,000
      monthlyLimit: 1000000, // TZS 1,000,000
      singleTransactionLimit: 50000, // TZS 50,000
      maxTransactionsPerDay: 10,
    });

    this.transactionLimits.set('verified', {
      dailyLimit: 500000, // TZS 500,000
      monthlyLimit: 5000000, // TZS 5,000,000
      singleTransactionLimit: 200000, // TZS 200,000
      maxTransactionsPerDay: 20,
    });

    this.transactionLimits.set('premium', {
      dailyLimit: 2000000, // TZS 2,000,000
      monthlyLimit: 20000000, // TZS 20,000,000
      singleTransactionLimit: 1000000, // TZS 1,000,000
      maxTransactionsPerDay: 50,
    });
  }

  // Pre-transaction security checks
  async validateTransaction(userId, transactionData) {
    const validationResult = {
      allowed: false,
      requiresApproval: false,
      securityChecks: [],
      errors: [],
      warnings: [],
    };

    try {
      // 1. Emergency mode check
      if (emergencyModeManager.isEmergencyMode) {
        if (!emergencyModeManager.isOperationAllowed('wallet_transaction')) {
          validationResult.errors.push('Wallet transactions suspended due to emergency mode');
          return validationResult;
        }
      }

      // 2. User fraud check
      if (fraudDetectionService.isUserBlocked(userId)) {
        validationResult.errors.push('User blocked due to suspicious activity');
        return validationResult;
      }

      // 3. Wallet suspension check
      if (this.suspendedWallets.has(userId)) {
        validationResult.errors.push('Wallet temporarily suspended');
        return validationResult;
      }

      // 4. IP whitelist check for high-value transactions
      if (transactionData.amount > 100000) { // > TZS 100,000
        const ipCheck = await ipWhitelistManager.checkIPAccess(SECURITY_LEVELS.FINANCIAL);
        if (!ipCheck.allowed) {
          validationResult.errors.push('High-value transactions require secure network access');
          return validationResult;
        }
        validationResult.securityChecks.push('IP_VERIFIED');
      }

      // 5. Device authorization check
      const deviceAuth = await deviceAuthManager.authorizeDevice(SECURITY_LEVELS.FINANCIAL);
      if (!deviceAuth.authorized) {
        if (deviceAuth.requiresApproval) {
          validationResult.requiresApproval = true;
          validationResult.warnings.push('Device requires manual approval for financial operations');
        } else {
          validationResult.errors.push('Device not authorized for financial operations');
          return validationResult;
        }
      }
      validationResult.securityChecks.push('DEVICE_AUTHORIZED');

      // 6. Permission check
      if (!rbacManager.hasPermission(userId, 'wallet.send')) {
        validationResult.errors.push('User does not have permission for wallet transactions');
        return validationResult;
      }
      validationResult.securityChecks.push('PERMISSION_VERIFIED');

      // 7. Transaction limits check
      const limitsCheck = await this.checkTransactionLimits(userId, transactionData);
      if (!limitsCheck.allowed) {
        validationResult.errors.push(...limitsCheck.errors);
        return validationResult;
      }
      validationResult.securityChecks.push('LIMITS_VERIFIED');

      // 8. Fraud detection check
      const riskLevel = fraudDetectionService.getUserRiskLevel(userId);
      if (riskLevel === 'HIGH' || riskLevel === 'CRITICAL') {
        validationResult.requiresApproval = true;
        validationResult.warnings.push(`High risk user (${riskLevel}) - requires approval`);
      }
      validationResult.securityChecks.push(`RISK_ASSESSED_${riskLevel}`);

      // 9. Velocity check
      const velocityCheck = this.checkTransactionVelocity(userId, transactionData);
      if (!velocityCheck.allowed) {
        validationResult.warnings.push('High transaction velocity detected');
        validationResult.requiresApproval = true;
      }
      validationResult.securityChecks.push('VELOCITY_CHECKED');

      // 10. Pattern analysis
      const patternCheck = this.analyzeTransactionPattern(userId, transactionData);
      if (patternCheck.suspicious) {
        validationResult.warnings.push('Unusual transaction pattern detected');
        validationResult.requiresApproval = true;
      }
      validationResult.securityChecks.push('PATTERN_ANALYZED');

      // All checks passed
      validationResult.allowed = true;

      // Log security validation
      SecurityLogger.logSecurityEvent('WALLET_TRANSACTION_VALIDATED', {
        userId,
        amount: transactionData.amount,
        securityChecks: validationResult.securityChecks,
        requiresApproval: validationResult.requiresApproval,
        riskLevel,
      });

    } catch (error) {
      validationResult.errors.push('Security validation failed');
      SecurityLogger.logSecurityEvent('WALLET_VALIDATION_ERROR', {
        userId,
        error: error.message,
        transactionData,
      });
    }

    return validationResult;
  }

  // Check transaction limits
  async checkTransactionLimits(userId, transactionData) {
    const userRole = rbacManager.getUserRole(userId);
    const userLevel = this.getUserLevel(userRole);
    const limits = this.transactionLimits.get(userLevel);

    if (!limits) {
      return { allowed: false, errors: ['User level not recognized'] };
    }

    const { amount, type } = transactionData;
    const errors = [];

    // Single transaction limit
    if (amount > limits.singleTransactionLimit) {
      errors.push(`Transaction amount exceeds single transaction limit of TZS ${limits.singleTransactionLimit.toLocaleString()}`);
    }

    // Daily limits
    const todayTransactions = await this.getTodayTransactions(userId);
    const todayTotal = todayTransactions.reduce((sum, t) => sum + t.amount, 0);
    
    if (todayTotal + amount > limits.dailyLimit) {
      errors.push(`Transaction would exceed daily limit of TZS ${limits.dailyLimit.toLocaleString()}`);
    }

    if (todayTransactions.length >= limits.maxTransactionsPerDay) {
      errors.push(`Daily transaction count limit of ${limits.maxTransactionsPerDay} reached`);
    }

    // Monthly limits
    const monthTransactions = await this.getMonthTransactions(userId);
    const monthTotal = monthTransactions.reduce((sum, t) => sum + t.amount, 0);
    
    if (monthTotal + amount > limits.monthlyLimit) {
      errors.push(`Transaction would exceed monthly limit of TZS ${limits.monthlyLimit.toLocaleString()}`);
    }

    return {
      allowed: errors.length === 0,
      errors,
      limits,
      usage: {
        dailyUsed: todayTotal,
        monthlyUsed: monthTotal,
        transactionsToday: todayTransactions.length,
      },
    };
  }

  // Check transaction velocity
  checkTransactionVelocity(userId, transactionData) {
    const recentTransactions = this.getRecentTransactions(userId, 3600000); // Last hour
    const velocityThreshold = 5; // Max 5 transactions per hour

    if (recentTransactions.length >= velocityThreshold) {
      return {
        allowed: false,
        reason: 'Transaction velocity too high',
        count: recentTransactions.length,
        threshold: velocityThreshold,
      };
    }

    return { allowed: true };
  }

  // Analyze transaction patterns
  analyzeTransactionPattern(userId, transactionData) {
    const recentTransactions = this.getRecentTransactions(userId, 7 * 24 * 60 * 60 * 1000); // Last 7 days
    
    if (recentTransactions.length < 3) {
      return { suspicious: false, reason: 'Insufficient history' };
    }

    const { amount, recipient, type } = transactionData;

    // Check for unusual amount patterns
    const amounts = recentTransactions.map(t => t.amount);
    const avgAmount = amounts.reduce((a, b) => a + b, 0) / amounts.length;
    const deviation = Math.abs(amount - avgAmount) / avgAmount;

    if (deviation > 2.0) { // 200% deviation
      return {
        suspicious: true,
        reason: 'Unusual transaction amount',
        deviation,
        avgAmount,
        currentAmount: amount,
      };
    }

    // Check for unusual recipient patterns
    const recipients = recentTransactions.map(t => t.recipient);
    const isNewRecipient = !recipients.includes(recipient);
    const isLargeAmountToNewRecipient = isNewRecipient && amount > avgAmount * 1.5;

    if (isLargeAmountToNewRecipient) {
      return {
        suspicious: true,
        reason: 'Large amount to new recipient',
        amount,
        recipient,
        avgAmount,
      };
    }

    return { suspicious: false };
  }

  // Secure transaction processing
  async processSecureTransaction(userId, transactionData, authData) {
    const transactionId = this.generateTransactionId();
    
    try {
      // 1. Validate authentication
      const authValid = await this.validateTransactionAuth(userId, authData);
      if (!authValid.valid) {
        throw new Error('Authentication failed: ' + authValid.reason);
      }

      // 2. Create secure transaction record
      const secureTransaction = {
        id: transactionId,
        userId,
        ...transactionData,
        status: 'processing',
        createdAt: new Date().toISOString(),
        securityLevel: SECURITY_LEVELS.FINANCIAL,
        authMethod: authData.method,
        deviceId: authData.deviceId,
        ipAddress: authData.ipAddress,
        encrypted: true,
      };

      // 3. Encrypt sensitive data
      const encryptedData = EncryptionUtils.encryptSensitiveData(
        JSON.stringify(secureTransaction)
      );

      // 4. Store in secure vault
      this.secureVault.set(transactionId, {
        encryptedData,
        timestamp: Date.now(),
        userId,
      });

      // 5. Add to audit trail
      this.auditTrail.push({
        transactionId,
        userId,
        action: 'TRANSACTION_CREATED',
        timestamp: new Date().toISOString(),
        metadata: {
          amount: transactionData.amount,
          type: transactionData.type,
          authMethod: authData.method,
        },
      });

      // 6. Track for fraud detection
      fraudDetectionService.trackUserAction(userId, 'wallet_transaction', {
        amount: transactionData.amount,
        type: transactionData.type,
        deviceId: authData.deviceId,
        ipAddress: authData.ipAddress,
      });

      // 7. Process transaction (simulate)
      await this.executeTransaction(transactionId);

      SecurityLogger.logSecurityEvent('SECURE_TRANSACTION_PROCESSED', {
        transactionId,
        userId,
        amount: transactionData.amount,
        type: transactionData.type,
      });

      // 🔔 TRIGGER AUTOMATIC FINANCIAL NOTIFICATIONS
      await this.triggerTransactionNotifications(userId, transactionData, transactionId);

      return {
        success: true,
        transactionId,
        status: 'completed',
        newBalance: this.getUserBalance(userId), // Mock balance
      };

    } catch (error) {
      SecurityLogger.logSecurityEvent('SECURE_TRANSACTION_FAILED', {
        transactionId,
        userId,
        error: error.message,
        transactionData,
      });

      return {
        success: false,
        error: error.message,
        transactionId,
      };
    }
  }

  // Validate transaction authentication
  async validateTransactionAuth(userId, authData) {
    const { method, pin, otp, biometric, deviceId } = authData;

    switch (method) {
      case 'pin':
        return this.validatePIN(userId, pin);
      
      case 'otp':
        return this.validateOTP(userId, otp);
      
      case 'biometric':
        return this.validateBiometric(userId, biometric, deviceId);
      
      case 'multi_factor':
        return this.validateMultiFactor(userId, authData);
      
      default:
        return { valid: false, reason: 'Unknown authentication method' };
    }
  }

  validatePIN(userId, pin) {
    // In production, compare with hashed PIN from database
    const storedPIN = localStorage.getItem(`prochat_pin_${userId}`);
    if (!storedPIN) {
      return { valid: false, reason: 'PIN not set' };
    }

    const isValid = storedPIN === pin; // In production, use proper hashing
    return {
      valid: isValid,
      reason: isValid ? 'PIN valid' : 'Invalid PIN',
    };
  }

  validateOTP(userId, otp) {
    // Use OTP manager from security config
    const phoneNumber = this.getUserPhoneNumber(userId);
    const result = OTPManager.verifyOTP(phoneNumber, otp);
    
    return {
      valid: result.success,
      reason: result.success ? 'OTP valid' : result.error,
    };
  }

  validateBiometric(userId, biometric, deviceId) {
    // In production, validate biometric data
    return {
      valid: true, // Simplified for demo
      reason: 'Biometric authentication successful',
    };
  }

  validateMultiFactor(userId, authData) {
    // Validate multiple authentication factors
    const pinValid = this.validatePIN(userId, authData.pin);
    const otpValid = this.validateOTP(userId, authData.otp);

    const allValid = pinValid.valid && otpValid.valid;
    
    return {
      valid: allValid,
      reason: allValid ? 'Multi-factor authentication successful' : 'One or more factors failed',
      details: { pinValid, otpValid },
    };
  }

  // Execute transaction (simplified)
  async executeTransaction(transactionId) {
    // Simulate transaction processing
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Update transaction status
    const vaultData = this.secureVault.get(transactionId);
    if (vaultData) {
      const decryptedData = EncryptionUtils.decryptSensitiveData(vaultData.encryptedData);
      const transaction = JSON.parse(decryptedData);
      transaction.status = 'completed';
      transaction.completedAt = new Date().toISOString();
      
      // Re-encrypt and store
      const encryptedData = EncryptionUtils.encryptSensitiveData(JSON.stringify(transaction));
      this.secureVault.set(transactionId, {
        ...vaultData,
        encryptedData,
      });
    }

    // Add to audit trail
    this.auditTrail.push({
      transactionId,
      action: 'TRANSACTION_COMPLETED',
      timestamp: new Date().toISOString(),
    });
  }

  // 🔔 AUTOMATIC NOTIFICATION TRIGGERS
  async triggerTransactionNotifications(userId, transactionData, transactionId) {
    try {
      const { amount, type, recipient, description } = transactionData;

      // Determine notification type based on transaction type
      switch (type) {
        case 'transfer':
        case 'send':
          // Money sent notification
          await smartNotificationService.triggerMoneySentNotification(
            userId,
            amount,
            recipient || 'Unknown'
          );

          // If recipient is also a user, notify them
          if (recipient && recipient.startsWith('user_')) {
            await smartNotificationService.triggerMoneyReceivedNotification(
              recipient,
              amount,
              userId
            );
          }
          break;

        case 'receive':
        case 'deposit':
          // Money received notification
          await smartNotificationService.triggerMoneyReceivedNotification(
            userId,
            amount,
            'ProChat System'
          );
          break;

        case 'reward':
        case 'bonus':
          // Money earned notification
          await smartNotificationService.triggerMoneyEarnedNotification(
            userId,
            amount,
            description || 'reward'
          );
          break;

        case 'cashout':
        case 'withdrawal':
          // Cashout notification
          await smartNotificationService.triggerCashoutNotification(
            userId,
            amount
          );
          break;

        case 'admin_credit':
          // Admin added money
          await smartNotificationService.triggerMoneyReceivedNotification(
            userId,
            amount,
            'Admin'
          );
          break;

        case 'admin_debit':
          // Admin deducted money
          await smartNotificationService.triggerMoneySentNotification(
            userId,
            amount,
            'System Adjustment'
          );
          break;
      }

      // Check for low balance after transaction
      const newBalance = this.getUserBalance(userId);
      if (newBalance < 1000) {
        await smartNotificationService.triggerLowBalanceNotification(userId, newBalance);
      }

    } catch (error) {
      console.error('Failed to trigger transaction notifications:', error);
    }
  }

  // Mock method to get user balance
  getUserBalance(userId) {
    // In production, get from database
    return Math.floor(Math.random() * 50000) + 1000; // Random balance between 1K-51K
  }

  // Utility methods
  generateTransactionId() {
    return `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getUserLevel(userRole) {
    if (!userRole) return 'basic';
    
    const roleName = userRole.name?.toLowerCase() || '';
    if (roleName.includes('premium')) return 'premium';
    if (roleName.includes('verified')) return 'verified';
    return 'basic';
  }

  getUserPhoneNumber(userId) {
    // In production, get from user database
    return '+255123456789'; // Simplified
  }

  async getTodayTransactions(userId) {
    // In production, query from database
    const today = new Date().toDateString();
    return this.auditTrail
      .filter(entry => 
        entry.userId === userId && 
        entry.action === 'TRANSACTION_COMPLETED' &&
        new Date(entry.timestamp).toDateString() === today
      )
      .map(entry => ({
        amount: entry.metadata?.amount || 0,
        timestamp: entry.timestamp,
      }));
  }

  async getMonthTransactions(userId) {
    // In production, query from database
    const thisMonth = new Date().getMonth();
    const thisYear = new Date().getFullYear();
    
    return this.auditTrail
      .filter(entry => {
        if (entry.userId !== userId || entry.action !== 'TRANSACTION_COMPLETED') return false;
        const entryDate = new Date(entry.timestamp);
        return entryDate.getMonth() === thisMonth && entryDate.getFullYear() === thisYear;
      })
      .map(entry => ({
        amount: entry.metadata?.amount || 0,
        timestamp: entry.timestamp,
      }));
  }

  getRecentTransactions(userId, timeWindow) {
    const cutoff = Date.now() - timeWindow;
    return this.auditTrail
      .filter(entry => 
        entry.userId === userId && 
        entry.action === 'TRANSACTION_COMPLETED' &&
        new Date(entry.timestamp).getTime() > cutoff
      )
      .map(entry => ({
        amount: entry.metadata?.amount || 0,
        recipient: entry.metadata?.recipient,
        type: entry.metadata?.type,
        timestamp: entry.timestamp,
      }));
  }

  // Administrative methods
  suspendWallet(userId, reason) {
    this.suspendedWallets.add(userId);
    SecurityLogger.logSecurityEvent('WALLET_SUSPENDED', {
      userId,
      reason,
      timestamp: new Date().toISOString(),
    });
  }

  unsuspendWallet(userId) {
    this.suspendedWallets.delete(userId);
    SecurityLogger.logSecurityEvent('WALLET_UNSUSPENDED', {
      userId,
      timestamp: new Date().toISOString(),
    });
  }

  getAuditTrail(userId = null, limit = 100) {
    let trail = this.auditTrail;
    
    if (userId) {
      trail = trail.filter(entry => entry.userId === userId);
    }
    
    return trail
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, limit);
  }

  getSecurityStats() {
    return {
      totalTransactions: this.auditTrail.filter(e => e.action === 'TRANSACTION_COMPLETED').length,
      suspendedWallets: this.suspendedWallets.size,
      secureVaultSize: this.secureVault.size,
      auditTrailSize: this.auditTrail.length,
    };
  }
}

// Create singleton instance
const walletSecurityService = new WalletSecurityService();

export default walletSecurityService;
