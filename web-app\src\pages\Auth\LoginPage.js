import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  IconButton,
  InputAdornment,
  Divider,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Phone,
  Lock,
  Google,
  Facebook,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { useAuth } from '../../contexts/AuthContext';
import { useDeviceDetection } from '../../hooks/useDeviceDetection';

const LoginPage = () => {
  const navigate = useNavigate();
  const { login, isLoading, error } = useAuth();
  const { isMobile } = useDeviceDetection();
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const onSubmit = async (data) => {
    const result = await login(data);
    if (result.success) {
      navigate('/');
    }
  };

  const handleGoogleLogin = () => {
    // Implement Google login
    console.log('Google login clicked');
  };

  const handleFacebookLogin = () => {
    // Implement Facebook login
    console.log('Facebook login clicked');
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: isMobile 
          ? 'linear-gradient(135deg, #007AFF 0%, #4DA3FF 100%)'
          : 'linear-gradient(135deg, #007AFF 0%, #4DA3FF 50%, #FFFFFF 100%)',
        p: 2,
      }}
    >
      <Paper
        elevation={isMobile ? 0 : 8}
        sx={{
          width: '100%',
          maxWidth: 400,
          p: isMobile ? 3 : 4,
          borderRadius: isMobile ? 0 : 3,
          bgcolor: isMobile ? 'transparent' : 'background.paper',
          boxShadow: isMobile ? 'none' : '0 8px 32px rgba(0,0,0,0.1)',
        }}
      >
        {/* Logo/Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography
            variant="h3"
            sx={{
              fontWeight: 'bold',
              color: isMobile ? 'white' : 'primary.main',
              mb: 1,
            }}
          >
            ProChat
          </Typography>
          <Typography
            variant="subtitle1"
            sx={{
              color: isMobile ? 'rgba(255,255,255,0.9)' : 'text.secondary',
            }}
          >
            Karibu tena! Ingia kwenye akaunti yako
          </Typography>
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Login Form */}
        <Box component="form" onSubmit={handleSubmit(onSubmit)}>
          {/* Phone Number Field */}
          <TextField
            fullWidth
            label="Namba ya Simu"
            placeholder="+255 123 456 789"
            variant="outlined"
            margin="normal"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Phone color="action" />
                </InputAdornment>
              ),
              sx: {
                bgcolor: isMobile ? 'rgba(255,255,255,0.9)' : 'background.paper',
                borderRadius: 2,
              },
            }}
            InputLabelProps={{
              sx: {
                color: isMobile ? 'rgba(0,0,0,0.6)' : 'text.secondary',
              },
            }}
            {...register('phoneNumber', {
              required: 'Namba ya simu inahitajika',
              pattern: {
                value: /^(\+255|0)[67]\d{8}$/,
                message: 'Namba ya simu si sahihi',
              },
            })}
            error={!!errors.phoneNumber}
            helperText={errors.phoneNumber?.message}
          />

          {/* Password Field */}
          <TextField
            fullWidth
            label="Nywila"
            type={showPassword ? 'text' : 'password'}
            variant="outlined"
            margin="normal"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Lock color="action" />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowPassword(!showPassword)}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
              sx: {
                bgcolor: isMobile ? 'rgba(255,255,255,0.9)' : 'background.paper',
                borderRadius: 2,
              },
            }}
            InputLabelProps={{
              sx: {
                color: isMobile ? 'rgba(0,0,0,0.6)' : 'text.secondary',
              },
            }}
            {...register('password', {
              required: 'Nywila inahitajika',
              minLength: {
                value: 6,
                message: 'Nywila lazima iwe na angalau herufi 6',
              },
            })}
            error={!!errors.password}
            helperText={errors.password?.message}
          />

          {/* Forgot Password Link */}
          <Box sx={{ textAlign: 'right', mt: 1, mb: 3 }}>
            <Link
              to="/forgot-password"
              style={{
                color: isMobile ? 'white' : '#007AFF',
                textDecoration: 'none',
                fontSize: '0.9rem',
              }}
            >
              Umesahau nywila?
            </Link>
          </Box>

          {/* Login Button */}
          <Button
            type="submit"
            fullWidth
            variant="contained"
            size="large"
            disabled={isLoading}
            sx={{
              py: 1.5,
              borderRadius: 2,
              fontSize: '1.1rem',
              fontWeight: 'bold',
              bgcolor: isMobile ? 'white' : 'primary.main',
              color: isMobile ? 'primary.main' : 'white',
              '&:hover': {
                bgcolor: isMobile ? 'rgba(255,255,255,0.9)' : 'primary.dark',
              },
              mb: 3,
            }}
          >
            {isLoading ? (
              <CircularProgress size={24} color="inherit" />
            ) : (
              'Ingia'
            )}
          </Button>

          {/* Divider */}
          <Divider sx={{ my: 3 }}>
            <Typography
              variant="body2"
              sx={{
                color: isMobile ? 'rgba(255,255,255,0.8)' : 'text.secondary',
                px: 2,
              }}
            >
              Au ingia kwa
            </Typography>
          </Divider>

          {/* Social Login Buttons */}
          <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Google />}
              onClick={handleGoogleLogin}
              sx={{
                py: 1.2,
                borderRadius: 2,
                borderColor: isMobile ? 'rgba(255,255,255,0.5)' : 'divider',
                color: isMobile ? 'white' : 'text.primary',
                '&:hover': {
                  borderColor: isMobile ? 'white' : 'primary.main',
                  bgcolor: isMobile ? 'rgba(255,255,255,0.1)' : 'action.hover',
                },
              }}
            >
              Google
            </Button>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Facebook />}
              onClick={handleFacebookLogin}
              sx={{
                py: 1.2,
                borderRadius: 2,
                borderColor: isMobile ? 'rgba(255,255,255,0.5)' : 'divider',
                color: isMobile ? 'white' : 'text.primary',
                '&:hover': {
                  borderColor: isMobile ? 'white' : 'primary.main',
                  bgcolor: isMobile ? 'rgba(255,255,255,0.1)' : 'action.hover',
                },
              }}
            >
              Facebook
            </Button>
          </Box>

          {/* Register Link */}
          <Box sx={{ textAlign: 'center' }}>
            <Typography
              variant="body2"
              sx={{
                color: isMobile ? 'rgba(255,255,255,0.8)' : 'text.secondary',
              }}
            >
              Huna akaunti?{' '}
              <Link
                to="/register"
                style={{
                  color: isMobile ? 'white' : '#007AFF',
                  textDecoration: 'none',
                  fontWeight: 'bold',
                }}
              >
                Jisajili hapa
              </Link>
            </Typography>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default LoginPage;
