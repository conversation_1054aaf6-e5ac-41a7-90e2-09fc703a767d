import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { auth } from '../config/firebase';
import { onAuthStateChanged, signOut } from 'firebase/auth';
import { apiClient } from '../services/api';
import {
  TokenManager,
  OTPManager,
  DeviceManager,
  RateLimiter,
  SecurityLogger,
  securityConfig
} from '../config/security';
import syncService from '../services/syncService';

// Auth Context
const AuthContext = createContext();

// Auth Actions
const AUTH_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  SET_ERROR: 'SET_ERROR',
  LOGOUT: 'LOGOUT',
  UPDATE_PROFILE: 'UPDATE_PROFILE',
};

// Initial State
const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
  token: null,
  refreshToken: null,
  deviceId: null,
  requires2FA: false,
  isLocked: false,
  lockoutTime: null,
  loginAttempts: 0,
};

// Auth Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };
    
    case AUTH_ACTIONS.SET_USER:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: !!action.payload.user,
        isLoading: false,
        error: null,
      };
    
    case AUTH_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...initialState,
        isLoading: false,
      };
    
    case AUTH_ACTIONS.UPDATE_PROFILE:
      return {
        ...state,
        user: {
          ...state.user,
          ...action.payload,
        },
      };
    
    default:
      return state;
  }
};

// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Enhanced Login function with security features
  const login = async (credentials) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

      // Check rate limiting
      const rateCheck = RateLimiter.checkRateLimit('login', securityConfig.MAX_LOGIN_ATTEMPTS);
      if (!rateCheck.allowed) {
        dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: rateCheck.message });
        return { success: false, error: rateCheck.message };
      }

      // Generate device fingerprint
      const deviceId = await DeviceManager.getDeviceFingerprint();

      // Enhanced credentials with security info
      const enhancedCredentials = {
        ...credentials,
        deviceId,
        platform: 'web',
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
      };

      const response = await apiClient.post('/auth/login', enhancedCredentials);
      const { user, token, refreshToken, requires2FA } = response.data;

      // If 2FA is required
      if (requires2FA) {
        dispatch({
          type: AUTH_ACTIONS.SET_USER,
          payload: { user: null, token: null, requires2FA: true },
        });

        SecurityLogger.logSecurityEvent('LOGIN_2FA_REQUIRED', {
          userId: user.id,
          deviceId,
        });

        return { success: false, requires2FA: true, tempToken: token };
      }

      // Store tokens securely
      TokenManager.setTokens(token, refreshToken, 'web');
      DeviceManager.storeDeviceInfo(deviceId, true);

      // Clear rate limiting on successful login
      RateLimiter.clearRateLimit('login');

      dispatch({
        type: AUTH_ACTIONS.SET_USER,
        payload: { user, token, refreshToken, deviceId },
      });

      // Initialize sync service
      syncService.syncUserAction('login', { platform: 'web', deviceId });

      SecurityLogger.logSecurityEvent('LOGIN_SUCCESS', {
        userId: user.id,
        deviceId,
        platform: 'web',
      });

      return { success: true, user };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Hitilafu ya kuingia';

      SecurityLogger.logSecurityEvent('LOGIN_FAILED', {
        error: errorMessage,
        credentials: credentials.phoneNumber,
      });

      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage });
      return { success: false, error: errorMessage };
    }
  };

  // Register function
  const register = async (userData) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
      
      const response = await apiClient.post('/auth/register', userData);
      const { user, token } = response.data;
      
      // Store token in localStorage
      localStorage.setItem('prochat_token', token);
      
      dispatch({
        type: AUTH_ACTIONS.SET_USER,
        payload: { user, token },
      });
      
      return { success: true, user };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Hitilafu ya kusajili';
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage });
      return { success: false, error: errorMessage };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Sign out from Firebase
      await signOut(auth);
      
      // Clear local storage
      localStorage.removeItem('prochat_token');
      
      // Clear API client token
      apiClient.defaults.headers.common['Authorization'] = '';
      
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
      
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false, error: 'Hitilafu ya kutoka' };
    }
  };

  // Update profile function
  const updateProfile = async (profileData) => {
    try {
      const response = await apiClient.put('/auth/profile', profileData);
      const updatedUser = response.data.user;
      
      dispatch({
        type: AUTH_ACTIONS.UPDATE_PROFILE,
        payload: updatedUser,
      });
      
      return { success: true, user: updatedUser };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Hitilafu ya kusasisha wasifu';
      return { success: false, error: errorMessage };
    }
  };

  // Check authentication status
  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('prochat_token');
      
      if (!token) {
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
        return;
      }
      
      // Set token in API client
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      // Verify token with backend
      const response = await apiClient.get('/auth/me');
      const user = response.data.user;
      
      dispatch({
        type: AUTH_ACTIONS.SET_USER,
        payload: { user, token },
      });
    } catch (error) {
      console.error('Auth check failed:', error);
      
      // Clear invalid token
      localStorage.removeItem('prochat_token');
      apiClient.defaults.headers.common['Authorization'] = '';
      
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
    }
  };

  // Refresh token function
  const refreshToken = async () => {
    try {
      const response = await apiClient.post('/auth/refresh');
      const { token } = response.data;
      
      localStorage.setItem('prochat_token', token);
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      return { success: true, token };
    } catch (error) {
      console.error('Token refresh failed:', error);
      logout();
      return { success: false };
    }
  };

  // OTP Request function
  const requestOTP = async (phoneNumber, purpose = 'login') => {
    try {
      const rateCheck = RateLimiter.checkRateLimit('otp_request', 3, 300000); // 3 attempts per 5 minutes
      if (!rateCheck.allowed) {
        return { success: false, error: rateCheck.message };
      }

      const response = await apiClient.post('/auth/request-otp', {
        phoneNumber,
        purpose,
        platform: 'web',
      });

      SecurityLogger.logSecurityEvent('OTP_REQUESTED', {
        phoneNumber,
        purpose,
      });

      return { success: true, message: 'OTP imetumwa kwenye simu yako' };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Hitilafu ya kutuma OTP';
      return { success: false, error: errorMessage };
    }
  };

  // OTP Verification function
  const verifyOTP = async (phoneNumber, otp, tempToken = null) => {
    try {
      const response = await apiClient.post('/auth/verify-otp', {
        phoneNumber,
        otp,
        tempToken,
        platform: 'web',
      });

      const { user, token, refreshToken } = response.data;

      // Store tokens securely
      TokenManager.setTokens(token, refreshToken, 'web');

      dispatch({
        type: AUTH_ACTIONS.SET_USER,
        payload: { user, token, refreshToken, requires2FA: false },
      });

      SecurityLogger.logSecurityEvent('OTP_VERIFIED', {
        userId: user.id,
        phoneNumber,
      });

      return { success: true, user };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'OTP si sahihi';

      SecurityLogger.logSecurityEvent('OTP_VERIFICATION_FAILED', {
        phoneNumber,
        error: errorMessage,
      });

      return { success: false, error: errorMessage };
    }
  };

  // Biometric Authentication (for supported devices)
  const authenticateWithBiometric = async () => {
    if (!window.PublicKeyCredential) {
      return { success: false, error: 'Biometric authentication haijaunganishwa' };
    }

    try {
      const credential = await navigator.credentials.get({
        publicKey: {
          challenge: new Uint8Array(32),
          allowCredentials: [],
          userVerification: 'required',
        },
      });

      const response = await apiClient.post('/auth/biometric', {
        credentialId: credential.id,
        platform: 'web',
      });

      const { user, token, refreshToken } = response.data;

      TokenManager.setTokens(token, refreshToken, 'web');

      dispatch({
        type: AUTH_ACTIONS.SET_USER,
        payload: { user, token, refreshToken },
      });

      SecurityLogger.logSecurityEvent('BIOMETRIC_LOGIN_SUCCESS', {
        userId: user.id,
      });

      return { success: true, user };
    } catch (error) {
      SecurityLogger.logSecurityEvent('BIOMETRIC_LOGIN_FAILED', {
        error: error.message,
      });

      return { success: false, error: 'Biometric authentication imeshindwa' };
    }
  };

  // Clear error function
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: null });
  };

  // Effect to check authentication on mount
  useEffect(() => {
    checkAuth();
  }, []);

  // Effect to listen for Firebase auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
      if (firebaseUser && !state.user) {
        // Firebase user exists but no local user - sync with backend
        checkAuth();
      } else if (!firebaseUser && state.user) {
        // Firebase user signed out - clear local state
        logout();
      }
    });

    return () => unsubscribe();
  }, [state.user]);

  // Context value
  const value = {
    ...state,
    login,
    register,
    logout,
    updateProfile,
    checkAuth,
    refreshToken,
    requestOTP,
    verifyOTP,
    authenticateWithBiometric,
    clearError,
    // Security utilities
    TokenManager,
    DeviceManager,
    SecurityLogger,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};
