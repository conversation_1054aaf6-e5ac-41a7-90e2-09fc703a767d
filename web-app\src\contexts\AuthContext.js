import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { auth } from '../config/firebase';
import { onAuthStateChanged, signOut } from 'firebase/auth';
import { apiClient } from '../services/api';

// Auth Context
const AuthContext = createContext();

// Auth Actions
const AUTH_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  SET_ERROR: 'SET_ERROR',
  LOGOUT: 'LOGOUT',
  UPDATE_PROFILE: 'UPDATE_PROFILE',
};

// Initial State
const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
  token: null,
};

// Auth Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };
    
    case AUTH_ACTIONS.SET_USER:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: !!action.payload.user,
        isLoading: false,
        error: null,
      };
    
    case AUTH_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...initialState,
        isLoading: false,
      };
    
    case AUTH_ACTIONS.UPDATE_PROFILE:
      return {
        ...state,
        user: {
          ...state.user,
          ...action.payload,
        },
      };
    
    default:
      return state;
  }
};

// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Login function
  const login = async (credentials) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
      
      const response = await apiClient.post('/auth/login', credentials);
      const { user, token } = response.data;
      
      // Store token in localStorage
      localStorage.setItem('prochat_token', token);
      
      dispatch({
        type: AUTH_ACTIONS.SET_USER,
        payload: { user, token },
      });
      
      return { success: true, user };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Hitilafu ya kuingia';
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage });
      return { success: false, error: errorMessage };
    }
  };

  // Register function
  const register = async (userData) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
      
      const response = await apiClient.post('/auth/register', userData);
      const { user, token } = response.data;
      
      // Store token in localStorage
      localStorage.setItem('prochat_token', token);
      
      dispatch({
        type: AUTH_ACTIONS.SET_USER,
        payload: { user, token },
      });
      
      return { success: true, user };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Hitilafu ya kusajili';
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage });
      return { success: false, error: errorMessage };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Sign out from Firebase
      await signOut(auth);
      
      // Clear local storage
      localStorage.removeItem('prochat_token');
      
      // Clear API client token
      apiClient.defaults.headers.common['Authorization'] = '';
      
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
      
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false, error: 'Hitilafu ya kutoka' };
    }
  };

  // Update profile function
  const updateProfile = async (profileData) => {
    try {
      const response = await apiClient.put('/auth/profile', profileData);
      const updatedUser = response.data.user;
      
      dispatch({
        type: AUTH_ACTIONS.UPDATE_PROFILE,
        payload: updatedUser,
      });
      
      return { success: true, user: updatedUser };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Hitilafu ya kusasisha wasifu';
      return { success: false, error: errorMessage };
    }
  };

  // Check authentication status
  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('prochat_token');
      
      if (!token) {
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
        return;
      }
      
      // Set token in API client
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      // Verify token with backend
      const response = await apiClient.get('/auth/me');
      const user = response.data.user;
      
      dispatch({
        type: AUTH_ACTIONS.SET_USER,
        payload: { user, token },
      });
    } catch (error) {
      console.error('Auth check failed:', error);
      
      // Clear invalid token
      localStorage.removeItem('prochat_token');
      apiClient.defaults.headers.common['Authorization'] = '';
      
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
    }
  };

  // Refresh token function
  const refreshToken = async () => {
    try {
      const response = await apiClient.post('/auth/refresh');
      const { token } = response.data;
      
      localStorage.setItem('prochat_token', token);
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      return { success: true, token };
    } catch (error) {
      console.error('Token refresh failed:', error);
      logout();
      return { success: false };
    }
  };

  // Clear error function
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: null });
  };

  // Effect to check authentication on mount
  useEffect(() => {
    checkAuth();
  }, []);

  // Effect to listen for Firebase auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
      if (firebaseUser && !state.user) {
        // Firebase user exists but no local user - sync with backend
        checkAuth();
      } else if (!firebaseUser && state.user) {
        // Firebase user signed out - clear local state
        logout();
      }
    });

    return () => unsubscribe();
  }, [state.user]);

  // Context value
  const value = {
    ...state,
    login,
    register,
    logout,
    updateProfile,
    checkAuth,
    refreshToken,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};
