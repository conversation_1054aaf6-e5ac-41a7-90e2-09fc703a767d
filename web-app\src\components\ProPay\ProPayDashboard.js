// ProChat ProPay Dashboard Component
// Complete wallet and payment management system

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Divider,
  Alert,
  CircularProgress,
  LinearProgress,
} from '@mui/material';
import {
  AccountBalanceWallet,
  Send,
  QrCode,
  Receipt,
  Phone,
  Savings,
  CreditCard,
  TrendingUp,
  Security,
  Visibility,
  VisibilityOff,
  Add,
  Remove,
  SwapHoriz,
  Payment,
  CardGiftcard,
  EmojiEvents,
  Refresh,
  Settings,
  ChevronRight,
  Verified,
} from '@mui/icons-material';
import SimplePaymentForm from '../Payment/SimplePaymentForm';

const ProPayDashboard = () => {
  const [wallet, setWallet] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showBalance, setShowBalance] = useState(true);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadWalletData();
  }, []);

  const loadWalletData = async () => {
    try {
      setLoading(true);
      
      // Mock wallet data - replace with actual API calls
      const mockWallet = {
        balance: 2500000, // TZS 2.5M
        accountNumber: 'PP-2024-001234',
        isVerified: true,
        currency: 'TZS',
      };

      const mockTransactions = [
        {
          id: 'txn_001',
          type: 'DEPOSIT',
          description: 'Malipo kutoka M-Pesa',
          amount: 50000,
          createdAt: new Date().toISOString(),
          status: 'completed',
        },
        {
          id: 'txn_002',
          type: 'PAYMENT',
          description: 'Malipo ya bili ya umeme',
          amount: 25000,
          createdAt: new Date(Date.now() - 3600000).toISOString(),
          status: 'completed',
        },
        {
          id: 'txn_003',
          type: 'TRANSFER',
          description: 'Kutuma pesa kwa John Mwangi',
          amount: 15000,
          createdAt: new Date(Date.now() - 7200000).toISOString(),
          status: 'completed',
        },
        {
          id: 'txn_004',
          type: 'REWARD',
          description: 'Zawadi ya kujiunga na ProChat',
          amount: 10000,
          createdAt: new Date(Date.now() - 86400000).toISOString(),
          status: 'completed',
        },
        {
          id: 'txn_005',
          type: 'GIFT',
          description: 'Zawadi kutoka Mary Kimani',
          amount: 5000,
          createdAt: new Date(Date.now() - 172800000).toISOString(),
          status: 'completed',
        },
      ];

      setWallet(mockWallet);
      setTransactions(mockTransactions);
    } catch (error) {
      console.error('Error loading wallet data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadWalletData();
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getTransactionIcon = (type) => {
    const icons = {
      DEPOSIT: <Add sx={{ color: 'success.main' }} />,
      WITHDRAWAL: <Remove sx={{ color: 'error.main' }} />,
      TRANSFER: <SwapHoriz sx={{ color: 'info.main' }} />,
      PAYMENT: <Payment sx={{ color: 'error.main' }} />,
      GIFT: <CardGiftcard sx={{ color: 'warning.main' }} />,
      REWARD: <EmojiEvents sx={{ color: 'success.main' }} />,
    };
    return icons[type] || <AccountBalanceWallet />;
  };

  const getTransactionColor = (type) => {
    const colors = {
      DEPOSIT: 'success.main',
      REWARD: 'success.main',
      WITHDRAWAL: 'error.main',
      PAYMENT: 'error.main',
      TRANSFER: 'info.main',
      GIFT: 'warning.main',
    };
    return colors[type] || 'text.primary';
  };

  const quickActions = [
    {
      title: 'Tuma Pesa',
      icon: <Send />,
      color: 'primary.main',
      action: () => console.log('Send money'),
    },
    {
      title: 'Pokea Pesa',
      icon: <QrCode />,
      color: 'success.main',
      action: () => console.log('Receive money'),
    },
    {
      title: 'Lipa Bill',
      icon: <Receipt />,
      color: 'warning.main',
      action: () => console.log('Pay bill'),
    },
    {
      title: 'Nunua Airtime',
      icon: <Phone />,
      color: 'info.main',
      action: () => console.log('Buy airtime'),
    },
  ];

  const services = [
    {
      title: 'Akiba ya ProPay',
      description: 'Okoa pesa na upate riba ya 5% kwa mwaka',
      icon: <Savings />,
      color: 'primary.main',
    },
    {
      title: 'Kadi ya ProPay',
      description: 'Omba kadi yako ya ProPay kwa matumizi ya kila siku',
      icon: <CreditCard />,
      color: 'primary.main',
    },
    {
      title: 'Uwekezaji wa ProPay',
      description: 'Wekeza pesa zako na upate mapato ya ziada',
      icon: <TrendingUp />,
      color: 'primary.main',
    },
    {
      title: 'Bima ya ProPay',
      description: 'Linda pesa zako na familia yako',
      icon: <Security />,
      color: 'primary.main',
    },
  ];

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Inapakia ProPay...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ display: 'flex', alignItems: 'center' }}>
          <AccountBalanceWallet sx={{ mr: 2 }} />
          ProPay Wallet
        </Typography>
        <Box>
          <IconButton onClick={handleRefresh} disabled={refreshing}>
            <Refresh />
          </IconButton>
          <IconButton>
            <Settings />
          </IconButton>
        </Box>
      </Box>

      {refreshing && <LinearProgress sx={{ mb: 2 }} />}

      <Grid container spacing={3}>
        {/* Balance Card */}
        <Grid item xs={12} md={8}>
          <Card sx={{ 
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            mb: 3,
          }}>
            <CardContent sx={{ p: 4 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" sx={{ opacity: 0.9 }}>
                  Salio Lako
                </Typography>
                <IconButton 
                  onClick={() => setShowBalance(!showBalance)}
                  sx={{ color: 'white' }}
                >
                  {showBalance ? <Visibility /> : <VisibilityOff />}
                </IconButton>
              </Box>
              
              <Typography variant="h3" sx={{ fontWeight: 'bold', mb: 3 }}>
                {showBalance ? formatCurrency(wallet?.balance || 0) : '••••••••'}
              </Typography>
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Akaunti: {wallet?.accountNumber || 'N/A'}
                </Typography>
                <Chip
                  icon={<Verified />}
                  label="Imethibitishwa"
                  size="small"
                  sx={{ 
                    bgcolor: 'rgba(255,255,255,0.2)', 
                    color: 'white',
                    '& .MuiChip-icon': { color: 'white' }
                  }}
                />
              </Box>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Vitendo vya Haraka
              </Typography>
              <Grid container spacing={2}>
                {quickActions.map((action, index) => (
                  <Grid item xs={6} sm={3} key={index}>
                    <Button
                      fullWidth
                      variant="outlined"
                      onClick={action.action}
                      sx={{
                        flexDirection: 'column',
                        py: 2,
                        borderColor: action.color,
                        color: action.color,
                        '&:hover': {
                          borderColor: action.color,
                          bgcolor: `${action.color}10`,
                        },
                      }}
                    >
                      <Box sx={{ mb: 1 }}>{action.icon}</Box>
                      <Typography variant="body2">{action.title}</Typography>
                    </Button>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>

          {/* Recent Transactions */}
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Miamala ya Hivi Karibuni
                </Typography>
                <Button size="small" color="primary">
                  Ona Zote
                </Button>
              </Box>

              {transactions.length > 0 ? (
                <List>
                  {transactions.slice(0, 5).map((transaction, index) => (
                    <React.Fragment key={transaction.id}>
                      <ListItem>
                        <ListItemIcon>
                          <Avatar sx={{ bgcolor: `${getTransactionColor(transaction.type)}20` }}>
                            {getTransactionIcon(transaction.type)}
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={transaction.description}
                          secondary={new Date(transaction.createdAt).toLocaleDateString('sw-TZ')}
                        />
                        <ListItemSecondaryAction>
                          <Typography
                            variant="body2"
                            sx={{ 
                              color: getTransactionColor(transaction.type),
                              fontWeight: 'bold',
                            }}
                          >
                            {['DEPOSIT', 'REWARD', 'GIFT'].includes(transaction.type) ? '+' : '-'}
                            {formatCurrency(transaction.amount)}
                          </Typography>
                        </ListItemSecondaryAction>
                      </ListItem>
                      {index < transactions.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Receipt sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                  <Typography color="text.secondary">
                    Hakuna miamala bado
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Services Sidebar */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Huduma za ProPay
              </Typography>
              
              <List>
                {services.map((service, index) => (
                  <React.Fragment key={index}>
                    <ListItem button>
                      <ListItemIcon>
                        <Avatar sx={{ bgcolor: `${service.color}20` }}>
                          {service.icon}
                        </Avatar>
                      </ListItemIcon>
                      <ListItemText
                        primary={service.title}
                        secondary={service.description}
                      />
                      <ListItemSecondaryAction>
                        <IconButton edge="end">
                          <ChevronRight />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < services.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>

          {/* Quick Payment */}
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Malipo ya Haraka
              </Typography>
              <Button
                fullWidth
                variant="contained"
                onClick={() => setShowPaymentForm(true)}
                sx={{ mb: 2 }}
              >
                Anza Malipo
              </Button>
              <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                Lipa kwa M-Pesa, Airtel Money, Kadi za Kimataifa na zaidi
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Payment Form Dialog */}
      {showPaymentForm && (
        <Box sx={{ 
          position: 'fixed', 
          top: 0, 
          left: 0, 
          right: 0, 
          bottom: 0, 
          bgcolor: 'rgba(0,0,0,0.5)', 
          zIndex: 1300,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: 2,
        }}>
          <Box sx={{ 
            bgcolor: 'white', 
            borderRadius: 2, 
            maxWidth: 600, 
            width: '100%',
            maxHeight: '90vh',
            overflow: 'auto',
          }}>
            <SimplePaymentForm
              amount={10000}
              currency="TZS"
              description="Malipo ya ProPay"
              onSuccess={(result) => {
                console.log('Payment successful:', result);
                setShowPaymentForm(false);
                handleRefresh();
              }}
              onError={(error) => {
                console.error('Payment failed:', error);
                setShowPaymentForm(false);
              }}
            />
            <Box sx={{ p: 2, textAlign: 'center' }}>
              <Button onClick={() => setShowPaymentForm(false)}>
                Funga
              </Button>
            </Box>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default ProPayDashboard;
