// ProChat Payment Routes
// Complete backend API routes for all payment operations

const express = require('express');
const router = express.Router();
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const paymentController = require('../controllers/paymentController');
const authMiddleware = require('../middleware/authMiddleware');
const securityMiddleware = require('../middleware/securityMiddleware');

// Rate limiting for payment endpoints
const paymentRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // limit each IP to 50 requests per windowMs
  message: {
    error: 'Too many payment requests, please try again later',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Validation schemas
const paymentValidation = [
  body('amount')
    .isNumeric()
    .withMessage('Amount must be a number')
    .custom((value) => {
      if (value < 100) throw new Error('Minimum amount is TZS 100');
      if (value > ********) throw new Error('Maximum amount is TZS 10M');
      return true;
    }),
  body('currency')
    .isIn(['TZS', 'USD', 'EUR', 'KES', 'UGX', 'RWF'])
    .withMessage('Invalid currency'),
  body('provider')
    .isIn([
      'mpesa', 'airtel_money', 'tigo_pesa', 'halopesa', 'azampesa', 'nara',
      'crdb_bank', 'nmb_bank', 'nbc_bank',
      'visa', 'mastercard', 'stripe',
      'bitcoin', 'usdt'
    ])
    .withMessage('Invalid payment provider'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description too long'),
];

const phoneValidation = [
  body('customerPhone')
    .matches(/^(\+255|0)[67]\d{8}$/)
    .withMessage('Invalid Tanzanian phone number'),
];

const cardValidation = [
  body('cardData.number')
    .matches(/^\d{16}$/)
    .withMessage('Invalid card number'),
  body('cardData.expiryDate')
    .matches(/^(0[1-9]|1[0-2])\/\d{2}$/)
    .withMessage('Invalid expiry date format (MM/YY)'),
  body('cardData.cvv')
    .matches(/^\d{3,4}$/)
    .withMessage('Invalid CVV'),
  body('cardData.holderName')
    .isLength({ min: 2, max: 50 })
    .withMessage('Invalid cardholder name'),
];

// Middleware to handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array(),
    });
  }
  next();
};

// ===========================================
// GENERAL PAYMENT ROUTES
// ===========================================

// Process payment (universal endpoint)
router.post('/process',
  paymentRateLimit,
  authMiddleware.authenticate,
  securityMiddleware.validateRequest,
  paymentValidation,
  handleValidationErrors,
  paymentController.processPayment
);

// Process withdrawal
router.post('/withdraw',
  paymentRateLimit,
  authMiddleware.authenticate,
  securityMiddleware.validateRequest,
  paymentValidation,
  handleValidationErrors,
  paymentController.processWithdrawal
);

// Get transaction status
router.get('/status/:transactionId',
  authMiddleware.authenticate,
  paymentController.getTransactionStatus
);

// Get payment history
router.get('/history',
  authMiddleware.authenticate,
  paymentController.getPaymentHistory
);

// ===========================================
// MOBILE MONEY ROUTES
// ===========================================

// M-Pesa routes
router.post('/mpesa/payment',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  phoneValidation,
  handleValidationErrors,
  paymentController.processMpesaPayment
);

router.post('/mpesa/withdrawal',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  phoneValidation,
  handleValidationErrors,
  paymentController.processMpesaWithdrawal
);

// Airtel Money routes
router.post('/airtel/payment',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  phoneValidation,
  handleValidationErrors,
  paymentController.processAirtelPayment
);

router.post('/airtel/withdrawal',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  phoneValidation,
  handleValidationErrors,
  paymentController.processAirtelWithdrawal
);

// Tigo Pesa routes
router.post('/tigo/payment',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  phoneValidation,
  handleValidationErrors,
  paymentController.processTigoPayment
);

router.post('/tigo/withdrawal',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  phoneValidation,
  handleValidationErrors,
  paymentController.processTigoWithdrawal
);

// HaloPesa routes
router.post('/halopesa/payment',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  phoneValidation,
  handleValidationErrors,
  paymentController.processHaloPayment
);

// AzamPesa routes
router.post('/azampesa/payment',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  phoneValidation,
  handleValidationErrors,
  paymentController.processAzamPayment
);

// NARA routes
router.post('/nara/payment',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  phoneValidation,
  handleValidationErrors,
  paymentController.processNaraPayment
);

// ===========================================
// BANKING ROUTES
// ===========================================

// CRDB Bank routes
router.post('/crdb/payment',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  [body('customerAccount').isLength({ min: 10, max: 20 }).withMessage('Invalid account number')],
  handleValidationErrors,
  paymentController.processCrdbPayment
);

// NMB Bank routes
router.post('/nmb/payment',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  [body('customerAccount').isLength({ min: 10, max: 20 }).withMessage('Invalid account number')],
  handleValidationErrors,
  paymentController.processNmbPayment
);

// NBC Bank routes
router.post('/nbc/payment',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  [body('customerAccount').isLength({ min: 10, max: 20 }).withMessage('Invalid account number')],
  handleValidationErrors,
  paymentController.processNbcPayment
);

// Bank account verification
router.post('/bank/verify',
  authMiddleware.authenticate,
  [
    body('bankCode').isLength({ min: 3, max: 10 }).withMessage('Invalid bank code'),
    body('accountNumber').isLength({ min: 10, max: 20 }).withMessage('Invalid account number'),
  ],
  handleValidationErrors,
  paymentController.verifyBankAccount
);

// ===========================================
// CARD PAYMENT ROUTES
// ===========================================

// Visa payment
router.post('/visa/payment',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  cardValidation,
  handleValidationErrors,
  paymentController.processVisaPayment
);

// Mastercard payment
router.post('/mastercard/payment',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  cardValidation,
  handleValidationErrors,
  paymentController.processMastercardPayment
);

// Stripe payment
router.post('/stripe/payment',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  cardValidation,
  handleValidationErrors,
  paymentController.processStripePayment
);

// Create payment intent
router.post('/intent/create',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  handleValidationErrors,
  paymentController.createPaymentIntent
);

// Confirm payment intent
router.post('/intent/:intentId/confirm',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentController.confirmPaymentIntent
);

// ===========================================
// CRYPTOCURRENCY ROUTES
// ===========================================

// Bitcoin payment
router.post('/bitcoin/payment',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  [body('walletAddress').isLength({ min: 26, max: 62 }).withMessage('Invalid Bitcoin address')],
  handleValidationErrors,
  paymentController.processBitcoinPayment
);

// USDT payment
router.post('/usdt/payment',
  paymentRateLimit,
  authMiddleware.authenticate,
  paymentValidation,
  [body('walletAddress').matches(/^0x[a-fA-F0-9]{40}$/).withMessage('Invalid Ethereum address')],
  handleValidationErrors,
  paymentController.processUsdtPayment
);

// Get crypto wallet address
router.get('/crypto/address/:currency',
  authMiddleware.authenticate,
  paymentController.getCryptoWalletAddress
);

// ===========================================
// REFUND ROUTES
// ===========================================

// Process refund
router.post('/refund',
  paymentRateLimit,
  authMiddleware.authenticate,
  [
    body('originalTransactionId').notEmpty().withMessage('Original transaction ID required'),
    body('amount').isNumeric().withMessage('Amount must be a number'),
    body('reason').isLength({ min: 10, max: 500 }).withMessage('Reason required (10-500 characters)'),
  ],
  handleValidationErrors,
  paymentController.processRefund
);

// Get refund status
router.get('/refund/:refundId/status',
  authMiddleware.authenticate,
  paymentController.getRefundStatus
);

// ===========================================
// ANALYTICS AND REPORTING ROUTES
// ===========================================

// Get payment statistics
router.get('/stats',
  authMiddleware.authenticate,
  authMiddleware.requireRole(['admin', 'finance']),
  paymentController.getPaymentStats
);

// Get provider performance
router.get('/providers/:provider/performance',
  authMiddleware.authenticate,
  authMiddleware.requireRole(['admin', 'finance']),
  paymentController.getProviderPerformance
);

// Get transaction analytics
router.get('/analytics',
  authMiddleware.authenticate,
  authMiddleware.requireRole(['admin', 'finance']),
  paymentController.getTransactionAnalytics
);

// ===========================================
// UTILITY ROUTES
// ===========================================

// Get available providers
router.get('/providers',
  paymentController.getAvailableProviders
);

// Get provider information
router.get('/providers/:provider',
  paymentController.getProviderInfo
);

// Calculate fees
router.post('/fees/calculate',
  authMiddleware.authenticate,
  [
    body('amount').isNumeric().withMessage('Amount must be a number'),
    body('currency').isIn(['TZS', 'USD', 'EUR']).withMessage('Invalid currency'),
    body('provider').notEmpty().withMessage('Provider required'),
  ],
  handleValidationErrors,
  paymentController.calculateFees
);

// Get exchange rates
router.get('/rates',
  paymentController.getExchangeRates
);

// Validate payment data
router.post('/validate',
  authMiddleware.authenticate,
  paymentController.validatePaymentData
);

// ===========================================
// SECURITY ROUTES
// ===========================================

// Verify transaction signature
router.post('/verify-signature',
  authMiddleware.authenticate,
  [
    body('transactionId').notEmpty().withMessage('Transaction ID required'),
    body('signature').notEmpty().withMessage('Signature required'),
  ],
  handleValidationErrors,
  paymentController.verifyTransactionSignature
);

// Report suspicious transaction
router.post('/report-suspicious',
  authMiddleware.authenticate,
  [
    body('transactionId').notEmpty().withMessage('Transaction ID required'),
    body('reason').isLength({ min: 10 }).withMessage('Reason required'),
  ],
  handleValidationErrors,
  paymentController.reportSuspiciousTransaction
);

// Get fraud detection results
router.get('/fraud-detection/:transactionId',
  authMiddleware.authenticate,
  authMiddleware.requireRole(['admin', 'security']),
  paymentController.getFraudDetectionResults
);

// ===========================================
// WEBHOOK ROUTES
// ===========================================

// Register webhook
router.post('/webhooks/register',
  authMiddleware.authenticate,
  authMiddleware.requireRole(['admin']),
  [
    body('provider').notEmpty().withMessage('Provider required'),
    body('url').isURL().withMessage('Valid URL required'),
    body('events').isArray().withMessage('Events array required'),
  ],
  handleValidationErrors,
  paymentController.registerWebhook
);

// Test webhook
router.post('/webhooks/:webhookId/test',
  authMiddleware.authenticate,
  authMiddleware.requireRole(['admin']),
  paymentController.testWebhook
);

// Webhook endpoints for providers (no auth required, but signature verified)
router.post('/webhooks/mpesa',
  securityMiddleware.verifyWebhookSignature('mpesa'),
  paymentController.handleMpesaWebhook
);

router.post('/webhooks/airtel',
  securityMiddleware.verifyWebhookSignature('airtel'),
  paymentController.handleAirtelWebhook
);

router.post('/webhooks/tigo',
  securityMiddleware.verifyWebhookSignature('tigo'),
  paymentController.handleTigoWebhook
);

router.post('/webhooks/halopesa',
  securityMiddleware.verifyWebhookSignature('halopesa'),
  paymentController.handleHaloWebhook
);

router.post('/webhooks/azampesa',
  securityMiddleware.verifyWebhookSignature('azampesa'),
  paymentController.handleAzamWebhook
);

router.post('/webhooks/nara',
  securityMiddleware.verifyWebhookSignature('nara'),
  paymentController.handleNaraWebhook
);

router.post('/webhooks/crdb',
  securityMiddleware.verifyWebhookSignature('crdb'),
  paymentController.handleCrdbWebhook
);

router.post('/webhooks/nmb',
  securityMiddleware.verifyWebhookSignature('nmb'),
  paymentController.handleNmbWebhook
);

router.post('/webhooks/nbc',
  securityMiddleware.verifyWebhookSignature('nbc'),
  paymentController.handleNbcWebhook
);

router.post('/webhooks/visa',
  securityMiddleware.verifyWebhookSignature('visa'),
  paymentController.handleVisaWebhook
);

router.post('/webhooks/mastercard',
  securityMiddleware.verifyWebhookSignature('mastercard'),
  paymentController.handleMastercardWebhook
);

router.post('/webhooks/stripe',
  securityMiddleware.verifyWebhookSignature('stripe'),
  paymentController.handleStripeWebhook
);

// ===========================================
// ADMIN ROUTES
// ===========================================

// Get all transactions (admin only)
router.get('/admin/transactions',
  authMiddleware.authenticate,
  authMiddleware.requireRole(['admin']),
  paymentController.getAllTransactions
);

// Update transaction status (admin only)
router.patch('/admin/transactions/:transactionId/status',
  authMiddleware.authenticate,
  authMiddleware.requireRole(['admin']),
  [body('status').isIn(['pending', 'completed', 'failed', 'cancelled']).withMessage('Invalid status')],
  handleValidationErrors,
  paymentController.updateTransactionStatus
);

// Manual refund (admin only)
router.post('/admin/refund/manual',
  authMiddleware.authenticate,
  authMiddleware.requireRole(['admin']),
  [
    body('transactionId').notEmpty().withMessage('Transaction ID required'),
    body('amount').isNumeric().withMessage('Amount must be a number'),
    body('reason').isLength({ min: 10 }).withMessage('Reason required'),
  ],
  handleValidationErrors,
  paymentController.processManualRefund
);

// ===========================================
// HEALTH CHECK ROUTES
// ===========================================

// Payment system health check
router.get('/health',
  paymentController.healthCheck
);

// Provider status check
router.get('/providers/status',
  authMiddleware.authenticate,
  authMiddleware.requireRole(['admin']),
  paymentController.getProvidersStatus
);

module.exports = router;
