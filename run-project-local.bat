@echo off
echo ========================================
echo ProChat Local Development Server
echo ========================================

echo.
echo 1. Starting MySQL service...
net start mysql80

echo.
echo 2. Testing MySQL connection...
mysql -u root -p"Ram$0101" -e "SELECT 'MySQL is running!' as status;"

echo.
echo 3. Starting Redis (if available)...
redis-server --daemonize yes 2>nul || echo Redis not available, continuing without cache...

echo.
echo 4. Setting Java 11 environment (if available)...
if exist "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\java.exe" (
    set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
    set PATH=%JAVA_HOME%\bin;%PATH%
    echo Java 11 configured
) else (
    echo Using default Java version
)

echo.
echo 5. Starting Backend API Server...
start "ProChat Backend" cmd /k "cd backend && mvn spring-boot:run -Dspring-boot.run.profiles=dev"

echo.
echo 6. Waiting for backend to start...
timeout /t 30 /nobreak >nul

echo.
echo 7. Starting Web App...
start "ProChat Web App" cmd /k "cd web-app && npm start"

echo.
echo 8. Starting Admin Panel...
start "ProChat Admin" cmd /k "cd admin-panel && npm start"

echo.
echo 9. Starting Public Website...
start "ProChat Website" cmd /k "cd public-website && npm start"

echo.
echo ========================================
echo ProChat Development Environment Started!
echo ========================================
echo.
echo Services running on:
echo - Backend API: http://localhost:8080/api
echo - Web App: http://localhost:3000
echo - Admin Panel: http://localhost:3001  
echo - Public Website: http://localhost:3002
echo - Database: localhost:3306 (prochat_db)
echo.
echo Press any key to stop all services...
pause

echo.
echo Stopping all services...
taskkill /f /im node.exe 2>nul
taskkill /f /im java.exe 2>nul
taskkill /f /im mysqld.exe 2>nul
taskkill /f /im redis-server.exe 2>nul

echo.
echo All services stopped.
pause
