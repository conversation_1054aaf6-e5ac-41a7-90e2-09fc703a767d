import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Tabs,
  Tab,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Avatar,
  Chip,
  IconButton,
  Grid,
  Button,
  Badge,
} from '@mui/material';
import {
  PlayArrow,
  Event,
  Work,
  Article,
  Visibility,
  AccessTime,
  LocationOn,
  People,
  Favorite,
  Share,
  BookmarkBorder,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useDeviceDetection } from '../../hooks/useDeviceDetection';

// Mock data for discover content
const mockContent = {
  news: [
    {
      id: '1',
      title: '<PERSON><PERSON><PERSON> wa Teknolojia',
      summary: 'Mradi huu utasaidia vijana kupata mafunzo ya teknolojia...',
      image: 'https://via.placeholder.com/300x200',
      category: 'Teknolojia',
      timestamp: '2 saa zilizopita',
      views: 1234,
      source: 'Daily News',
    },
    {
      id: '2',
      title: '<PERSON><PERSON><PERSON> wa <PERSON> Unaongezeka',
      summary: 'Takwimu mpya zinaonyesha ongezeko la asilimia 6...',
      image: 'https://via.placeholder.com/300x200',
      category: 'Uchumi',
      timestamp: '4 saa zilizopita',
      views: 2567,
      source: 'Business Times',
    },
  ],
  videos: [
    {
      id: '1',
      title: 'Jinsi ya Kuanza Biashara Ndogo',
      thumbnail: 'https://via.placeholder.com/300x200',
      duration: '15:30',
      views: 45000,
      likes: 1200,
      creator: 'Business Hub TZ',
      timestamp: '1 siku iliyopita',
    },
    {
      id: '2',
      title: 'Mazingira na Mazao',
      thumbnail: 'https://via.placeholder.com/300x200',
      duration: '8:45',
      views: 23000,
      likes: 890,
      creator: 'Kilimo Smart',
      timestamp: '3 siku zilizopita',
    },
  ],
  events: [
    {
      id: '1',
      title: 'Tech Meetup Dar es Salaam',
      description: 'Mkutano wa watengenezaji programu na wabunifu...',
      image: 'https://via.placeholder.com/300x200',
      date: '2024-01-20',
      time: '14:00',
      location: 'UDSM, Dar es Salaam',
      attendees: 156,
      price: 'Bure',
      category: 'Teknolojia',
    },
    {
      id: '2',
      title: 'Semina ya Biashara',
      description: 'Jifunze jinsi ya kuongeza mapato yako...',
      image: 'https://via.placeholder.com/300x200',
      date: '2024-01-25',
      time: '09:00',
      location: 'Mlimani City, Dar es Salaam',
      attendees: 89,
      price: 'TSh 15,000',
      category: 'Biashara',
    },
  ],
  jobs: [
    {
      id: '1',
      title: 'Software Developer',
      company: 'TechCorp Tanzania',
      location: 'Dar es Salaam',
      salary: 'TSh 800,000 - 1,200,000',
      type: 'Kudumu',
      posted: '2 siku zilizopita',
      logo: 'https://via.placeholder.com/50',
    },
    {
      id: '2',
      title: 'Marketing Manager',
      company: 'StartUp Hub',
      location: 'Arusha',
      salary: 'TSh 600,000 - 900,000',
      type: 'Mkataba',
      posted: '1 wiki iliyopita',
      logo: 'https://via.placeholder.com/50',
    },
  ],
};

const DiscoverPage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { isMobile } = useDeviceDetection();
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const NewsCard = ({ news }) => (
    <Card 
      sx={{ 
        mb: 2, 
        cursor: 'pointer',
        '&:hover': { transform: 'translateY(-2px)' },
        transition: 'transform 0.2s',
      }}
      onClick={() => navigate(`/news/${news.id}`)}
    >
      <CardMedia
        component="img"
        height="160"
        image={news.image}
        alt={news.title}
      />
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Chip label={news.category} size="small" color="primary" sx={{ mr: 1 }} />
          <Typography variant="caption" color="text.secondary">
            {news.source} • {news.timestamp}
          </Typography>
        </Box>
        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
          {news.title}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {news.summary}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Visibility sx={{ fontSize: 16, mr: 0.5 }} />
            <Typography variant="caption">{news.views}</Typography>
          </Box>
          <Box>
            <IconButton size="small">
              <BookmarkBorder />
            </IconButton>
            <IconButton size="small">
              <Share />
            </IconButton>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  const VideoCard = ({ video }) => (
    <Card 
      sx={{ 
        mb: 2, 
        cursor: 'pointer',
        '&:hover': { transform: 'translateY(-2px)' },
        transition: 'transform 0.2s',
      }}
      onClick={() => navigate(`/video/${video.id}`)}
    >
      <Box sx={{ position: 'relative' }}>
        <CardMedia
          component="img"
          height="160"
          image={video.thumbnail}
          alt={video.title}
        />
        <Box sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          bgcolor: 'rgba(0,0,0,0.7)',
          borderRadius: '50%',
          p: 1,
        }}>
          <PlayArrow sx={{ color: 'white', fontSize: 32 }} />
        </Box>
        <Chip
          label={video.duration}
          size="small"
          sx={{
            position: 'absolute',
            bottom: 8,
            right: 8,
            bgcolor: 'rgba(0,0,0,0.8)',
            color: 'white',
          }}
        />
      </Box>
      <CardContent>
        <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
          {video.title}
        </Typography>
        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
          {video.creator} • {video.timestamp}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Visibility sx={{ fontSize: 16, mr: 0.5 }} />
              <Typography variant="caption">{video.views.toLocaleString()}</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Favorite sx={{ fontSize: 16, mr: 0.5 }} />
              <Typography variant="caption">{video.likes.toLocaleString()}</Typography>
            </Box>
          </Box>
          <IconButton size="small">
            <Share />
          </IconButton>
        </Box>
      </CardContent>
    </Card>
  );

  const EventCard = ({ event }) => (
    <Card 
      sx={{ 
        mb: 2, 
        cursor: 'pointer',
        '&:hover': { transform: 'translateY(-2px)' },
        transition: 'transform 0.2s',
      }}
      onClick={() => navigate(`/event/${event.id}`)}
    >
      <CardMedia
        component="img"
        height="160"
        image={event.image}
        alt={event.title}
      />
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Chip label={event.category} size="small" color="secondary" sx={{ mr: 1 }} />
          <Chip label={event.price} size="small" variant="outlined" />
        </Box>
        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
          {event.title}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {event.description}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <AccessTime sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
          <Typography variant="caption" color="text.secondary">
            {event.date} saa {event.time}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <LocationOn sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
          <Typography variant="caption" color="text.secondary">
            {event.location}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <People sx={{ fontSize: 16, mr: 0.5 }} />
            <Typography variant="caption">{event.attendees} watahudhuria</Typography>
          </Box>
          <Button size="small" variant="contained">
            Jiunge
          </Button>
        </Box>
      </CardContent>
    </Card>
  );

  const JobCard = ({ job }) => (
    <Card 
      sx={{ 
        mb: 2, 
        cursor: 'pointer',
        '&:hover': { transform: 'translateY(-2px)' },
        transition: 'transform 0.2s',
      }}
      onClick={() => navigate(`/job/${job.id}`)}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
          <Avatar src={job.logo} sx={{ mr: 2, width: 50, height: 50 }}>
            {job.company.charAt(0)}
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 0.5 }}>
              {job.title}
            </Typography>
            <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 0.5 }}>
              {job.company}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <Chip label={job.type} size="small" color="primary" />
              <Typography variant="caption" color="text.secondary">
                {job.posted}
              </Typography>
            </Box>
          </Box>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <LocationOn sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
          <Typography variant="body2" color="text.secondary">
            {job.location}
          </Typography>
        </Box>
        <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 2 }}>
          {job.salary}
        </Typography>
        <Button fullWidth variant="outlined">
          Omba Kazi
        </Button>
      </CardContent>
    </Card>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 0:
        return mockContent.news.map(news => <NewsCard key={news.id} news={news} />);
      case 1:
        return mockContent.videos.map(video => <VideoCard key={video.id} video={video} />);
      case 2:
        return mockContent.events.map(event => <EventCard key={event.id} event={event} />);
      case 3:
        return mockContent.jobs.map(job => <JobCard key={job.id} job={job} />);
      default:
        return null;
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', px: isMobile ? 1 : 2 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            '& .MuiTab-root': {
              minWidth: 'auto',
              px: isMobile ? 1 : 2,
            },
          }}
        >
          <Tab 
            icon={<Article />} 
            label="Habari" 
            iconPosition="start"
            sx={{ flexDirection: 'row', gap: 1 }}
          />
          <Tab 
            icon={<PlayArrow />} 
            label="Video" 
            iconPosition="start"
            sx={{ flexDirection: 'row', gap: 1 }}
          />
          <Tab 
            icon={<Event />} 
            label="Matukio" 
            iconPosition="start"
            sx={{ flexDirection: 'row', gap: 1 }}
          />
          <Tab 
            icon={<Work />} 
            label="Kazi" 
            iconPosition="start"
            sx={{ flexDirection: 'row', gap: 1 }}
          />
        </Tabs>
      </Box>

      {/* Content */}
      <Box sx={{ flexGrow: 1, overflow: 'auto', p: isMobile ? 1 : 2 }}>
        {renderContent()}
      </Box>
    </Box>
  );
};

export default DiscoverPage;
