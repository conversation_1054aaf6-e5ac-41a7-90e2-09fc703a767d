@echo off
echo ========================================
echo ProChat NPM Issues Fix Script
echo ========================================

echo.
echo 1. Checking Node.js and npm versions...
node --version
npm --version

echo.
echo 2. Clearing npm cache...
npm cache clean --force

echo.
echo 3. Setting npm registry to default...
npm config set registry https://registry.npmjs.org/

echo.
echo 4. Increasing npm timeout...
npm config set fetch-timeout 600000
npm config set fetch-retry-mintimeout 10000
npm config set fetch-retry-maxtimeout 60000

echo.
echo 5. Setting npm to use legacy peer deps...
npm config set legacy-peer-deps true

echo.
echo 6. Disabling strict SSL (for corporate networks)...
npm config set strict-ssl false

echo.
echo 7. Current npm configuration:
npm config list

echo.
echo 8. Testing npm connectivity...
npm ping

echo.
echo 9. Installing dependencies for web-app...
cd web-app
echo Installing web-app dependencies...
npm install --legacy-peer-deps --force --verbose

echo.
echo 10. Installing dependencies for admin-panel...
cd ..\admin-panel
echo Installing admin-panel dependencies...
npm install --legacy-peer-deps --force --verbose

echo.
echo 11. Installing dependencies for public-website...
cd ..\public-website
echo Installing public-website dependencies...
npm install --legacy-peer-deps --force --verbose

echo.
echo 12. Installing dependencies for mobile...
cd ..\mobile
echo Installing mobile dependencies...
npm install --legacy-peer-deps --force --verbose

cd ..

echo.
echo ========================================
echo NPM issues fix completed!
echo ========================================
pause
