// ProChat Advanced Admin Login Component
// Military-grade authentication for administrators

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  InputAdornment,
  IconButton,
  Stepper,
  Step,
  StepLabel,
  Chip,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Security,
  Fingerprint,
  PhoneAndroid,
  Computer,
  CheckCircle,
  Error,
  Warning,
  Shield,
  Lock,
  VpnKey,
  LocationOn,
} from '@mui/icons-material';

import { useNavigate } from 'react-router-dom';
import { useDeviceDetection } from '../../hooks/useDeviceDetection';
import adminSecurityService from '../../services/adminSecurity';
import { ipWhitelistManager, deviceAuthManager } from '../../config/bankingSecurity';

const AdminLogin = () => {
  const navigate = useNavigate();
  const { isMobile, deviceType } = useDeviceDetection();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [credentials, setCredentials] = useState({
    username: '',
    password: '',
    otp: '',
    biometric: null,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState([]);
  const [warnings, setWarnings] = useState([]);
  const [securityChecks, setSecurityChecks] = useState([]);
  const [preflightResults, setPreflightResults] = useState(null);
  const [showPreflightDialog, setShowPreflightDialog] = useState(false);

  const steps = [
    'Security Preflight',
    'Credentials',
    'Two-Factor Auth',
    'Biometric Verification',
    'Access Granted'
  ];

  useEffect(() => {
    performSecurityPreflight();
  }, []);

  const performSecurityPreflight = async () => {
    setLoading(true);
    try {
      const [ipCheck, deviceCheck] = await Promise.all([
        ipWhitelistManager.checkIPAccess(),
        deviceAuthManager.authorizeDevice(),
      ]);

      const results = {
        ip: ipCheck,
        device: deviceCheck,
        timestamp: new Date().toISOString(),
      };

      setPreflightResults(results);

      // Check if admin access is possible
      if (!ipCheck.allowed) {
        setErrors(['Admin access denied: IP address not whitelisted']);
        setShowPreflightDialog(true);
      } else if (!deviceCheck.authorized && deviceCheck.requiresApproval) {
        setWarnings(['Device requires manual approval for admin access']);
        setShowPreflightDialog(true);
      } else {
        setCurrentStep(1); // Move to credentials step
      }

    } catch (error) {
      setErrors(['Security preflight failed: ' + error.message]);
    } finally {
      setLoading(false);
    }
  };

  const handleCredentialsSubmit = async () => {
    if (!credentials.username || !credentials.password) {
      setErrors(['Username and password are required']);
      return;
    }

    setLoading(true);
    setErrors([]);
    
    try {
      // Validate credentials (first step of authentication)
      const authResult = await adminSecurityService.authenticateAdmin({
        username: credentials.username,
        password: credentials.password,
        deviceId: preflightResults?.device?.deviceId,
      });

      if (authResult.success) {
        setSecurityChecks(authResult.securityChecks);
        setCurrentStep(2); // Move to 2FA
      } else {
        setErrors(authResult.errors);
        setWarnings(authResult.warnings);
      }

    } catch (error) {
      setErrors(['Authentication failed: ' + error.message]);
    } finally {
      setLoading(false);
    }
  };

  const handleOTPSubmit = async () => {
    if (!credentials.otp) {
      setErrors(['OTP is required']);
      return;
    }

    setLoading(true);
    setErrors([]);

    try {
      // Complete authentication with OTP
      const authResult = await adminSecurityService.authenticateAdmin({
        username: credentials.username,
        password: credentials.password,
        otp: credentials.otp,
        deviceId: preflightResults?.device?.deviceId,
      });

      if (authResult.success) {
        setSecurityChecks(authResult.securityChecks);
        
        // Store admin session
        localStorage.setItem('admin_session_id', authResult.sessionId);
        localStorage.setItem('admin_username', credentials.username);
        
        // Check if biometric is available
        if (navigator.credentials && window.PublicKeyCredential) {
          setCurrentStep(3); // Move to biometric
        } else {
          setCurrentStep(4); // Skip to access granted
          setTimeout(() => {
            navigate('/admin/dashboard');
          }, 2000);
        }
      } else {
        setErrors(authResult.errors);
        setWarnings(authResult.warnings);
      }

    } catch (error) {
      setErrors(['OTP verification failed: ' + error.message]);
    } finally {
      setLoading(false);
    }
  };

  const handleBiometricAuth = async () => {
    setLoading(true);
    setErrors([]);

    try {
      // Simulate biometric authentication
      // In production, use WebAuthn API
      const biometricResult = await simulateBiometricAuth();
      
      if (biometricResult.success) {
        setCredentials({ ...credentials, biometric: biometricResult.data });
        setCurrentStep(4); // Access granted
        
        setTimeout(() => {
          navigate('/admin/dashboard');
        }, 2000);
      } else {
        setErrors(['Biometric authentication failed']);
      }

    } catch (error) {
      setErrors(['Biometric authentication error: ' + error.message]);
    } finally {
      setLoading(false);
    }
  };

  const simulateBiometricAuth = async () => {
    // Simulate biometric authentication delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Simulate success (in production, use actual WebAuthn)
    return {
      success: true,
      data: {
        type: 'fingerprint',
        timestamp: Date.now(),
      },
    };
  };

  const skipBiometric = () => {
    setCurrentStep(4);
    setTimeout(() => {
      navigate('/admin/dashboard');
    }, 2000);
  };

  const SecurityCheckItem = ({ check, status = 'success' }) => {
    const icons = {
      success: <CheckCircle color="success" />,
      warning: <Warning color="warning" />,
      error: <Error color="error" />,
    };

    return (
      <ListItem>
        <ListItemIcon>
          {icons[status]}
        </ListItemIcon>
        <ListItemText primary={check} />
      </ListItem>
    );
  };

  const PreflightDialog = () => (
    <Dialog open={showPreflightDialog} onClose={() => setShowPreflightDialog(false)}>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Shield sx={{ mr: 1 }} />
          Security Preflight Results
        </Box>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body1" sx={{ mb: 2 }}>
          Security checks completed. Review the results below:
        </Typography>

        <List>
          <SecurityCheckItem
            check={`IP Address: ${preflightResults?.ip?.ip || 'Unknown'}`}
            status={preflightResults?.ip?.allowed ? 'success' : 'error'}
          />
          <SecurityCheckItem
            check={`Device Authorization: ${preflightResults?.device?.authorized ? 'Authorized' : 'Pending'}`}
            status={preflightResults?.device?.authorized ? 'success' : 'warning'}
          />
          <SecurityCheckItem
            check={`Device Type: ${deviceType}`}
            status="success"
          />
          <SecurityCheckItem
            check={`Security Level: Admin Access`}
            status="success"
          />
        </List>

        {errors.length > 0 && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {errors.map((error, index) => (
              <div key={index}>{error}</div>
            ))}
          </Alert>
        )}

        {warnings.length > 0 && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            {warnings.map((warning, index) => (
              <div key={index}>{warning}</div>
            ))}
          </Alert>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setShowPreflightDialog(false)}>
          Close
        </Button>
        {preflightResults?.ip?.allowed && (
          <Button
            variant="contained"
            onClick={() => {
              setShowPreflightDialog(false);
              setCurrentStep(1);
            }}
          >
            Continue to Login
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'grey.100',
        p: 2,
      }}
    >
      <Card sx={{ maxWidth: 500, width: '100%' }}>
        <CardContent sx={{ p: 4 }}>
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Security sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              ProChat Admin
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Military-Grade Administrative Access
            </Typography>
          </Box>

          {/* Progress Stepper */}
          <Stepper activeStep={currentStep} sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{!isMobile && label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {/* Loading Progress */}
          {loading && <LinearProgress sx={{ mb: 2 }} />}

          {/* Error Messages */}
          {errors.length > 0 && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {errors.map((error, index) => (
                <div key={index}>{error}</div>
              ))}
            </Alert>
          )}

          {/* Warning Messages */}
          {warnings.length > 0 && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              {warnings.map((warning, index) => (
                <div key={index}>{warning}</div>
              ))}
            </Alert>
          )}

          {/* Step 0: Security Preflight */}
          {currentStep === 0 && (
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Performing Security Checks...
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Validating IP address, device authorization, and security policies
              </Typography>
            </Box>
          )}

          {/* Step 1: Credentials */}
          {currentStep === 1 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>
                Administrator Credentials
              </Typography>
              
              <TextField
                fullWidth
                label="Username"
                value={credentials.username}
                onChange={(e) => setCredentials({ ...credentials, username: e.target.value })}
                sx={{ mb: 2 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Security />
                    </InputAdornment>
                  ),
                }}
              />

              <TextField
                fullWidth
                label="Password"
                type={showPassword ? 'text' : 'password'}
                value={credentials.password}
                onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
                sx={{ mb: 3 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Lock />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />

              <Button
                fullWidth
                variant="contained"
                size="large"
                onClick={handleCredentialsSubmit}
                disabled={loading}
              >
                Verify Credentials
              </Button>
            </Box>
          )}

          {/* Step 2: Two-Factor Authentication */}
          {currentStep === 2 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Two-Factor Authentication
              </Typography>
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Enter the 6-digit code from your authenticator app
              </Typography>

              <TextField
                fullWidth
                label="Authentication Code"
                value={credentials.otp}
                onChange={(e) => setCredentials({ ...credentials, otp: e.target.value })}
                sx={{ mb: 3 }}
                inputProps={{ maxLength: 6, style: { textAlign: 'center', fontSize: '1.5rem' } }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <PhoneAndroid />
                    </InputAdornment>
                  ),
                }}
              />

              <Button
                fullWidth
                variant="contained"
                size="large"
                onClick={handleOTPSubmit}
                disabled={loading || credentials.otp.length !== 6}
              >
                Verify Code
              </Button>
            </Box>
          )}

          {/* Step 3: Biometric Verification */}
          {currentStep === 3 && (
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Biometric Verification
              </Typography>
              
              <Fingerprint sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Place your finger on the sensor or use your device's biometric authentication
              </Typography>

              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  variant="outlined"
                  onClick={skipBiometric}
                  disabled={loading}
                  sx={{ flex: 1 }}
                >
                  Skip
                </Button>
                <Button
                  variant="contained"
                  onClick={handleBiometricAuth}
                  disabled={loading}
                  sx={{ flex: 1 }}
                >
                  Authenticate
                </Button>
              </Box>
            </Box>
          )}

          {/* Step 4: Access Granted */}
          {currentStep === 4 && (
            <Box sx={{ textAlign: 'center' }}>
              <CheckCircle sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
              
              <Typography variant="h6" sx={{ mb: 2 }}>
                Access Granted
              </Typography>
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Welcome to ProChat Admin Dashboard. Redirecting...
              </Typography>

              {/* Security Checks Summary */}
              <Box sx={{ textAlign: 'left', mb: 3 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  Security Checks Completed:
                </Typography>
                {securityChecks.map((check, index) => (
                  <Chip
                    key={index}
                    label={check}
                    color="success"
                    size="small"
                    sx={{ mr: 1, mb: 1 }}
                  />
                ))}
              </Box>
            </Box>
          )}

          {/* Security Information */}
          <Box sx={{ mt: 4, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="caption" color="text.secondary">
              🔒 This is a secure administrative interface protected by military-grade security.
              All access attempts are logged and monitored.
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* Preflight Results Dialog */}
      <PreflightDialog />
    </Box>
  );
};

export default AdminLogin;
