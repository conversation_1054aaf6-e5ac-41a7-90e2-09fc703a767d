{"version": 3, "sources": ["../../../src/utils/tar.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport { extract as tarExtract } from 'tar';\n\nimport * as Log from '../log';\n\nconst debug = require('debug')('expo:utils:tar') as typeof console.log;\n\n/** Extract a tar using built-in tools if available and falling back on Node.js. */\nexport async function extractAsync(input: string, output: string): Promise<void> {\n  try {\n    if (process.platform !== 'win32') {\n      debug(`Extracting ${input} to ${output}`);\n      await spawnAsync('tar', ['-xf', input, '-C', output], {\n        stdio: 'inherit',\n      });\n      return;\n    }\n  } catch (error: any) {\n    Log.warn(\n      `Failed to extract tar using native tools, falling back on JS tar module. ${error.message}`\n    );\n  }\n  debug(`Extracting ${input} to ${output} using JS tar module`);\n  // tar node module has previously had problems with big files, and seems to\n  // be slower, so only use it as a backup.\n  await tarExtract({ file: input, cwd: output });\n}\n"], "names": ["extractAsync", "debug", "require", "input", "output", "process", "platform", "spawnAsync", "stdio", "error", "Log", "warn", "message", "tarExtract", "file", "cwd"], "mappings": ";;;;+BAQsBA;;;eAAAA;;;;gEARC;;;;;;;yBACe;;;;;;6DAEjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,eAAeF,aAAaG,KAAa,EAAEC,MAAc;IAC9D,IAAI;QACF,IAAIC,QAAQC,QAAQ,KAAK,SAAS;YAChCL,MAAM,CAAC,WAAW,EAAEE,MAAM,IAAI,EAAEC,QAAQ;YACxC,MAAMG,IAAAA,qBAAU,EAAC,OAAO;gBAAC;gBAAOJ;gBAAO;gBAAMC;aAAO,EAAE;gBACpDI,OAAO;YACT;YACA;QACF;IACF,EAAE,OAAOC,OAAY;QACnBC,KAAIC,IAAI,CACN,CAAC,yEAAyE,EAAEF,MAAMG,OAAO,EAAE;IAE/F;IACAX,MAAM,CAAC,WAAW,EAAEE,MAAM,IAAI,EAAEC,OAAO,oBAAoB,CAAC;IAC5D,2EAA2E;IAC3E,yCAAyC;IACzC,MAAMS,IAAAA,cAAU,EAAC;QAAEC,MAAMX;QAAOY,KAAKX;IAAO;AAC9C"}