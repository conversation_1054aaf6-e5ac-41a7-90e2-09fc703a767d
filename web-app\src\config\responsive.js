// ProChat Cross-Platform Responsive Design System
// Consistent UI/UX across Web + Mobile App

import { createTheme } from '@mui/material/styles';

// Breakpoints for different devices
export const BREAKPOINTS = {
  mobile: 0,
  tablet: 768,
  desktop: 1024,
  large: 1440,
  xlarge: 1920,
};

// Device detection utilities
export const DEVICE_TYPES = {
  MOBILE: 'mobile',
  TABLET: 'tablet', 
  DESKTOP: 'desktop',
  LARGE: 'large',
};

export const detectDeviceType = () => {
  const width = window.innerWidth;
  
  if (width < BREAKPOINTS.tablet) return DEVICE_TYPES.MOBILE;
  if (width < BREAKPOINTS.desktop) return DEVICE_TYPES.TABLET;
  if (width < BREAKPOINTS.large) return DEVICE_TYPES.DESKTOP;
  return DEVICE_TYPES.LARGE;
};

export const isMobileDevice = () => {
  return detectDeviceType() === DEVICE_TYPES.MOBILE;
};

export const isTabletDevice = () => {
  return detectDeviceType() === DEVICE_TYPES.TABLET;
};

export const isDesktopDevice = () => {
  const deviceType = detectDeviceType();
  return deviceType === DEVICE_TYPES.DESKTOP || deviceType === DEVICE_TYPES.LARGE;
};

// ProChat Brand Colors (consistent across platforms)
export const BRAND_COLORS = {
  primary: {
    main: '#007AFF',
    light: '#4DA3FF',
    dark: '#0056CC',
    contrastText: '#FFFFFF',
  },
  secondary: {
    main: '#FF6B35',
    light: '#FF8A65',
    dark: '#E64A19',
    contrastText: '#FFFFFF',
  },
  success: {
    main: '#4CAF50',
    light: '#81C784',
    dark: '#388E3C',
  },
  warning: {
    main: '#FF9800',
    light: '#FFB74D',
    dark: '#F57C00',
  },
  error: {
    main: '#F44336',
    light: '#EF5350',
    dark: '#D32F2F',
  },
  info: {
    main: '#2196F3',
    light: '#64B5F6',
    dark: '#1976D2',
  },
  background: {
    default: '#F8F9FA',
    paper: '#FFFFFF',
    dark: '#121212',
  },
  text: {
    primary: '#212121',
    secondary: '#757575',
    disabled: '#BDBDBD',
  },
};

// Typography system (consistent fonts)
export const TYPOGRAPHY = {
  fontFamily: {
    primary: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    secondary: '"Poppins", "Inter", "Roboto", sans-serif',
    mono: '"Fira Code", "Monaco", "Consolas", monospace',
  },
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
    '5xl': '3rem',    // 48px
  },
  fontWeight: {
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
  },
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
};

// Spacing system (8px grid)
export const SPACING = {
  xs: '0.25rem',  // 4px
  sm: '0.5rem',   // 8px
  md: '1rem',     // 16px
  lg: '1.5rem',   // 24px
  xl: '2rem',     // 32px
  '2xl': '3rem',  // 48px
  '3xl': '4rem',  // 64px
  '4xl': '6rem',  // 96px
  '5xl': '8rem',  // 128px
};

// Border radius system
export const BORDER_RADIUS = {
  none: '0',
  sm: '0.25rem',   // 4px
  md: '0.5rem',    // 8px
  lg: '0.75rem',   // 12px
  xl: '1rem',      // 16px
  '2xl': '1.5rem', // 24px
  full: '9999px',
};

// Shadow system
export const SHADOWS = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
};

// Component variants for different platforms
export const COMPONENT_VARIANTS = {
  mobile: {
    appBar: {
      height: 56,
      padding: '0 16px',
    },
    bottomNav: {
      height: 56,
      padding: '8px 0',
    },
    card: {
      borderRadius: BORDER_RADIUS.md,
      padding: SPACING.md,
      margin: `${SPACING.sm} 0`,
    },
    button: {
      height: 44,
      borderRadius: BORDER_RADIUS.lg,
      fontSize: TYPOGRAPHY.fontSize.base,
    },
    input: {
      height: 48,
      borderRadius: BORDER_RADIUS.lg,
      fontSize: TYPOGRAPHY.fontSize.base,
    },
  },
  tablet: {
    appBar: {
      height: 64,
      padding: '0 24px',
    },
    card: {
      borderRadius: BORDER_RADIUS.lg,
      padding: SPACING.lg,
      margin: `${SPACING.md} 0`,
    },
    button: {
      height: 48,
      borderRadius: BORDER_RADIUS.xl,
      fontSize: TYPOGRAPHY.fontSize.lg,
    },
    input: {
      height: 52,
      borderRadius: BORDER_RADIUS.xl,
      fontSize: TYPOGRAPHY.fontSize.lg,
    },
  },
  desktop: {
    appBar: {
      height: 64,
      padding: '0 32px',
    },
    sidebar: {
      width: 280,
      padding: SPACING.lg,
    },
    card: {
      borderRadius: BORDER_RADIUS.xl,
      padding: SPACING.xl,
      margin: `${SPACING.lg} 0`,
    },
    button: {
      height: 48,
      borderRadius: BORDER_RADIUS.xl,
      fontSize: TYPOGRAPHY.fontSize.lg,
    },
    input: {
      height: 52,
      borderRadius: BORDER_RADIUS.xl,
      fontSize: TYPOGRAPHY.fontSize.lg,
    },
  },
};

// Create responsive theme
export const createResponsiveTheme = (deviceType = 'mobile', darkMode = false) => {
  const variants = COMPONENT_VARIANTS[deviceType] || COMPONENT_VARIANTS.mobile;
  
  return createTheme({
    palette: {
      mode: darkMode ? 'dark' : 'light',
      primary: BRAND_COLORS.primary,
      secondary: BRAND_COLORS.secondary,
      success: BRAND_COLORS.success,
      warning: BRAND_COLORS.warning,
      error: BRAND_COLORS.error,
      info: BRAND_COLORS.info,
      background: darkMode ? {
        default: '#121212',
        paper: '#1E1E1E',
      } : BRAND_COLORS.background,
      text: darkMode ? {
        primary: '#FFFFFF',
        secondary: '#B3B3B3',
        disabled: '#666666',
      } : BRAND_COLORS.text,
    },
    typography: {
      fontFamily: TYPOGRAPHY.fontFamily.primary,
      h1: {
        fontSize: deviceType === 'mobile' ? TYPOGRAPHY.fontSize['3xl'] : TYPOGRAPHY.fontSize['4xl'],
        fontWeight: TYPOGRAPHY.fontWeight.bold,
        lineHeight: TYPOGRAPHY.lineHeight.tight,
      },
      h2: {
        fontSize: deviceType === 'mobile' ? TYPOGRAPHY.fontSize['2xl'] : TYPOGRAPHY.fontSize['3xl'],
        fontWeight: TYPOGRAPHY.fontWeight.bold,
        lineHeight: TYPOGRAPHY.lineHeight.tight,
      },
      h3: {
        fontSize: deviceType === 'mobile' ? TYPOGRAPHY.fontSize.xl : TYPOGRAPHY.fontSize['2xl'],
        fontWeight: TYPOGRAPHY.fontWeight.semibold,
        lineHeight: TYPOGRAPHY.lineHeight.tight,
      },
      h4: {
        fontSize: deviceType === 'mobile' ? TYPOGRAPHY.fontSize.lg : TYPOGRAPHY.fontSize.xl,
        fontWeight: TYPOGRAPHY.fontWeight.semibold,
        lineHeight: TYPOGRAPHY.lineHeight.normal,
      },
      h5: {
        fontSize: TYPOGRAPHY.fontSize.lg,
        fontWeight: TYPOGRAPHY.fontWeight.medium,
        lineHeight: TYPOGRAPHY.lineHeight.normal,
      },
      h6: {
        fontSize: TYPOGRAPHY.fontSize.base,
        fontWeight: TYPOGRAPHY.fontWeight.medium,
        lineHeight: TYPOGRAPHY.lineHeight.normal,
      },
      body1: {
        fontSize: TYPOGRAPHY.fontSize.base,
        fontWeight: TYPOGRAPHY.fontWeight.normal,
        lineHeight: TYPOGRAPHY.lineHeight.normal,
      },
      body2: {
        fontSize: TYPOGRAPHY.fontSize.sm,
        fontWeight: TYPOGRAPHY.fontWeight.normal,
        lineHeight: TYPOGRAPHY.lineHeight.normal,
      },
      caption: {
        fontSize: TYPOGRAPHY.fontSize.xs,
        fontWeight: TYPOGRAPHY.fontWeight.normal,
        lineHeight: TYPOGRAPHY.lineHeight.normal,
      },
    },
    breakpoints: {
      values: BREAKPOINTS,
    },
    spacing: 8, // 8px base unit
    shape: {
      borderRadius: parseInt(variants.card.borderRadius),
    },
    shadows: [
      'none',
      SHADOWS.sm,
      SHADOWS.md,
      SHADOWS.lg,
      SHADOWS.xl,
      SHADOWS['2xl'],
      // Add more shadows as needed
      ...Array(19).fill(SHADOWS['2xl']),
    ],
    components: {
      MuiAppBar: {
        styleOverrides: {
          root: {
            height: variants.appBar.height,
            padding: variants.appBar.padding,
            boxShadow: SHADOWS.sm,
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: variants.card.borderRadius,
            padding: variants.card.padding,
            margin: variants.card.margin,
            boxShadow: SHADOWS.md,
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            height: variants.button.height,
            borderRadius: variants.button.borderRadius,
            fontSize: variants.button.fontSize,
            fontWeight: TYPOGRAPHY.fontWeight.semibold,
            textTransform: 'none',
            boxShadow: 'none',
            '&:hover': {
              boxShadow: SHADOWS.sm,
            },
          },
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              height: variants.input.height,
              borderRadius: variants.input.borderRadius,
              fontSize: variants.input.fontSize,
            },
          },
        },
      },
      MuiBottomNavigation: {
        styleOverrides: {
          root: {
            height: variants.bottomNav?.height || 56,
            padding: variants.bottomNav?.padding || '8px 0',
          },
        },
      },
    },
  });
};

// Utility functions for responsive design
export const getResponsiveValue = (values, deviceType) => {
  if (typeof values === 'object' && !Array.isArray(values)) {
    return values[deviceType] || values.mobile || values.default;
  }
  return values;
};

export const getResponsiveSpacing = (spacing, deviceType) => {
  const multiplier = deviceType === 'mobile' ? 1 : deviceType === 'tablet' ? 1.25 : 1.5;
  return `${parseFloat(spacing) * multiplier}rem`;
};

export const getResponsiveFontSize = (size, deviceType) => {
  const multiplier = deviceType === 'mobile' ? 1 : deviceType === 'tablet' ? 1.1 : 1.2;
  return `${parseFloat(size) * multiplier}rem`;
};

// Media query helpers
export const mediaQueries = {
  mobile: `@media (max-width: ${BREAKPOINTS.tablet - 1}px)`,
  tablet: `@media (min-width: ${BREAKPOINTS.tablet}px) and (max-width: ${BREAKPOINTS.desktop - 1}px)`,
  desktop: `@media (min-width: ${BREAKPOINTS.desktop}px)`,
  large: `@media (min-width: ${BREAKPOINTS.large}px)`,
  xlarge: `@media (min-width: ${BREAKPOINTS.xlarge}px)`,
};

export default {
  BREAKPOINTS,
  DEVICE_TYPES,
  BRAND_COLORS,
  TYPOGRAPHY,
  SPACING,
  BORDER_RADIUS,
  SHADOWS,
  COMPONENT_VARIANTS,
  detectDeviceType,
  isMobileDevice,
  isTabletDevice,
  isDesktopDevice,
  createResponsiveTheme,
  getResponsiveValue,
  getResponsiveSpacing,
  getResponsiveFontSize,
  mediaQueries,
};
