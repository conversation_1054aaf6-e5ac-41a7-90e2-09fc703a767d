// ProChat Advanced Threat Intelligence System
// Real-time threat detection and response

import { SecurityLogger } from '../config/security';
import { emergencyModeManager } from '../config/bankingSecurity';
import fraudDetectionService from './fraudDetection';

class ThreatIntelligenceService {
  constructor() {
    this.threatDatabase = new Map();
    this.activeThreatSources = new Set();
    this.threatPatterns = new Map();
    this.realTimeThreats = new Map();
    this.threatHistory = [];
    this.isMonitoring = false;
    
    this.initializeThreatDatabase();
    this.startThreatMonitoring();
  }

  initializeThreatDatabase() {
    // Known threat patterns and signatures
    const threatPatterns = {
      'sql_injection': {
        name: 'SQL Injection Attack',
        severity: 'HIGH',
        patterns: [
          /(\%27)|(\')|(\-\-)|(\%23)|(#)/i,
          /((\%3D)|(=))[^\n]*((\%27)|(\')|(\-\-)|(\%3B)|(;))/i,
          /\w*((\%27)|(\'))((\%6F)|o|(\%4F))((\%72)|r|(\%52))/i,
        ],
        description: 'Attempts to inject SQL code into input fields',
        response: 'block_immediately',
      },
      'xss_attack': {
        name: 'Cross-Site Scripting (XSS)',
        severity: 'HIGH',
        patterns: [
          /<script[^>]*>.*?<\/script>/gi,
          /javascript:/gi,
          /on\w+\s*=/gi,
          /<iframe[^>]*>.*?<\/iframe>/gi,
        ],
        description: 'Attempts to inject malicious scripts',
        response: 'sanitize_and_block',
      },
      'brute_force': {
        name: 'Brute Force Attack',
        severity: 'MEDIUM',
        patterns: [
          { type: 'login_attempts', threshold: 5, timeWindow: 300000 }, // 5 attempts in 5 minutes
          { type: 'password_reset', threshold: 3, timeWindow: 600000 }, // 3 resets in 10 minutes
        ],
        description: 'Repeated failed authentication attempts',
        response: 'rate_limit_and_block',
      },
      'credential_stuffing': {
        name: 'Credential Stuffing',
        severity: 'HIGH',
        patterns: [
          { type: 'multiple_accounts', threshold: 10, timeWindow: 3600000 }, // 10 accounts in 1 hour
          { type: 'rapid_logins', threshold: 20, timeWindow: 1800000 }, // 20 logins in 30 minutes
        ],
        description: 'Using stolen credentials across multiple accounts',
        response: 'block_and_investigate',
      },
      'ddos_attack': {
        name: 'Distributed Denial of Service',
        severity: 'CRITICAL',
        patterns: [
          { type: 'request_volume', threshold: 1000, timeWindow: 60000 }, // 1000 requests per minute
          { type: 'concurrent_connections', threshold: 100 },
        ],
        description: 'Overwhelming the system with requests',
        response: 'emergency_mode',
      },
      'account_takeover': {
        name: 'Account Takeover Attempt',
        severity: 'CRITICAL',
        patterns: [
          { type: 'location_change', threshold: 1000 }, // 1000km distance
          { type: 'device_change', suspicious: true },
          { type: 'behavior_anomaly', deviation: 0.8 },
        ],
        description: 'Unauthorized access to user accounts',
        response: 'freeze_account',
      },
      'api_abuse': {
        name: 'API Abuse',
        severity: 'MEDIUM',
        patterns: [
          { type: 'api_calls', threshold: 500, timeWindow: 300000 }, // 500 calls in 5 minutes
          { type: 'data_scraping', threshold: 100, timeWindow: 60000 }, // 100 data requests per minute
        ],
        description: 'Excessive or malicious API usage',
        response: 'throttle_api',
      },
      'social_engineering': {
        name: 'Social Engineering Attack',
        severity: 'HIGH',
        patterns: [
          { type: 'phishing_keywords', keywords: ['urgent', 'verify', 'suspended', 'click here'] },
          { type: 'impersonation', admin_claims: true },
        ],
        description: 'Attempts to manipulate users into revealing information',
        response: 'warn_user',
      },
    };

    threatPatterns.forEach((pattern, patternId) => {
      this.threatPatterns.set(patternId, pattern);
    });

    // Initialize threat sources (external feeds)
    this.activeThreatSources.add('internal_monitoring');
    this.activeThreatSources.add('user_reports');
    this.activeThreatSources.add('automated_detection');
  }

  startThreatMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    
    // Real-time threat monitoring
    setInterval(() => {
      this.scanForThreats();
      this.analyzeTrafficPatterns();
      this.updateThreatIntelligence();
    }, 10000); // Every 10 seconds

    // Periodic threat assessment
    setInterval(() => {
      this.performThreatAssessment();
      this.cleanupOldThreats();
    }, 300000); // Every 5 minutes

    console.log('Threat Intelligence: Monitoring started');
  }

  // Analyze incoming requests for threats
  analyzeRequest(requestData) {
    const { url, method, headers, body, ip, userAgent, userId } = requestData;
    const threats = [];

    // Check for SQL injection
    const sqlThreats = this.checkSQLInjection(url, body);
    threats.push(...sqlThreats);

    // Check for XSS attacks
    const xssThreats = this.checkXSSAttack(url, body);
    threats.push(...xssThreats);

    // Check for suspicious patterns
    const patternThreats = this.checkSuspiciousPatterns(requestData);
    threats.push(...patternThreats);

    // Process detected threats
    if (threats.length > 0) {
      this.processThreatDetection(threats, requestData);
    }

    return {
      safe: threats.length === 0,
      threats,
      riskScore: this.calculateRequestRiskScore(threats),
    };
  }

  checkSQLInjection(url, body) {
    const threats = [];
    const sqlPattern = this.threatPatterns.get('sql_injection');
    const testString = `${url} ${JSON.stringify(body)}`;

    sqlPattern.patterns.forEach((pattern, index) => {
      if (pattern.test(testString)) {
        threats.push({
          type: 'sql_injection',
          severity: sqlPattern.severity,
          pattern: `SQL_${index}`,
          description: sqlPattern.description,
          evidence: testString.match(pattern)?.[0],
        });
      }
    });

    return threats;
  }

  checkXSSAttack(url, body) {
    const threats = [];
    const xssPattern = this.threatPatterns.get('xss_attack');
    const testString = `${url} ${JSON.stringify(body)}`;

    xssPattern.patterns.forEach((pattern, index) => {
      if (pattern.test(testString)) {
        threats.push({
          type: 'xss_attack',
          severity: xssPattern.severity,
          pattern: `XSS_${index}`,
          description: xssPattern.description,
          evidence: testString.match(pattern)?.[0],
        });
      }
    });

    return threats;
  }

  checkSuspiciousPatterns(requestData) {
    const threats = [];
    const { ip, userAgent, userId, timestamp } = requestData;

    // Check for bot-like behavior
    if (this.isBotLikeUserAgent(userAgent)) {
      threats.push({
        type: 'bot_detection',
        severity: 'MEDIUM',
        description: 'Suspicious user agent detected',
        evidence: userAgent,
      });
    }

    // Check for rapid requests from same IP
    const recentRequests = this.getRecentRequestsByIP(ip, 60000); // Last minute
    if (recentRequests.length > 100) {
      threats.push({
        type: 'ddos_attack',
        severity: 'HIGH',
        description: 'High request volume from single IP',
        evidence: `${recentRequests.length} requests in 1 minute`,
      });
    }

    // Check for geographic anomalies
    const geoThreat = this.checkGeographicAnomaly(ip, userId);
    if (geoThreat) {
      threats.push(geoThreat);
    }

    return threats;
  }

  isBotLikeUserAgent(userAgent) {
    const botPatterns = [
      /bot/i, /crawler/i, /spider/i, /scraper/i,
      /curl/i, /wget/i, /python/i, /java/i,
      /automated/i, /script/i
    ];

    return botPatterns.some(pattern => pattern.test(userAgent));
  }

  getRecentRequestsByIP(ip, timeWindow) {
    const cutoff = Date.now() - timeWindow;
    return this.threatHistory.filter(entry => 
      entry.ip === ip && entry.timestamp > cutoff
    );
  }

  checkGeographicAnomaly(ip, userId) {
    // Simplified geo check (in production, use proper geolocation service)
    const userHistory = this.getUserLocationHistory(userId);
    if (userHistory.length === 0) return null;

    const currentLocation = this.getLocationFromIP(ip);
    if (!currentLocation) return null;

    const lastKnownLocation = userHistory[userHistory.length - 1];
    const distance = this.calculateDistance(currentLocation, lastKnownLocation);

    if (distance > 1000) { // More than 1000km
      return {
        type: 'geographic_anomaly',
        severity: 'MEDIUM',
        description: 'Login from unusual geographic location',
        evidence: `${distance}km from last known location`,
      };
    }

    return null;
  }

  processThreatDetection(threats, requestData) {
    const threatId = this.generateThreatId();
    const highestSeverity = this.getHighestSeverity(threats);

    // Store threat information
    this.realTimeThreats.set(threatId, {
      id: threatId,
      threats,
      requestData,
      severity: highestSeverity,
      timestamp: Date.now(),
      status: 'active',
      response: null,
    });

    // Log security event
    SecurityLogger.logSecurityEvent('THREAT_DETECTED', {
      threatId,
      threats: threats.map(t => ({ type: t.type, severity: t.severity })),
      ip: requestData.ip,
      userId: requestData.userId,
      severity: highestSeverity,
    });

    // Execute threat response
    this.executeThreatResponse(threatId, threats, requestData);

    // Update threat history
    this.threatHistory.push({
      threatId,
      ip: requestData.ip,
      userId: requestData.userId,
      threats,
      timestamp: Date.now(),
    });
  }

  executeThreatResponse(threatId, threats, requestData) {
    const responses = new Set();
    
    threats.forEach(threat => {
      const pattern = this.threatPatterns.get(threat.type);
      if (pattern?.response) {
        responses.add(pattern.response);
      }
    });

    // Execute responses in order of severity
    const responseOrder = [
      'emergency_mode',
      'block_immediately',
      'freeze_account',
      'block_and_investigate',
      'rate_limit_and_block',
      'throttle_api',
      'sanitize_and_block',
      'warn_user',
    ];

    for (const response of responseOrder) {
      if (responses.has(response)) {
        this.executeResponse(response, threatId, requestData);
        break; // Execute only the highest priority response
      }
    }
  }

  executeResponse(responseType, threatId, requestData) {
    const { ip, userId } = requestData;

    switch (responseType) {
      case 'emergency_mode':
        emergencyModeManager.activateEmergencyMode(
          `Critical threat detected: ${threatId}`,
          ['view_only']
        );
        this.notifySecurityTeam('CRITICAL', threatId, requestData);
        break;

      case 'block_immediately':
        this.blockIP(ip, 'Malicious activity detected');
        this.notifySecurityTeam('HIGH', threatId, requestData);
        break;

      case 'freeze_account':
        if (userId) {
          this.freezeUserAccount(userId, `Security threat: ${threatId}`);
        }
        this.notifySecurityTeam('HIGH', threatId, requestData);
        break;

      case 'block_and_investigate':
        this.blockIP(ip, 'Suspicious activity under investigation');
        this.flagForInvestigation(threatId, requestData);
        break;

      case 'rate_limit_and_block':
        this.applyRateLimit(ip, 3600000); // 1 hour
        break;

      case 'throttle_api':
        this.throttleAPI(ip, 300000); // 5 minutes
        break;

      case 'sanitize_and_block':
        this.sanitizeInput(requestData);
        this.blockRequest(threatId);
        break;

      case 'warn_user':
        if (userId) {
          this.warnUser(userId, threatId);
        }
        break;
    }

    // Update threat record
    const threat = this.realTimeThreats.get(threatId);
    if (threat) {
      threat.response = responseType;
      threat.responseTime = Date.now();
    }
  }

  // Response implementation methods
  blockIP(ip, reason) {
    console.log(`🚫 Blocking IP ${ip}: ${reason}`);
    // In production, update firewall rules
  }

  freezeUserAccount(userId, reason) {
    console.log(`🧊 Freezing account ${userId}: ${reason}`);
    fraudDetectionService.blockUser(userId, reason);
  }

  applyRateLimit(ip, duration) {
    console.log(`⏱️ Rate limiting IP ${ip} for ${duration}ms`);
    // In production, update rate limiting rules
  }

  throttleAPI(ip, duration) {
    console.log(`🐌 Throttling API for IP ${ip} for ${duration}ms`);
    // In production, apply API throttling
  }

  sanitizeInput(requestData) {
    console.log(`🧹 Sanitizing input from ${requestData.ip}`);
    // In production, sanitize and log
  }

  blockRequest(threatId) {
    console.log(`🛑 Blocking request ${threatId}`);
    // In production, block the specific request
  }

  warnUser(userId, threatId) {
    console.log(`⚠️ Warning user ${userId} about threat ${threatId}`);
    // In production, send user notification
  }

  flagForInvestigation(threatId, requestData) {
    console.log(`🔍 Flagging ${threatId} for investigation`);
    // In production, add to investigation queue
  }

  notifySecurityTeam(severity, threatId, requestData) {
    console.log(`📢 Notifying security team: ${severity} threat ${threatId}`);
    // In production, send alerts to security team
  }

  // Analysis and monitoring methods
  scanForThreats() {
    // Scan active sessions for threats
    this.realTimeThreats.forEach((threat, threatId) => {
      if (threat.status === 'active') {
        this.reassessThreat(threatId);
      }
    });
  }

  analyzeTrafficPatterns() {
    // Analyze traffic for anomalies
    const recentTraffic = this.getRecentTraffic(300000); // Last 5 minutes
    
    if (recentTraffic.length > 5000) { // High traffic volume
      this.checkForDDoS(recentTraffic);
    }
  }

  updateThreatIntelligence() {
    // Update threat patterns based on recent attacks
    const recentThreats = Array.from(this.realTimeThreats.values())
      .filter(t => Date.now() - t.timestamp < 3600000); // Last hour

    if (recentThreats.length > 10) {
      console.log(`⚠️ High threat activity: ${recentThreats.length} threats in last hour`);
    }
  }

  performThreatAssessment() {
    const stats = this.getThreatStatistics();
    
    SecurityLogger.logSecurityEvent('THREAT_ASSESSMENT', {
      activeThreats: stats.activeThreats,
      totalThreats: stats.totalThreats,
      criticalThreats: stats.criticalThreats,
      blockedIPs: stats.blockedIPs,
    });
  }

  // Utility methods
  generateThreatId() {
    return `threat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getHighestSeverity(threats) {
    const severityOrder = ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'];
    let highest = 'LOW';
    
    threats.forEach(threat => {
      if (severityOrder.indexOf(threat.severity) > severityOrder.indexOf(highest)) {
        highest = threat.severity;
      }
    });
    
    return highest;
  }

  calculateRequestRiskScore(threats) {
    const severityScores = { LOW: 0.25, MEDIUM: 0.5, HIGH: 0.75, CRITICAL: 1.0 };
    
    return threats.reduce((score, threat) => {
      return Math.max(score, severityScores[threat.severity] || 0);
    }, 0);
  }

  getUserLocationHistory(userId) {
    // In production, get from database
    return [];
  }

  getLocationFromIP(ip) {
    // In production, use geolocation service
    return null;
  }

  calculateDistance(loc1, loc2) {
    // Simplified distance calculation
    return 0;
  }

  getRecentTraffic(timeWindow) {
    const cutoff = Date.now() - timeWindow;
    return this.threatHistory.filter(entry => entry.timestamp > cutoff);
  }

  checkForDDoS(trafficData) {
    // Check if traffic pattern indicates DDoS
    const ipCounts = new Map();
    
    trafficData.forEach(entry => {
      const count = ipCounts.get(entry.ip) || 0;
      ipCounts.set(entry.ip, count + 1);
    });

    // If any IP has more than 1000 requests, it's likely DDoS
    ipCounts.forEach((count, ip) => {
      if (count > 1000) {
        this.processThreatDetection([{
          type: 'ddos_attack',
          severity: 'CRITICAL',
          description: 'DDoS attack detected',
          evidence: `${count} requests from ${ip}`,
        }], { ip, timestamp: Date.now() });
      }
    });
  }

  cleanupOldThreats() {
    const dayAgo = Date.now() - 24 * 60 * 60 * 1000;
    
    // Clean up old threats
    this.realTimeThreats.forEach((threat, threatId) => {
      if (threat.timestamp < dayAgo) {
        this.realTimeThreats.delete(threatId);
      }
    });

    // Clean up old history
    this.threatHistory = this.threatHistory.filter(entry => entry.timestamp > dayAgo);
  }

  reassessThreat(threatId) {
    const threat = this.realTimeThreats.get(threatId);
    if (!threat) return;

    // Check if threat is still active
    const timeSinceDetection = Date.now() - threat.timestamp;
    
    if (timeSinceDetection > 3600000) { // 1 hour
      threat.status = 'resolved';
    }
  }

  // Public API methods
  getThreatStatistics() {
    const activeThreats = Array.from(this.realTimeThreats.values())
      .filter(t => t.status === 'active');
    
    const criticalThreats = activeThreats.filter(t => t.severity === 'CRITICAL');
    
    return {
      totalThreats: this.realTimeThreats.size,
      activeThreats: activeThreats.length,
      criticalThreats: criticalThreats.length,
      blockedIPs: new Set(this.threatHistory.map(t => t.ip)).size,
      threatTypes: this.getThreatTypeDistribution(),
    };
  }

  getThreatTypeDistribution() {
    const distribution = new Map();
    
    this.threatHistory.forEach(entry => {
      entry.threats.forEach(threat => {
        const count = distribution.get(threat.type) || 0;
        distribution.set(threat.type, count + 1);
      });
    });
    
    return Object.fromEntries(distribution);
  }

  getActiveThreats() {
    return Array.from(this.realTimeThreats.values())
      .filter(t => t.status === 'active')
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  getThreatHistory(limit = 100) {
    return this.threatHistory
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  isIPBlocked(ip) {
    // Check if IP is currently blocked
    return this.realTimeThreats.has(`blocked_${ip}`);
  }

  unblockIP(ip) {
    this.realTimeThreats.delete(`blocked_${ip}`);
    console.log(`✅ Unblocked IP ${ip}`);
  }
}

// Create singleton instance
const threatIntelligenceService = new ThreatIntelligenceService();

export default threatIntelligenceService;
