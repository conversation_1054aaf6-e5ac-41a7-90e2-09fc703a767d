// ProChat Global Super App Dashboard
// World-class features for global domination

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Tabs,
  Tab,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Badge,
  LinearProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Public,
  Psychology,
  AccountBalance,
  Mic,
  CloudOff,
  School,
  Store,
  EmojiEvents,
  CurrencyBitcoin,
  Translate,
  SmartToy,
  Security,
  TrendingUp,
  Language,
  ExpandMore,
  VoiceChat,
  OfflineBolt,
  AutoAwesome,
  Rocket,
  Diamond,
  Star,
  MonetizationOn,
} from '@mui/icons-material';

import { useAuth } from '../../contexts/AuthContext';
import { useDeviceDetection } from '../../hooks/useDeviceDetection';
import globalSuperAppService from '../../services/globalSuperAppService';

const GlobalSuperAppDashboard = () => {
  const { user } = useAuth();
  const { isMobile } = useDeviceDetection();
  const [activeTab, setActiveTab] = useState(0);
  const [globalData, setGlobalData] = useState({});
  const [loading, setLoading] = useState(true);
  const [voiceDialog, setVoiceDialog] = useState(false);
  const [aiDialog, setAiDialog] = useState(false);

  useEffect(() => {
    loadGlobalData();
  }, [user]);

  const loadGlobalData = async () => {
    try {
      setLoading(true);
      const [stats, features] = await Promise.all([
        globalSuperAppService.getGlobalStats(),
        {
          ai: globalSuperAppService.getAIFeatures(),
          blockchain: globalSuperAppService.getBlockchainFeatures(),
          payments: globalSuperAppService.getGlobalPaymentFeatures(),
          voice: globalSuperAppService.getVoiceFeatures(),
          offline: globalSuperAppService.getOfflineFeatures(),
          education: globalSuperAppService.getEducationFeatures(),
          marketplace: globalSuperAppService.getMarketplaceFeatures(),
          loyalty: globalSuperAppService.getLoyaltyFeatures(),
        },
      ]);

      setGlobalData({ stats, features });
    } catch (error) {
      console.error('Failed to load global data:', error);
    } finally {
      setLoading(false);
    }
  };

  const StatCard = ({ title, value, subtitle, icon, color = 'primary', gradient = false }) => (
    <Card 
      sx={{ 
        height: '100%',
        background: gradient ? `linear-gradient(135deg, ${color}.main, ${color}.dark)` : 'inherit',
        color: gradient ? 'white' : 'inherit',
      }}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {icon}
          <Typography variant="h6" sx={{ ml: 1, flexGrow: 1 }}>
            {title}
          </Typography>
        </Box>
        <Typography 
          variant="h3" 
          sx={{ 
            fontWeight: 'bold', 
            mb: 1,
            color: gradient ? 'white' : `${color}.main`,
          }}
        >
          {value}
        </Typography>
        {subtitle && (
          <Typography 
            variant="body2" 
            sx={{ 
              color: gradient ? 'rgba(255,255,255,0.8)' : 'text.secondary',
            }}
          >
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );

  const FeatureCard = ({ title, description, icon, color, features, enabled = true }) => (
    <Card sx={{ height: '100%', opacity: enabled ? 1 : 0.6 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ color: `${color}.main`, mr: 2 }}>
            {icon}
          </Box>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6">{title}</Typography>
            <Typography variant="body2" color="text.secondary">
              {description}
            </Typography>
          </Box>
          <Chip 
            label={enabled ? 'Active' : 'Coming Soon'}
            color={enabled ? 'success' : 'default'}
            size="small"
          />
        </Box>
        
        <List dense>
          {features.slice(0, 3).map((feature, index) => (
            <ListItem key={index} sx={{ px: 0 }}>
              <ListItemIcon sx={{ minWidth: 32 }}>
                <Star sx={{ fontSize: 16, color: `${color}.main` }} />
              </ListItemIcon>
              <ListItemText 
                primary={feature}
                primaryTypographyProps={{ variant: 'body2' }}
              />
            </ListItem>
          ))}
          {features.length > 3 && (
            <ListItem sx={{ px: 0 }}>
              <ListItemText 
                primary={`+${features.length - 3} more features`}
                primaryTypographyProps={{ 
                  variant: 'caption', 
                  color: 'text.secondary',
                  fontStyle: 'italic',
                }}
              />
            </ListItem>
          )}
        </List>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" sx={{ mb: 2 }}>Global Super App</Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: isMobile ? 2 : 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Public sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          🌍 Global Super App
        </Typography>
        <Chip 
          label="World-Class Features"
          color="primary"
          variant="outlined"
          sx={{ mr: 2 }}
        />
        <Button
          variant="contained"
          startIcon={<Rocket />}
          onClick={() => window.open('https://prochat.app/global', '_blank')}
        >
          Go Global
        </Button>
      </Box>

      {/* Vision Statement */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="h6">
          🚀 "The First African Super App to Conquer the World"
        </Typography>
        <Typography variant="body2">
          Combining Social Media + E-Wallet + Business Platform + AI + Blockchain for global domination! 🌍💰🤖
        </Typography>
      </Alert>

      {/* Global Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Global Currencies"
            value={globalData.stats?.supportedCurrencies || 20}
            subtitle="Fiat + Crypto supported"
            icon={<CurrencyBitcoin color="warning" />}
            color="warning"
            gradient={true}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Languages"
            value={globalData.stats?.supportedLanguages || 12}
            subtitle="Multi-language support"
            icon={<Translate color="info" />}
            color="info"
            gradient={true}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Cross-Border Payments"
            value={globalData.stats?.crossBorderTransactions || 0}
            subtitle="International transactions"
            icon={<Public color="success" />}
            color="success"
            gradient={true}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="AI Recommendations"
            value="∞"
            subtitle="Infinite AI possibilities"
            icon={<Psychology color="error" />}
            color="error"
            gradient={true}
          />
        </Grid>
      </Grid>

      {/* Feature Categories */}
      <Typography variant="h5" sx={{ mb: 3 }}>
        🌟 World-Class Features
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* AI Integration */}
        <Grid item xs={12} md={6} lg={4}>
          <FeatureCard
            title="🤖 AI Integration"
            description="Advanced AI for personalized experience"
            icon={<Psychology sx={{ fontSize: 40 }} />}
            color="primary"
            features={[
              'Smart Chat Assistant',
              'AI Recommendations',
              'Financial AI Advisor',
              'Content Creation AI',
              'Fraud Detection AI',
              'Sentiment Analysis',
            ]}
          />
        </Grid>

        {/* Blockchain Support */}
        <Grid item xs={12} md={6} lg={4}>
          <FeatureCard
            title="⛓️ Blockchain & Web3"
            description="Decentralized features for the future"
            icon={<CurrencyBitcoin sx={{ fontSize: 40 }} />}
            color="warning"
            features={[
              'Multi-Crypto Wallet',
              'DeFi Integration',
              'NFT Marketplace',
              'Smart Contracts',
              'DAO Participation',
              'Cross-Chain Swaps',
            ]}
          />
        </Grid>

        {/* Global Payments */}
        <Grid item xs={12} md={6} lg={4}>
          <FeatureCard
            title="🌍 Global Payments"
            description="Send money anywhere in the world"
            icon={<AccountBalance sx={{ fontSize: 40 }} />}
            color="success"
            features={[
              '20+ Fiat Currencies',
              '10+ Cryptocurrencies',
              'Cross-Border Transfers',
              'Virtual Banking',
              'Multi-Currency Accounts',
              'SWIFT Integration',
            ]}
          />
        </Grid>

        {/* Voice Commands */}
        <Grid item xs={12} md={6} lg={4}>
          <FeatureCard
            title="🎤 Voice Interface"
            description="Control everything with your voice"
            icon={<Mic sx={{ fontSize: 40 }} />}
            color="info"
            features={[
              'Natural Voice Commands',
              '12 Language Support',
              'Voice Biometrics',
              'AI Voice Assistant',
              'Hands-Free Operation',
              'Voice Security',
            ]}
          />
        </Grid>

        {/* Offline Mode */}
        <Grid item xs={12} md={6} lg={4}>
          <FeatureCard
            title="📱 Offline Capabilities"
            description="Work without internet connection"
            icon={<CloudOff sx={{ fontSize: 40 }} />}
            color="secondary"
            features={[
              'Offline Messaging',
              'Offline Wallet View',
              'Cached Content',
              'Smart Synchronization',
              'Offline Security',
              'Background Sync',
            ]}
          />
        </Grid>

        {/* Education Platform */}
        <Grid item xs={12} md={6} lg={4}>
          <FeatureCard
            title="📚 Education Hub"
            description="Learn while you earn"
            icon={<School sx={{ fontSize: 40 }} />}
            color="error"
            features={[
              'Financial Literacy',
              'Digital Skills',
              'Career Development',
              'Interactive Courses',
              'Skill Certifications',
              'Mentorship Program',
            ]}
          />
        </Grid>
      </Grid>

      {/* Advanced Features Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={activeTab} 
            onChange={(e, newValue) => setActiveTab(newValue)}
            variant={isMobile ? 'scrollable' : 'fullWidth'}
            scrollButtons="auto"
          >
            <Tab label="🤖 AI Features" />
            <Tab label="⛓️ Blockchain" />
            <Tab label="🌍 Global Pay" />
            <Tab label="🎤 Voice AI" />
            <Tab label="📱 Offline" />
            <Tab label="🎁 Loyalty" />
          </Tabs>
        </Box>

        <CardContent>
          {/* AI Features Tab */}
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>
                🤖 Artificial Intelligence Features
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Accordion defaultExpanded>
                    <AccordionSummary expandIcon={<ExpandMore />}>
                      <Typography variant="subtitle1">🧠 Smart Assistant</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <List dense>
                        <ListItem>
                          <ListItemIcon><SmartToy color="primary" /></ListItemIcon>
                          <ListItemText 
                            primary="Natural Conversation"
                            secondary="Chat naturally with AI assistant"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon><Translate color="info" /></ListItemIcon>
                          <ListItemText 
                            primary="Real-time Translation"
                            secondary="Translate messages instantly"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon><Psychology color="warning" /></ListItemIcon>
                          <ListItemText 
                            primary="Emotion Recognition"
                            secondary="AI understands your emotions"
                          />
                        </ListItem>
                      </List>
                    </AccordionDetails>
                  </Accordion>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Accordion defaultExpanded>
                    <AccordionSummary expandIcon={<ExpandMore />}>
                      <Typography variant="subtitle1">💡 Smart Recommendations</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <List dense>
                        <ListItem>
                          <ListItemIcon><TrendingUp color="success" /></ListItemIcon>
                          <ListItemText 
                            primary="Personalized Content"
                            secondary="AI curates content for you"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon><MonetizationOn color="warning" /></ListItemIcon>
                          <ListItemText 
                            primary="Investment Advice"
                            secondary="AI financial advisor"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon><Store color="secondary" /></ListItemIcon>
                          <ListItemText 
                            primary="Product Suggestions"
                            secondary="Smart shopping recommendations"
                          />
                        </ListItem>
                      </List>
                    </AccordionDetails>
                  </Accordion>
                </Grid>
              </Grid>

              <Box sx={{ mt: 3, textAlign: 'center' }}>
                <Button
                  variant="contained"
                  startIcon={<AutoAwesome />}
                  onClick={() => setAiDialog(true)}
                  size="large"
                >
                  Try AI Assistant
                </Button>
              </Box>
            </Box>
          )}

          {/* Blockchain Tab */}
          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>
                ⛓️ Blockchain & Web3 Integration
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <CurrencyBitcoin sx={{ fontSize: 48, color: 'warning.main', mb: 2 }} />
                      <Typography variant="h6" sx={{ mb: 1 }}>
                        Multi-Crypto Wallet
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Support for Bitcoin, Ethereum, USDT, BNB, and more
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Diamond sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                      <Typography variant="h6" sx={{ mb: 1 }}>
                        NFT Marketplace
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Create, buy, and sell NFTs within the app
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Security sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
                      <Typography variant="h6" sx={{ mb: 1 }}>
                        Smart Contracts
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Automated agreements and transactions
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Global Payments Tab */}
          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>
                🌍 Global Payment System
              </Typography>
              
              <Alert severity="success" sx={{ mb: 3 }}>
                <Typography variant="body1">
                  <strong>Send money to 195+ countries</strong> with competitive rates and instant transfers!
                </Typography>
              </Alert>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" sx={{ mb: 2 }}>
                    💱 Supported Currencies
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'TZS', 'KES', 'UGX', 'BTC', 'ETH', 'USDT', 'BNB'].map((currency) => (
                      <Chip key={currency} label={currency} variant="outlined" size="small" />
                    ))}
                    <Chip label="+8 more" variant="outlined" size="small" color="primary" />
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" sx={{ mb: 2 }}>
                    🏦 Banking Features
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemIcon><AccountBalance color="primary" /></ListItemIcon>
                      <ListItemText primary="Virtual IBAN Accounts" />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Security color="success" /></ListItemIcon>
                      <ListItemText primary="SWIFT Wire Transfers" />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><MonetizationOn color="warning" /></ListItemIcon>
                      <ListItemText primary="Multi-Currency Savings" />
                    </ListItem>
                  </List>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Voice AI Tab */}
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>
                🎤 Voice-Powered Interface
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <VoiceChat sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      Try Voice Commands
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      Say: "Tuma TZS 5,000 kwa John" or "Check my balance"
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<Mic />}
                      onClick={() => setVoiceDialog(true)}
                    >
                      Start Voice Command
                    </Button>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" sx={{ mb: 2 }}>
                    🗣️ Supported Languages
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {['Kiswahili', 'English', 'Français', '中文', 'العربية', 'Español'].map((lang) => (
                      <Chip key={lang} label={lang} variant="outlined" size="small" />
                    ))}
                    <Chip label="+6 more" variant="outlined" size="small" color="primary" />
                  </Box>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Offline Tab */}
          {activeTab === 4 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>
                📱 Offline Capabilities
              </Typography>
              
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body1">
                  <strong>Work without internet!</strong> ProChat works even when you're offline.
                </Typography>
              </Alert>

              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <OfflineBolt sx={{ fontSize: 48, color: 'warning.main', mb: 2 }} />
                      <Typography variant="h6" sx={{ mb: 1 }}>
                        Offline Messaging
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Send messages offline, sync when online
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <AccountBalance sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
                      <Typography variant="h6" sx={{ mb: 1 }}>
                        Offline Wallet
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        View balance and history offline
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <CloudOff sx={{ fontSize: 48, color: 'info.main', mb: 2 }} />
                      <Typography variant="h6" sx={{ mb: 1 }}>
                        Smart Sync
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Intelligent data synchronization
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Loyalty Tab */}
          {activeTab === 5 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>
                🎁 Advanced Loyalty & Rewards
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" sx={{ mb: 2 }}>
                    💎 Loyalty Tiers
                  </Typography>
                  <List>
                    {[
                      { tier: 'Bronze', color: '#CD7F32', benefits: 'Basic cashback' },
                      { tier: 'Silver', color: '#C0C0C0', benefits: 'Enhanced cashback + Priority support' },
                      { tier: 'Gold', color: '#FFD700', benefits: 'Premium cashback + Exclusive offers' },
                      { tier: 'Platinum', color: '#E5E4E2', benefits: 'VIP treatment + Personal advisor' },
                      { tier: 'Diamond', color: '#B9F2FF', benefits: 'Ultimate rewards + Concierge service' },
                    ].map((tier) => (
                      <ListItem key={tier.tier}>
                        <ListItemIcon>
                          <Star sx={{ color: tier.color }} />
                        </ListItemIcon>
                        <ListItemText 
                          primary={tier.tier}
                          secondary={tier.benefits}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" sx={{ mb: 2 }}>
                    🏆 Weekly Competitions
                  </Typography>
                  <List>
                    <ListItem>
                      <ListItemIcon><EmojiEvents sx={{ color: '#FFD700' }} /></ListItemIcon>
                      <ListItemText 
                        primary="Best Content Creator"
                        secondary="Win TZS 100,000 weekly"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Store sx={{ color: '#32CD32' }} /></ListItemIcon>
                      <ListItemText 
                        primary="Top Seller"
                        secondary="Win TZS 50,000 monthly"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><TrendingUp sx={{ color: '#FF6347' }} /></ListItemIcon>
                      <ListItemText 
                        primary="Most Active User"
                        secondary="Win TZS 25,000 daily"
                      />
                    </ListItem>
                  </List>
                </Grid>
              </Grid>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Voice Command Dialog */}
      <Dialog open={voiceDialog} onClose={() => setVoiceDialog(false)}>
        <DialogTitle>🎤 Voice Command</DialogTitle>
        <DialogContent>
          <Box sx={{ textAlign: 'center', py: 3 }}>
            <Mic sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
            <Typography variant="h6" sx={{ mb: 2 }}>
              Listening...
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Try saying: "Tuma TZS 5,000 kwa John" or "Check my balance"
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setVoiceDialog(false)}>Cancel</Button>
          <Button variant="contained">Stop Listening</Button>
        </DialogActions>
      </Dialog>

      {/* AI Assistant Dialog */}
      <Dialog open={aiDialog} onClose={() => setAiDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>🤖 AI Assistant</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={4}
            placeholder="Ask me anything... I can help with payments, recommendations, financial advice, and more!"
            sx={{ mb: 2 }}
          />
          <Alert severity="info">
            <Typography variant="body2">
              💡 Try asking: "What's my spending pattern?" or "Recommend good investments"
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAiDialog(false)}>Close</Button>
          <Button variant="contained" startIcon={<AutoAwesome />}>
            Ask AI
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GlobalSuperAppDashboard;
