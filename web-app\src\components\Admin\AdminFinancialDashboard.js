// ProChat Admin Financial Control Dashboard
// Complete control over all financial aspects

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Chip,
  Slider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Tooltip,
  LinearProgress,
} from '@mui/material';
import {
  MonetizationOn,
  TrendingUp,
  Settings,
  Warning,
  CheckCircle,
  Error,
  Edit,
  Save,
  Cancel,
  Emergency,
  ExpandMore,
  Refresh,
  AccountBalance,
  Assessment,
  Security,
  People,
} from '@mui/icons-material';

import { useDeviceDetection } from '../../hooks/useDeviceDetection';
import adminFinancialControlService from '../../services/adminFinancialControl';
import adminSecurityService from '../../services/adminSecurity';

const AdminFinancialDashboard = () => {
  const { isMobile } = useDeviceDetection();
  const [dashboardData, setDashboardData] = useState({});
  const [loading, setLoading] = useState(true);
  const [editDialog, setEditDialog] = useState({ open: false, type: '', data: {} });
  const [emergencyDialog, setEmergencyDialog] = useState(false);

  useEffect(() => {
    loadFinancialDashboard();
    const interval = setInterval(loadFinancialDashboard, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadFinancialDashboard = async () => {
    try {
      setLoading(true);
      const sessionId = localStorage.getItem('admin_session_id');
      const dashboard = await adminFinancialControlService.getFinancialDashboard(sessionId);
      setDashboardData(dashboard);
    } catch (error) {
      console.error('Failed to load financial dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRewardRateUpdate = async (category, actionType, newRates) => {
    try {
      const sessionId = localStorage.getItem('admin_session_id');
      await adminFinancialControlService.updateRewardRates(
        sessionId,
        category,
        actionType,
        newRates
      );
      alert('Reward rates updated successfully!');
      loadFinancialDashboard();
    } catch (error) {
      alert('Error: ' + error.message);
    }
  };

  const handleGlobalMultiplierChange = async (multiplier) => {
    try {
      const sessionId = localStorage.getItem('admin_session_id');
      await adminFinancialControlService.setGlobalRewardMultiplier(sessionId, multiplier);
      alert(`Global multiplier set to ${multiplier}x`);
      loadFinancialDashboard();
    } catch (error) {
      alert('Error: ' + error.message);
    }
  };

  const handleUserBalanceAdjustment = async (userId, amount, reason) => {
    try {
      const sessionId = localStorage.getItem('admin_session_id');
      await adminFinancialControlService.adjustUserBalance(sessionId, userId, amount, reason);
      alert(`User balance adjusted by TZS ${amount.toLocaleString()}`);
      loadFinancialDashboard();
    } catch (error) {
      alert('Error: ' + error.message);
    }
  };

  const handleEmergencyMode = async (activate, restrictions = {}) => {
    try {
      const sessionId = localStorage.getItem('admin_session_id');
      
      if (activate) {
        await adminFinancialControlService.activateFinancialEmergencyMode(
          sessionId,
          'Manual activation from dashboard',
          restrictions
        );
        alert('Financial emergency mode activated!');
      } else {
        await adminFinancialControlService.deactivateFinancialEmergencyMode(sessionId);
        alert('Financial emergency mode deactivated!');
      }
      
      setEmergencyDialog(false);
      loadFinancialDashboard();
    } catch (error) {
      alert('Error: ' + error.message);
    }
  };

  const StatCard = ({ title, value, subtitle, icon, color = 'primary', trend }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {icon}
          <Typography variant="h6" sx={{ ml: 1, flexGrow: 1 }}>
            {title}
          </Typography>
          {trend && (
            <Chip 
              label={`${trend > 0 ? '+' : ''}${trend}%`}
              color={trend > 0 ? 'success' : 'error'}
              size="small"
            />
          )}
        </Box>
        <Typography variant="h4" color={`${color}.main`} sx={{ fontWeight: 'bold' }}>
          {value}
        </Typography>
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );

  const RewardSettingsCard = ({ category, settings }) => (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2 }}>
          {category.replace('_', ' ').toUpperCase()} REWARDS
        </Typography>
        
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Action</TableCell>
                <TableCell>Coins</TableCell>
                <TableCell>Cash (TZS)</TableCell>
                <TableCell>Enabled</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Object.entries(settings).map(([actionType, reward]) => (
                <TableRow key={actionType}>
                  <TableCell>{actionType.replace('_', ' ')}</TableCell>
                  <TableCell>{reward.coins || 0}</TableCell>
                  <TableCell>{reward.cash || 0}</TableCell>
                  <TableCell>
                    <Chip 
                      label={reward.enabled ? 'Yes' : 'No'}
                      color={reward.enabled ? 'success' : 'error'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => setEditDialog({
                        open: true,
                        type: 'reward_rate',
                        data: { category, actionType, reward }
                      })}
                    >
                      <Edit />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" sx={{ mb: 2 }}>Admin Financial Dashboard</Typography>
        <LinearProgress />
      </Box>
    );
  }

  const { rewardSettings, platformBudget, emergencyStatus } = dashboardData;

  return (
    <Box sx={{ p: isMobile ? 2 : 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <MonetizationOn sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          💰 Admin Financial Control
        </Typography>
        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={loadFinancialDashboard}
          sx={{ mr: 2 }}
        >
          Refresh
        </Button>
        <Button
          variant="contained"
          color="error"
          startIcon={<Emergency />}
          onClick={() => setEmergencyDialog(true)}
        >
          Emergency Mode
        </Button>
      </Box>

      {/* Emergency Status */}
      {emergencyStatus?.active && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="h6">
            🚨 FINANCIAL EMERGENCY MODE ACTIVE
          </Typography>
          <Typography variant="body2">
            Reason: {emergencyStatus.reason}
          </Typography>
          <Typography variant="caption">
            Active since: {new Date(emergencyStatus.activatedAt).toLocaleString()}
          </Typography>
        </Alert>
      )}

      {/* Financial Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Daily Budget"
            value={`TZS ${(platformBudget?.dailyBudget || 0).toLocaleString()}`}
            subtitle="Total daily allocation"
            icon={<AccountBalance color="primary" />}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Today's Spending"
            value={`TZS ${(platformBudget?.todaySpending || 0).toLocaleString()}`}
            subtitle={`${Math.round((platformBudget?.todaySpending || 0) / (platformBudget?.dailyBudget || 1) * 100)}% used`}
            icon={<TrendingUp color="warning" />}
            color="warning"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Users"
            value={(dashboardData.dailyStats?.activeUsers || 0).toLocaleString()}
            subtitle="Users earning today"
            icon={<People color="info" />}
            color="info"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Global Multiplier"
            value={`${dashboardData.rewardSettings?.global_settings?.reward_multiplier_global || 1}x`}
            subtitle="Current reward rate"
            icon={<Settings color="success" />}
            color="success"
          />
        </Grid>
      </Grid>

      {/* Global Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            🎛️ Global Financial Controls
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Global Reward Multiplier
              </Typography>
              <Box sx={{ px: 2 }}>
                <Slider
                  value={dashboardData.rewardSettings?.global_settings?.reward_multiplier_global || 1}
                  min={0}
                  max={5}
                  step={0.1}
                  marks={[
                    { value: 0, label: '0x' },
                    { value: 1, label: '1x' },
                    { value: 2, label: '2x' },
                    { value: 5, label: '5x' },
                  ]}
                  valueLabelDisplay="on"
                  onChange={(e, value) => handleGlobalMultiplierChange(value)}
                />
              </Box>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Quick Actions
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => handleGlobalMultiplierChange(0)}
                >
                  Stop All Rewards
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => handleGlobalMultiplierChange(0.5)}
                >
                  Half Rewards
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => handleGlobalMultiplierChange(1)}
                >
                  Normal Rewards
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => handleGlobalMultiplierChange(2)}
                >
                  Double Rewards
                </Button>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Reward Settings */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Typography variant="h6">💎 Reward Rate Settings</Typography>
        </AccordionSummary>
        <AccordionDetails>
          {rewardSettings && Object.entries(rewardSettings).map(([category, settings]) => (
            <RewardSettingsCard key={category} category={category} settings={settings} />
          ))}
        </AccordionDetails>
      </Accordion>

      {/* User Balance Adjustment */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            👤 User Balance Management
          </Typography>
          
          <Button
            variant="contained"
            onClick={() => setEditDialog({
              open: true,
              type: 'user_balance',
              data: {}
            })}
          >
            Adjust User Balance
          </Button>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={editDialog.open} onClose={() => setEditDialog({ open: false, type: '', data: {} })}>
        <DialogTitle>
          {editDialog.type === 'reward_rate' ? 'Edit Reward Rate' : 'Adjust User Balance'}
        </DialogTitle>
        <DialogContent>
          {editDialog.type === 'reward_rate' ? (
            <Box sx={{ pt: 2 }}>
              <TextField
                fullWidth
                label="Coins"
                type="number"
                defaultValue={editDialog.data.reward?.coins || 0}
                sx={{ mb: 2 }}
                id="edit-coins"
              />
              <TextField
                fullWidth
                label="Cash (TZS)"
                type="number"
                defaultValue={editDialog.data.reward?.cash || 0}
                sx={{ mb: 2 }}
                id="edit-cash"
              />
              <FormControlLabel
                control={
                  <Switch
                    defaultChecked={editDialog.data.reward?.enabled || false}
                    id="edit-enabled"
                  />
                }
                label="Enabled"
              />
            </Box>
          ) : (
            <Box sx={{ pt: 2 }}>
              <TextField
                fullWidth
                label="User ID"
                sx={{ mb: 2 }}
                id="adjust-user-id"
              />
              <TextField
                fullWidth
                label="Amount (TZS)"
                type="number"
                helperText="Positive to add, negative to deduct"
                sx={{ mb: 2 }}
                id="adjust-amount"
              />
              <TextField
                fullWidth
                label="Reason"
                multiline
                rows={3}
                id="adjust-reason"
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog({ open: false, type: '', data: {} })}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              if (editDialog.type === 'reward_rate') {
                const coins = parseInt(document.getElementById('edit-coins').value);
                const cash = parseInt(document.getElementById('edit-cash').value);
                const enabled = document.getElementById('edit-enabled').checked;
                
                handleRewardRateUpdate(
                  editDialog.data.category,
                  editDialog.data.actionType,
                  { coins, cash, enabled }
                );
              } else {
                const userId = document.getElementById('adjust-user-id').value;
                const amount = parseInt(document.getElementById('adjust-amount').value);
                const reason = document.getElementById('adjust-reason').value;
                
                handleUserBalanceAdjustment(userId, amount, reason);
              }
              setEditDialog({ open: false, type: '', data: {} });
            }}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Emergency Mode Dialog */}
      <Dialog open={emergencyDialog} onClose={() => setEmergencyDialog(false)}>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Emergency sx={{ mr: 1, color: 'error.main' }} />
            Financial Emergency Mode
          </Box>
        </DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            Emergency mode will restrict financial operations and reduce reward rates.
          </Alert>
          
          {emergencyStatus?.active ? (
            <Typography>
              Emergency mode is currently active. Do you want to deactivate it?
            </Typography>
          ) : (
            <Typography>
              Do you want to activate financial emergency mode?
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEmergencyDialog(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color="error"
            onClick={() => handleEmergencyMode(!emergencyStatus?.active)}
          >
            {emergencyStatus?.active ? 'Deactivate' : 'Activate'} Emergency Mode
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminFinancialDashboard;
