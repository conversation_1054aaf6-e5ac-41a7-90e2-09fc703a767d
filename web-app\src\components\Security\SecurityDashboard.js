// ProChat Security Dashboard Component
// Real-time security monitoring and controls

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  Alert,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Switch,
  FormControlLabel,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Security,
  Warning,
  CheckCircle,
  Error,
  Shield,
  Devices,
  LocationOn,
  Block,
  Refresh,
  Settings,
  Emergency,
  Visibility,
  VisibilityOff,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useDeviceDetection } from '../../hooks/useDeviceDetection';
import {
  ipWhitelistManager,
  deviceAuthManager,
  rbacManager,
  emergencyModeManager,
} from '../../config/bankingSecurity';
import fraudDetectionService from '../../services/fraudDetection';
import walletSecurityService from '../../services/walletSecurity';
import { SecurityLogger } from '../../config/security';

const SecurityDashboard = () => {
  const { user } = useAuth();
  const { isMobile } = useDeviceDetection();
  const [securityStatus, setSecurityStatus] = useState({});
  const [emergencyDialogOpen, setEmergencyDialogOpen] = useState(false);
  const [showSensitiveData, setShowSensitiveData] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSecurityStatus();
    const interval = setInterval(loadSecurityStatus, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadSecurityStatus = async () => {
    try {
      setLoading(true);
      
      const [
        ipStatus,
        deviceStatus,
        emergencyStatus,
        fraudStats,
        walletStats,
      ] = await Promise.all([
        ipWhitelistManager.checkIPAccess(),
        deviceAuthManager.authorizeDevice(),
        emergencyModeManager.getEmergencyStatus(),
        fraudDetectionService.getSystemStats(),
        walletSecurityService.getSecurityStats(),
      ]);

      const userRole = rbacManager.getUserRole(user?.id);
      const userRiskLevel = fraudDetectionService.getUserRiskLevel(user?.id);
      const userRiskScore = fraudDetectionService.getUserRiskScore(user?.id);

      setSecurityStatus({
        ip: ipStatus,
        device: deviceStatus,
        emergency: emergencyStatus,
        fraud: fraudStats,
        wallet: walletStats,
        userRole,
        userRiskLevel,
        userRiskScore,
        lastUpdated: new Date().toISOString(),
      });

    } catch (error) {
      console.error('Failed to load security status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEmergencyMode = (activate) => {
    if (activate) {
      emergencyModeManager.activateEmergencyMode(
        'Manual activation from security dashboard',
        ['view_only']
      );
    } else {
      emergencyModeManager.deactivateEmergencyMode();
    }
    setEmergencyDialogOpen(false);
    loadSecurityStatus();
  };

  const getSecurityScore = () => {
    let score = 100;
    
    if (!securityStatus.ip?.allowed) score -= 30;
    if (!securityStatus.device?.authorized) score -= 25;
    if (securityStatus.emergency?.active) score -= 20;
    if (securityStatus.userRiskLevel === 'HIGH') score -= 15;
    if (securityStatus.userRiskLevel === 'CRITICAL') score -= 25;
    
    return Math.max(0, score);
  };

  const getSecurityLevel = (score) => {
    if (score >= 90) return { level: 'Excellent', color: 'success' };
    if (score >= 70) return { level: 'Good', color: 'info' };
    if (score >= 50) return { level: 'Fair', color: 'warning' };
    return { level: 'Poor', color: 'error' };
  };

  const SecurityCard = ({ title, status, icon, details, actions }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {icon}
          <Typography variant="h6" sx={{ ml: 1, flexGrow: 1 }}>
            {title}
          </Typography>
          <Chip
            label={status}
            color={status === 'Secure' ? 'success' : status === 'Warning' ? 'warning' : 'error'}
            size="small"
          />
        </Box>
        
        {details && (
          <Box sx={{ mb: 2 }}>
            {details.map((detail, index) => (
              <Typography key={index} variant="body2" color="text.secondary">
                {detail}
              </Typography>
            ))}
          </Box>
        )}
        
        {actions && (
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {actions}
          </Box>
        )}
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h5" sx={{ mb: 2 }}>Security Dashboard</Typography>
        <LinearProgress />
      </Box>
    );
  }

  const securityScore = getSecurityScore();
  const securityLevel = getSecurityLevel(securityScore);

  return (
    <Box sx={{ p: isMobile ? 2 : 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Security sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          Security Dashboard
        </Typography>
        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={loadSecurityStatus}
          size="small"
        >
          Refresh
        </Button>
      </Box>

      {/* Security Score */}
      <Card sx={{ mb: 3, bgcolor: securityLevel.color + '.light' }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Shield sx={{ fontSize: 40, color: securityLevel.color + '.main', mr: 2 }} />
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                Security Score: {securityScore}/100
              </Typography>
              <Typography variant="subtitle1" color={securityLevel.color + '.main'}>
                {securityLevel.level}
              </Typography>
            </Box>
          </Box>
          <LinearProgress
            variant="determinate"
            value={securityScore}
            color={securityLevel.color}
            sx={{ height: 8, borderRadius: 4 }}
          />
        </CardContent>
      </Card>

      {/* Emergency Mode Alert */}
      {securityStatus.emergency?.active && (
        <Alert
          severity="error"
          sx={{ mb: 3 }}
          action={
            <Button
              color="inherit"
              size="small"
              onClick={() => setEmergencyDialogOpen(true)}
            >
              Manage
            </Button>
          }
        >
          🚨 Emergency Mode Active: {securityStatus.emergency.reason}
        </Alert>
      )}

      {/* Security Cards Grid */}
      <Grid container spacing={3}>
        {/* IP Security */}
        <Grid item xs={12} md={6}>
          <SecurityCard
            title="Network Security"
            status={securityStatus.ip?.allowed ? 'Secure' : 'Blocked'}
            icon={<LocationOn color={securityStatus.ip?.allowed ? 'success' : 'error'} />}
            details={[
              `IP Address: ${showSensitiveData ? securityStatus.ip?.ip : '***.***.***.**'}`,
              `Access Level: ${securityStatus.ip?.allowed ? 'Authorized' : 'Restricted'}`,
            ]}
            actions={[
              <Tooltip key="toggle" title="Toggle IP visibility">
                <IconButton
                  size="small"
                  onClick={() => setShowSensitiveData(!showSensitiveData)}
                >
                  {showSensitiveData ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </Tooltip>
            ]}
          />
        </Grid>

        {/* Device Security */}
        <Grid item xs={12} md={6}>
          <SecurityCard
            title="Device Security"
            status={securityStatus.device?.authorized ? 'Trusted' : 'Unverified'}
            icon={<Devices color={securityStatus.device?.authorized ? 'success' : 'warning'} />}
            details={[
              `Device ID: ${showSensitiveData ? securityStatus.device?.deviceId?.substring(0, 12) + '...' : '************'}`,
              `Trust Level: ${securityStatus.device?.deviceInfo?.trusted ? 'Trusted' : 'Pending'}`,
              `Last Seen: ${securityStatus.device?.deviceInfo?.lastSeen ? new Date(securityStatus.device.deviceInfo.lastSeen).toLocaleString() : 'Unknown'}`,
            ]}
            actions={[
              <Button key="manage" size="small" variant="outlined">
                Manage Devices
              </Button>
            ]}
          />
        </Grid>

        {/* User Risk Assessment */}
        <Grid item xs={12} md={6}>
          <SecurityCard
            title="Risk Assessment"
            status={securityStatus.userRiskLevel || 'Unknown'}
            icon={<Warning color={
              securityStatus.userRiskLevel === 'LOW' ? 'success' :
              securityStatus.userRiskLevel === 'MEDIUM' ? 'warning' : 'error'
            } />}
            details={[
              `Risk Score: ${(securityStatus.userRiskScore * 100).toFixed(1)}%`,
              `Risk Level: ${securityStatus.userRiskLevel || 'Not assessed'}`,
              `Account Type: ${securityStatus.userRole?.name || 'Standard User'}`,
            ]}
          />
        </Grid>

        {/* Wallet Security */}
        <Grid item xs={12} md={6}>
          <SecurityCard
            title="Wallet Security"
            status="Protected"
            icon={<Security color="success" />}
            details={[
              `Total Transactions: ${securityStatus.wallet?.totalTransactions || 0}`,
              `Suspended Wallets: ${securityStatus.wallet?.suspendedWallets || 0}`,
              `Security Level: Banking Grade`,
            ]}
            actions={[
              <Button key="wallet" size="small" variant="outlined">
                Wallet Settings
              </Button>
            ]}
          />
        </Grid>

        {/* System Status */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                System Security Status
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="primary.main">
                      {securityStatus.fraud?.totalUsers || 0}
                    </Typography>
                    <Typography variant="caption">Active Users</Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="error.main">
                      {securityStatus.fraud?.blockedUsers || 0}
                    </Typography>
                    <Typography variant="caption">Blocked Users</Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="warning.main">
                      {securityStatus.fraud?.suspiciousActivities || 0}
                    </Typography>
                    <Typography variant="caption">Suspicious Activities</Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main">
                      {securityStatus.fraud?.activeRules || 0}
                    </Typography>
                    <Typography variant="caption">Security Rules</Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Emergency Controls */}
        <Grid item xs={12}>
          <Card sx={{ border: '2px solid', borderColor: 'error.main' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Emergency sx={{ color: 'error.main', mr: 2 }} />
                <Typography variant="h6" color="error.main">
                  Emergency Controls
                </Typography>
              </Box>
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Emergency mode will restrict all operations except viewing. Use only in case of security threats.
              </Typography>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={securityStatus.emergency?.active || false}
                    onChange={(e) => setEmergencyDialogOpen(true)}
                    color="error"
                  />
                }
                label="Emergency Mode"
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Emergency Mode Dialog */}
      <Dialog open={emergencyDialogOpen} onClose={() => setEmergencyDialogOpen(false)}>
        <DialogTitle>
          {securityStatus.emergency?.active ? 'Deactivate Emergency Mode' : 'Activate Emergency Mode'}
        </DialogTitle>
        <DialogContent>
          <Typography>
            {securityStatus.emergency?.active
              ? 'Are you sure you want to deactivate emergency mode? This will restore normal operations.'
              : 'Are you sure you want to activate emergency mode? This will restrict all operations except viewing.'}
          </Typography>
          {securityStatus.emergency?.active && (
            <Alert severity="info" sx={{ mt: 2 }}>
              Emergency mode has been active for{' '}
              {Math.round(securityStatus.emergency.duration / 1000 / 60)} minutes
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEmergencyDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={() => handleEmergencyMode(!securityStatus.emergency?.active)}
            color={securityStatus.emergency?.active ? 'primary' : 'error'}
            variant="contained"
          >
            {securityStatus.emergency?.active ? 'Deactivate' : 'Activate'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SecurityDashboard;
