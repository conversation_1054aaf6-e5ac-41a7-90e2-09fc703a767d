// ProChat Jobs Platform Landing Page
// World's most advanced AI-powered job platform

import React from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Avatar,
  Paper,
  Divider,
} from '@mui/material';
import {
  Work,
  SmartToy,
  Security,
  Psychology,
  VideoCall,
  Mic,
  School,
  TrendingUp,
  MonetizationOn,
  Verified,
  Star,
  CheckCircle,
  Speed,
  Language,
  Public,
} from '@mui/icons-material';

const JobsPlatformLanding = () => {
  const features = [
    {
      icon: <SmartToy sx={{ fontSize: 40, color: 'primary.main' }} />,
      title: '🤖 Mahojiano ya AI',
      description: 'AI ya hali ya juu inafanya mahojiano salama na uchambuzi wa wakati halisi',
      benefits: [
        'Mahojiano ya video ya moja kwa moja na uchambuzi wa AI',
        'Tathmini ya majibu ya video yaliyorekodiwa',
        '<PERSON>gu<PERSON> za mahojiano ya sauti tu',
        '<PERSON><PERSON><PERSON> ya ujuzi wa kiufundi',
        'Uchambuzi wa uongozi na utamaduni',
        'Alama za otomatiki na maoni',
      ],
    },
    {
      icon: <Security sx={{ fontSize: 40, color: 'error.main' }} />,
      title: '🛡️ Teknolojia ya Kuzuia Udanganyifu',
      description: 'Usalama wa kijeshi unahakikisha mahojiano ya haki na ya uaminifu',
      benefits: [
        'Uthibitishaji wa kutambua uso',
        'Uthibitishaji wa biometric ya sauti',
        'Ufuatiliaji wa tabia wa wakati halisi',
        'Mfumo wa kugundua unakiliaji',
        'Kugundua msaada wa AI',
        'Ufuatiliaji wa mazingira',
      ],
    },
    {
      icon: <Psychology sx={{ fontSize: 40, color: 'success.main' }} />,
      title: '🎯 Ulinganishaji wa Kazi wa Akili',
      description: 'AI inachambua wasifu na kulinganisha fursa kamili za kazi',
      benefits: [
        'Uchambuzi wa ulinganifu wa ujuzi',
        'Alama za umuhimu wa uzoefu',
        'Tathmini ya ulinganifu wa utamaduni',
        'Kulinganisha matarajio ya mshahara',
        'Upangaji wa njia ya kazi',
        'Uchambuzi wa uwezekano wa ukuaji',
      ],
    },
    {
      icon: <School sx={{ fontSize: 40, color: 'warning.main' }} />,
      title: '📚 Maendeleo ya Kazi',
      description: 'Jukwaa kamili la kujifunza na vyeti na mafunzo',
      benefits: [
        'Kozi za maendeleo ya kitaaluma',
        'Vyeti vya viwanda',
        'Mafunzo ya kazi ya mtu mmoja kwa mmoja',
        'Mafunzo ya maandalizi ya mahojiano',
        'Zana za kuboresha CV',
        'Uchambuzi wa pengo la ujuzi',
      ],
    },
  ];

  const stats = [
    { label: 'Kazi Zinazotumika', value: '10,000+', icon: <Work /> },
    { label: 'Mahojiano ya AI', value: '50,000+', icon: <SmartToy /> },
    { label: 'Kiwango cha Mafanikio', value: '95%', icon: <TrendingUp /> },
    { label: 'Makampuni', value: '2,500+', icon: <Verified /> },
  ];

  const testimonials = [
    {
      name: 'Sarah Mwalimu',
      role: 'Msanidi Programu',
      company: 'TechCorp Tanzania',
      avatar: '/avatars/sarah.jpg',
      rating: 5,
      comment: 'Mahojiano ya AI yalikuwa ya ajabu! Yalihisi ya kawaida na maoni yalikuwa ya kina sana. Nilipata kazi ndani ya wiki moja!',
    },
    {
      name: 'Michael Juma',
      role: 'Meneja wa Uuzaji',
      company: 'Digital Solutions',
      avatar: '/avatars/michael.jpg',
      rating: 5,
      comment: 'Teknolojia ya kuzuia udanganyifu ilinipa imani kwamba mchakato ulikuwa wa haki. Mafunzo ya kazi yalinisaidia kupata kazi yangu ya ndoto!',
    },
    {
      name: 'Amina Hassan',
      role: 'Mchambuzi wa Data',
      company: 'Analytics Pro',
      avatar: '/avatars/amina.jpg',
      rating: 5,
      comment: 'Ulinganishaji wa kazi wa akili ni wa ajabu! Ulinipata kazi ambazo singezigundua kamwe. Jukwaa hili limebadilisha kazi yangu!',
    },
  ];

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          py: 8,
          textAlign: 'center',
        }}
      >
        <Container maxWidth="lg">
          <Typography variant="h2" sx={{ fontWeight: 'bold', mb: 2 }}>
            💼 Jukwaa la Kazi la AI la Kisasa Zaidi Duniani
          </Typography>
          <Typography variant="h5" sx={{ mb: 4, opacity: 0.9 }}>
            Tafuta kazi, omba kwa msaada wa AI, fanya mahojiano salama, na jenga kazi yako - vyote vikiongozwa na teknolojia ya AI ya hali ya juu!
          </Typography>
          
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mb: 4, flexWrap: 'wrap' }}>
            <Chip
              icon={<SmartToy />}
              label="Mahojiano ya AI"
              sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
            />
            <Chip
              icon={<Security />}
              label="Teknolojia ya Kuzuia Udanganyifu"
              sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
            />
            <Chip
              icon={<Psychology />}
              label="Ulinganishaji wa Kazi wa Akili"
              sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
            />
            <Chip
              icon={<School />}
              label="Maendeleo ya Kazi"
              sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
            />
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              sx={{
                bgcolor: 'white',
                color: 'primary.main',
                '&:hover': { bgcolor: 'grey.100' },
                px: 4,
                py: 1.5,
              }}
            >
              🔍 Tafuta Kazi Sasa
            </Button>
            <Button
              variant="outlined"
              size="large"
              sx={{
                borderColor: 'white',
                color: 'white',
                '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' },
                px: 4,
                py: 1.5,
              }}
            >
              📝 Chapisha Kazi
            </Button>
          </Box>
        </Container>
      </Box>

      {/* Stats Section */}
      <Container maxWidth="lg" sx={{ py: 6 }}>
        <Grid container spacing={4}>
          {stats.map((stat, index) => (
            <Grid item xs={6} md={3} key={index}>
              <Card sx={{ textAlign: 'center', p: 3 }}>
                <Box sx={{ color: 'primary.main', mb: 2 }}>
                  {stat.icon}
                </Box>
                <Typography variant="h3" sx={{ fontWeight: 'bold', color: 'primary.main', mb: 1 }}>
                  {stat.value}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  {stat.label}
                </Typography>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Features Section */}
      <Box sx={{ bgcolor: 'grey.50', py: 8 }}>
        <Container maxWidth="lg">
          <Typography variant="h3" sx={{ textAlign: 'center', mb: 2, fontWeight: 'bold' }}>
            🚀 Vipengele vya Mapinduzi
          </Typography>
          <Typography variant="h6" sx={{ textAlign: 'center', mb: 6, color: 'text.secondary' }}>
            Furahia mustakabali wa kutafuta kazi na kuajiri kwa jukwaa letu la AI
          </Typography>

          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} md={6} key={index}>
                <Card sx={{ height: '100%', p: 3 }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      {feature.icon}
                      <Typography variant="h5" sx={{ ml: 2, fontWeight: 'bold' }}>
                        {feature.title}
                      </Typography>
                    </Box>
                    
                    <Typography variant="body1" sx={{ mb: 3, color: 'text.secondary' }}>
                      {feature.description}
                    </Typography>
                    
                    <List dense>
                      {feature.benefits.map((benefit, idx) => (
                        <ListItem key={idx} sx={{ px: 0 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            <CheckCircle sx={{ fontSize: 20, color: 'success.main' }} />
                          </ListItemIcon>
                          <ListItemText 
                            primary={benefit}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* How It Works Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography variant="h3" sx={{ textAlign: 'center', mb: 6, fontWeight: 'bold' }}>
          🎯 Jinsi Inavyofanya Kazi
        </Typography>

        <Grid container spacing={4} alignItems="center">
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 4, textAlign: 'center', height: '100%' }}>
              <Box sx={{ color: 'primary.main', mb: 2 }}>
                <Work sx={{ fontSize: 60 }} />
              </Box>
              <Typography variant="h5" sx={{ mb: 2, fontWeight: 'bold' }}>
                1. Vinjari Kazi
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Tumia utafutaji wetu wa AI kupata kazi zinazofaa kabisa ujuzi, uzoefu, na malengo yako ya kazi.
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 4, textAlign: 'center', height: '100%' }}>
              <Box sx={{ color: 'success.main', mb: 2 }}>
                <SmartToy sx={{ fontSize: 60 }} />
              </Box>
              <Typography variant="h5" sx={{ mb: 2, fontWeight: 'bold' }}>
                2. Mahojiano ya AI
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Fanya mahojiano salama ya AI na teknolojia ya kuzuia udanganyifu. Pata maoni na alama papo hapo.
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 4, textAlign: 'center', height: '100%' }}>
              <Box sx={{ color: 'warning.main', mb: 2 }}>
                <TrendingUp sx={{ fontSize: 60 }} />
              </Box>
              <Typography variant="h5" sx={{ mb: 2, fontWeight: 'bold' }}>
                3. Pata Ajira
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Pata kazi yako ya ndoto kwa kiwango chetu cha mafanikio cha 95%. Endelea kukua na jukwaa letu la maendeleo ya kazi.
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </Container>

      {/* Testimonials Section */}
      <Box sx={{ bgcolor: 'grey.50', py: 8 }}>
        <Container maxWidth="lg">
          <Typography variant="h3" sx={{ textAlign: 'center', mb: 6, fontWeight: 'bold' }}>
            ⭐ Hadithi za Mafanikio
          </Typography>

          <Grid container spacing={4}>
            {testimonials.map((testimonial, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Card sx={{ p: 3, height: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar src={testimonial.avatar} sx={{ mr: 2 }}>
                      {testimonial.name.charAt(0)}
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                        {testimonial.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {testimonial.role} at {testimonial.company}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex' }}>
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} sx={{ color: 'gold', fontSize: 20 }} />
                      ))}
                    </Box>
                  </Box>
                  
                  <Typography variant="body1" sx={{ fontStyle: 'italic' }}>
                    "{testimonial.comment}"
                  </Typography>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Unique Features Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography variant="h3" sx={{ textAlign: 'center', mb: 6, fontWeight: 'bold' }}>
          🌟 Kwa Nini Uchague ProChat Jobs?
        </Typography>

        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <VideoCall sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  Miundo Mbalimbali ya Mahojiano
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Video, sauti, maandishi, na mahojiano ya moja kwa moja - chagua kinachokufaa zaidi
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Speed sx={{ fontSize: 40, color: 'success.main', mr: 2 }} />
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  Matokeo ya Papo Hapo
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Pata matokeo ya mahojiano na maoni mara baada ya kumaliza
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Language sx={{ fontSize: 40, color: 'warning.main', mr: 2 }} />
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  Msaada wa Lugha Nyingi
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Inapatikana kwa Kiingereza, Kiswahili, Kifaransa, na lugha nyingi zaidi
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <MonetizationOn sx={{ fontSize: 40, color: 'info.main', mr: 2 }} />
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  Soko la Kazi za Huru
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Pata miradi ya kazi za huru na kazi za muda na malipo salama
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Public sx={{ fontSize: 40, color: 'secondary.main', mr: 2 }} />
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  Fursa za Kimataifa
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Fikia kazi kutoka makampuni duniani kote na chaguo za kufanya kazi kwa mbali
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Verified sx={{ fontSize: 40, color: 'error.main', mr: 2 }} />
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  Makampuni Yaliyothibitishwa
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Makampuni yote yamethibitishwa ili kuhakikisha fursa za kazi halali
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Container>

      {/* CTA Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          py: 8,
          textAlign: 'center',
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h3" sx={{ fontWeight: 'bold', mb: 2 }}>
            🚀 Uko Tayari Kubadilisha Kazi Yako?
          </Typography>
          <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
            Jiunge na maelfu ya wataalamu ambao wamepata kazi zao za ndoto kupitia jukwaa letu la AI!
          </Typography>

          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              sx={{
                bgcolor: 'white',
                color: 'primary.main',
                '&:hover': { bgcolor: 'grey.100' },
                px: 4,
                py: 1.5,
              }}
            >
              🎯 Anza Kutafuta Kazi
            </Button>
            <Button
              variant="outlined"
              size="large"
              sx={{
                borderColor: 'white',
                color: 'white',
                '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' },
                px: 4,
                py: 1.5,
              }}
            >
              💼 Ajiri Vipaji
            </Button>
          </Box>

          <Typography variant="body1" sx={{ mt: 4, opacity: 0.8 }}>
            ✨ Bure kujiunge • 🤖 Inaongozwa na AI • 🛡️ Salama • 🌍 Ufikio wa kimataifa
          </Typography>
        </Container>
      </Box>
    </Box>
  );
};

export default JobsPlatformLanding;
