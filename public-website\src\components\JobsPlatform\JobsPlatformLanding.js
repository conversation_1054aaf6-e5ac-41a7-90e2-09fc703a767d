// ProChat Jobs Platform Landing Page
// World's most advanced AI-powered job platform

import React from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Avatar,
  Paper,
  Divider,
} from '@mui/material';
import {
  Work,
  SmartToy,
  Security,
  Psychology,
  VideoCall,
  Mic,
  School,
  TrendingUp,
  MonetizationOn,
  Verified,
  Star,
  CheckCircle,
  Speed,
  Language,
  Public,
} from '@mui/icons-material';

const JobsPlatformLanding = () => {
  const features = [
    {
      icon: <SmartToy sx={{ fontSize: 40, color: 'primary.main' }} />,
      title: '🤖 AI-Powered Interviews',
      description: 'Advanced AI conducts secure interviews with real-time analysis',
      benefits: [
        'Live video interviews with AI analysis',
        'Recorded video responses evaluation',
        'Voice-only interview options',
        'Technical skills assessment',
        'Personality and cultural fit analysis',
        'Automated scoring and feedback',
      ],
    },
    {
      icon: <Security sx={{ fontSize: 40, color: 'error.main' }} />,
      title: '🛡️ Anti-Cheating Technology',
      description: 'Military-grade security ensures fair and honest interviews',
      benefits: [
        'Face recognition verification',
        'Voice biometric authentication',
        'Real-time behavior monitoring',
        'Plagiarism detection system',
        'AI assistance detection',
        'Environment monitoring',
      ],
    },
    {
      icon: <Psychology sx={{ fontSize: 40, color: 'success.main' }} />,
      title: '🎯 Smart Job Matching',
      description: 'AI analyzes profiles and matches perfect job opportunities',
      benefits: [
        'Skill compatibility analysis',
        'Experience relevance scoring',
        'Cultural fit assessment',
        'Salary expectation matching',
        'Career path alignment',
        'Growth potential analysis',
      ],
    },
    {
      icon: <School sx={{ fontSize: 40, color: 'warning.main' }} />,
      title: '📚 Career Development',
      description: 'Complete learning platform with certifications and coaching',
      benefits: [
        'Professional development courses',
        'Industry certifications',
        'One-on-one career coaching',
        'Interview preparation training',
        'CV optimization tools',
        'Skill gap analysis',
      ],
    },
  ];

  const stats = [
    { label: 'Active Jobs', value: '10,000+', icon: <Work /> },
    { label: 'AI Interviews', value: '50,000+', icon: <SmartToy /> },
    { label: 'Success Rate', value: '95%', icon: <TrendingUp /> },
    { label: 'Companies', value: '2,500+', icon: <Verified /> },
  ];

  const testimonials = [
    {
      name: 'Sarah Johnson',
      role: 'Software Developer',
      company: 'TechCorp',
      avatar: '/avatars/sarah.jpg',
      rating: 5,
      comment: 'The AI interview was amazing! It felt natural and the feedback was incredibly detailed. Got hired within a week!',
    },
    {
      name: 'Michael Chen',
      role: 'Marketing Manager',
      company: 'Digital Solutions',
      avatar: '/avatars/michael.jpg',
      rating: 5,
      comment: 'Anti-cheating technology gave me confidence that the process was fair. The career coaching helped me land my dream job!',
    },
    {
      name: 'Amina Hassan',
      role: 'Data Analyst',
      company: 'Analytics Pro',
      avatar: '/avatars/amina.jpg',
      rating: 5,
      comment: 'Smart job matching is incredible! It found jobs I never would have discovered. The platform changed my career!',
    },
  ];

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          py: 8,
          textAlign: 'center',
        }}
      >
        <Container maxWidth="lg">
          <Typography variant="h2" sx={{ fontWeight: 'bold', mb: 2 }}>
            💼 World's Most Advanced AI Jobs Platform
          </Typography>
          <Typography variant="h5" sx={{ mb: 4, opacity: 0.9 }}>
            Find jobs, apply with AI assistance, take secure interviews, and build your career - all powered by cutting-edge AI technology!
          </Typography>
          
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mb: 4, flexWrap: 'wrap' }}>
            <Chip 
              icon={<SmartToy />} 
              label="AI-Powered Interviews" 
              sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }} 
            />
            <Chip 
              icon={<Security />} 
              label="Anti-Cheating Technology" 
              sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }} 
            />
            <Chip 
              icon={<Psychology />} 
              label="Smart Job Matching" 
              sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }} 
            />
            <Chip 
              icon={<School />} 
              label="Career Development" 
              sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }} 
            />
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              sx={{ 
                bgcolor: 'white', 
                color: 'primary.main',
                '&:hover': { bgcolor: 'grey.100' },
                px: 4,
                py: 1.5,
              }}
            >
              🔍 Find Jobs Now
            </Button>
            <Button
              variant="outlined"
              size="large"
              sx={{ 
                borderColor: 'white', 
                color: 'white',
                '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' },
                px: 4,
                py: 1.5,
              }}
            >
              📝 Post a Job
            </Button>
          </Box>
        </Container>
      </Box>

      {/* Stats Section */}
      <Container maxWidth="lg" sx={{ py: 6 }}>
        <Grid container spacing={4}>
          {stats.map((stat, index) => (
            <Grid item xs={6} md={3} key={index}>
              <Card sx={{ textAlign: 'center', p: 3 }}>
                <Box sx={{ color: 'primary.main', mb: 2 }}>
                  {stat.icon}
                </Box>
                <Typography variant="h3" sx={{ fontWeight: 'bold', color: 'primary.main', mb: 1 }}>
                  {stat.value}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  {stat.label}
                </Typography>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Features Section */}
      <Box sx={{ bgcolor: 'grey.50', py: 8 }}>
        <Container maxWidth="lg">
          <Typography variant="h3" sx={{ textAlign: 'center', mb: 2, fontWeight: 'bold' }}>
            🚀 Revolutionary Features
          </Typography>
          <Typography variant="h6" sx={{ textAlign: 'center', mb: 6, color: 'text.secondary' }}>
            Experience the future of job searching and hiring with our AI-powered platform
          </Typography>

          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} md={6} key={index}>
                <Card sx={{ height: '100%', p: 3 }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      {feature.icon}
                      <Typography variant="h5" sx={{ ml: 2, fontWeight: 'bold' }}>
                        {feature.title}
                      </Typography>
                    </Box>
                    
                    <Typography variant="body1" sx={{ mb: 3, color: 'text.secondary' }}>
                      {feature.description}
                    </Typography>
                    
                    <List dense>
                      {feature.benefits.map((benefit, idx) => (
                        <ListItem key={idx} sx={{ px: 0 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            <CheckCircle sx={{ fontSize: 20, color: 'success.main' }} />
                          </ListItemIcon>
                          <ListItemText 
                            primary={benefit}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* How It Works Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography variant="h3" sx={{ textAlign: 'center', mb: 6, fontWeight: 'bold' }}>
          🎯 How It Works
        </Typography>

        <Grid container spacing={4} alignItems="center">
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 4, textAlign: 'center', height: '100%' }}>
              <Box sx={{ color: 'primary.main', mb: 2 }}>
                <Work sx={{ fontSize: 60 }} />
              </Box>
              <Typography variant="h5" sx={{ mb: 2, fontWeight: 'bold' }}>
                1. Browse Jobs
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Use our AI-powered search to find jobs that perfectly match your skills, experience, and career goals.
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 4, textAlign: 'center', height: '100%' }}>
              <Box sx={{ color: 'success.main', mb: 2 }}>
                <SmartToy sx={{ fontSize: 60 }} />
              </Box>
              <Typography variant="h5" sx={{ mb: 2, fontWeight: 'bold' }}>
                2. AI Interview
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Take secure AI-powered interviews with anti-cheating technology. Get instant feedback and scoring.
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 4, textAlign: 'center', height: '100%' }}>
              <Box sx={{ color: 'warning.main', mb: 2 }}>
                <TrendingUp sx={{ fontSize: 60 }} />
              </Box>
              <Typography variant="h5" sx={{ mb: 2, fontWeight: 'bold' }}>
                3. Get Hired
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Land your dream job with our 95% success rate. Continue growing with our career development platform.
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </Container>

      {/* Testimonials Section */}
      <Box sx={{ bgcolor: 'grey.50', py: 8 }}>
        <Container maxWidth="lg">
          <Typography variant="h3" sx={{ textAlign: 'center', mb: 6, fontWeight: 'bold' }}>
            ⭐ Success Stories
          </Typography>

          <Grid container spacing={4}>
            {testimonials.map((testimonial, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Card sx={{ p: 3, height: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar src={testimonial.avatar} sx={{ mr: 2 }}>
                      {testimonial.name.charAt(0)}
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                        {testimonial.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {testimonial.role} at {testimonial.company}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex' }}>
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} sx={{ color: 'gold', fontSize: 20 }} />
                      ))}
                    </Box>
                  </Box>
                  
                  <Typography variant="body1" sx={{ fontStyle: 'italic' }}>
                    "{testimonial.comment}"
                  </Typography>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Unique Features Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography variant="h3" sx={{ textAlign: 'center', mb: 6, fontWeight: 'bold' }}>
          🌟 Why Choose ProChat Jobs?
        </Typography>

        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <VideoCall sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  Multiple Interview Formats
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Video, audio, text, and live interviews - choose what works best for you
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Speed sx={{ fontSize: 40, color: 'success.main', mr: 2 }} />
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  Instant Results
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Get interview results and feedback immediately after completion
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Language sx={{ fontSize: 40, color: 'warning.main', mr: 2 }} />
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  Multi-Language Support
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Available in English, Swahili, French, and more languages
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <MonetizationOn sx={{ fontSize: 40, color: 'info.main', mr: 2 }} />
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  Freelance Marketplace
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Find freelance projects and gig work with secure payments
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Public sx={{ fontSize: 40, color: 'secondary.main', mr: 2 }} />
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  Global Opportunities
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Access jobs from companies worldwide with remote work options
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Verified sx={{ fontSize: 40, color: 'error.main', mr: 2 }} />
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  Verified Companies
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  All companies are verified to ensure legitimate job opportunities
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Container>

      {/* CTA Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          py: 8,
          textAlign: 'center',
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h3" sx={{ fontWeight: 'bold', mb: 2 }}>
            🚀 Ready to Transform Your Career?
          </Typography>
          <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
            Join thousands of professionals who have found their dream jobs through our AI-powered platform!
          </Typography>
          
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              sx={{ 
                bgcolor: 'white', 
                color: 'primary.main',
                '&:hover': { bgcolor: 'grey.100' },
                px: 4,
                py: 1.5,
              }}
            >
              🎯 Start Job Search
            </Button>
            <Button
              variant="outlined"
              size="large"
              sx={{ 
                borderColor: 'white', 
                color: 'white',
                '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' },
                px: 4,
                py: 1.5,
              }}
            >
              💼 Hire Talent
            </Button>
          </Box>

          <Typography variant="body1" sx={{ mt: 4, opacity: 0.8 }}>
            ✨ Free to join • 🤖 AI-powered • 🛡️ Secure • 🌍 Global reach
          </Typography>
        </Container>
      </Box>
    </Box>
  );
};

export default JobsPlatformLanding;
