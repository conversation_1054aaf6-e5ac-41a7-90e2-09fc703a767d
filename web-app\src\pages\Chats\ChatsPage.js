import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Typography,
  Badge,
  IconButton,
  TextField,
  InputAdornment,
  Fab,
  Chip,
  Divider,
} from '@mui/material';
import {
  Search,
  Add,
  VideoCall,
  Call,
  MoreVert,
  Circle,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useSocket } from '../../contexts/SocketContext';
import { useDeviceDetection } from '../../hooks/useDeviceDetection';

// Mock data for chats (same as mobile app)
const mockChats = [
  {
    id: '1',
    name: '<PERSON>',
    avatar: 'https://via.placeholder.com/50',
    lastMessage: 'Habari za leo?',
    timestamp: '10:30',
    unreadCount: 2,
    isOnline: true,
    isGroup: false,
  },
  {
    id: '2',
    name: '<PERSON>',
    avatar: 'https://via.placeholder.com/50',
    lastMessage: 'Asante sana kwa msaada',
    timestamp: 'Jana',
    unreadCount: 0,
    isOnline: false,
    isGroup: false,
  },
  {
    id: '3',
    name: 'Kikundi cha Biashara',
    avatar: 'https://via.placeholder.com/50',
    lastMessage: 'Peter: Tutakutana kesho',
    timestamp: 'Juzi',
    unreadCount: 5,
    isOnline: false,
    isGroup: true,
    members: 12,
  },
  {
    id: '4',
    name: 'Sarah Johnson',
    avatar: 'https://via.placeholder.com/50',
    lastMessage: 'Nimepokea pesa, asante!',
    timestamp: 'Wiki iliyopita',
    unreadCount: 0,
    isOnline: true,
    isGroup: false,
  },
  {
    id: '5',
    name: 'ProChat Support',
    avatar: 'https://via.placeholder.com/50',
    lastMessage: 'Karibu ProChat! Tupo hapa kukusaidia.',
    timestamp: 'Mwezi uliopita',
    unreadCount: 0,
    isOnline: true,
    isGroup: false,
    isOfficial: true,
  },
];

const ChatsPage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { onlineUsers, isConnected } = useSocket();
  const { isMobile } = useDeviceDetection();
  const [chats, setChats] = useState(mockChats);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredChats, setFilteredChats] = useState(mockChats);

  useEffect(() => {
    // Filter chats based on search query
    if (searchQuery.trim()) {
      const filtered = chats.filter(chat =>
        chat.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        chat.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredChats(filtered);
    } else {
      setFilteredChats(chats);
    }
  }, [searchQuery, chats]);

  const handleChatPress = (chatId) => {
    navigate(`/chat/${chatId}`);
  };

  const handleNewChat = () => {
    navigate('/contacts');
  };

  const formatTimestamp = (timestamp) => {
    // Simple timestamp formatting
    return timestamp;
  };

  const ChatItem = ({ chat }) => (
    <>
      <ListItem
        button
        onClick={() => handleChatPress(chat.id)}
        sx={{
          py: 1.5,
          px: isMobile ? 2 : 3,
          '&:hover': {
            bgcolor: 'action.hover',
          },
        }}
      >
        <ListItemAvatar>
          <Badge
            overlap="circular"
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            badgeContent={
              chat.isOnline ? (
                <Circle sx={{ color: 'success.main', fontSize: 12 }} />
              ) : null
            }
          >
            <Avatar src={chat.avatar} sx={{ width: 50, height: 50 }}>
              {chat.name.charAt(0)}
            </Avatar>
          </Badge>
        </ListItemAvatar>

        <ListItemText
          primary={
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <Typography
                variant="subtitle2"
                sx={{
                  fontWeight: chat.unreadCount > 0 ? 'bold' : 'normal',
                  flexGrow: 1,
                  mr: 1,
                }}
              >
                {chat.name}
              </Typography>
              {chat.isOfficial && (
                <Chip
                  label="Rasmi"
                  size="small"
                  color="primary"
                  sx={{ height: 16, fontSize: '0.6rem', mr: 1 }}
                />
              )}
              {chat.isGroup && (
                <Chip
                  label={`${chat.members} wanachama`}
                  size="small"
                  variant="outlined"
                  sx={{ height: 16, fontSize: '0.6rem', mr: 1 }}
                />
              )}
              <Typography variant="caption" color="text.secondary">
                {formatTimestamp(chat.timestamp)}
              </Typography>
            </Box>
          }
          secondary={
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{
                  fontWeight: chat.unreadCount > 0 ? 'bold' : 'normal',
                  flexGrow: 1,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  mr: 1,
                }}
              >
                {chat.lastMessage}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                {chat.unreadCount > 0 && (
                  <Badge
                    badgeContent={chat.unreadCount}
                    color="primary"
                    sx={{
                      '& .MuiBadge-badge': {
                        fontSize: '0.7rem',
                        height: 18,
                        minWidth: 18,
                      },
                    }}
                  />
                )}
                <IconButton size="small" sx={{ ml: 0.5 }}>
                  <MoreVert fontSize="small" />
                </IconButton>
              </Box>
            </Box>
          }
        />
      </ListItem>
      <Divider variant="inset" component="li" />
    </>
  );

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Search Bar */}
      <Box sx={{ p: 2, pb: 1 }}>
        <TextField
          fullWidth
          placeholder="Tafuta mazungumzo..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search color="action" />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 3,
              bgcolor: 'background.paper',
            },
          }}
        />
      </Box>

      {/* Connection Status */}
      {!isConnected && (
        <Box
          sx={{
            bgcolor: 'warning.light',
            color: 'warning.contrastText',
            p: 1,
            textAlign: 'center',
            mx: 2,
            borderRadius: 1,
            mb: 1,
          }}
        >
          <Typography variant="caption">
            Hakuna muunganiko. Mazungumzo hayatafunguka.
          </Typography>
        </Box>
      )}

      {/* Online Users Count */}
      <Box sx={{ px: 2, pb: 1 }}>
        <Typography variant="caption" color="text.secondary">
          {onlineUsers.length} watumiaji wako mtandaoni
        </Typography>
      </Box>

      {/* Chats List */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        {filteredChats.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body2" color="text.secondary">
              {searchQuery ? 'Hakuna mazungumzo yaliyopatikana' : 'Hakuna mazungumzo'}
            </Typography>
          </Box>
        ) : (
          <List sx={{ py: 0 }}>
            {filteredChats.map((chat) => (
              <ChatItem key={chat.id} chat={chat} />
            ))}
          </List>
        )}
      </Box>

      {/* Floating Action Button (Mobile only) */}
      {isMobile && (
        <Fab
          color="primary"
          sx={{
            position: 'fixed',
            bottom: 80,
            right: 16,
            zIndex: 1000,
          }}
          onClick={handleNewChat}
        >
          <Add />
        </Fab>
      )}
    </Box>
  );
};

export default ChatsPage;
