// ProChat Advanced Admin Financial Control System
// Complete control over all financial aspects of the platform

import { SecurityLogger } from '../config/security';
import adminSecurityService from './adminSecurity';
import engagementRewardsService from './engagementRewards';
import walletSecurityService from './walletSecurity';
import complianceAuditService from './complianceAudit';

class AdminFinancialControlService {
  constructor() {
    this.rewardSettings = new Map();
    this.financialPolicies = new Map();
    this.transactionLimits = new Map();
    this.emergencyControls = new Map();
    this.auditTrail = new Map();
    
    this.initializeFinancialControl();
  }

  initializeFinancialControl() {
    // Initialize default reward settings
    this.setupDefaultRewardSettings();
    
    // Initialize financial policies
    this.setupFinancialPolicies();
    
    // Initialize transaction limits
    this.setupTransactionLimits();
    
    // Initialize emergency controls
    this.setupEmergencyControls();
  }

  setupDefaultRewardSettings() {
    // 💰 ADMIN CONTROLLED REWARD RATES
    this.rewardSettings.set('post_rewards', {
      post_created: { coins: 5, cash: 100, enabled: true },
      post_liked: { coins: 1, cash: 10, enabled: true },
      post_shared: { coins: 3, cash: 50, enabled: true },
      post_commented: { coins: 2, cash: 25, enabled: true },
      post_viral: { coins: 100, cash: 5000, enabled: true },
    });

    this.rewardSettings.set('activity_rewards', {
      profile_completed: { coins: 50, cash: 500, enabled: true },
      first_post: { coins: 20, cash: 200, enabled: true },
      invite_friend: { coins: 100, cash: 1000, enabled: true },
      friend_joins: { coins: 200, cash: 2000, enabled: true },
      daily_login: { coins: 10, cash: 50, enabled: true },
      weekly_active: { coins: 100, cash: 1000, enabled: true },
    });

    this.rewardSettings.set('marketplace_rewards', {
      product_listed: { coins: 10, cash: 100, enabled: true },
      product_sold: { coins: 50, percentage: 5, enabled: true },
      product_reviewed: { coins: 5, cash: 50, enabled: true },
      store_featured: { coins: 500, cash: 5000, enabled: true },
    });

    this.rewardSettings.set('engagement_rewards', {
      video_watched: { coins: 2, cash: 20, enabled: true },
      news_read: { coins: 1, cash: 10, enabled: true },
      event_attended: { coins: 50, cash: 500, enabled: true },
      job_applied: { coins: 20, cash: 200, enabled: true },
      donation_made: { coins: 100, cash: 0, enabled: true },
    });

    // 🎯 MISSION REWARDS CONTROL
    this.rewardSettings.set('mission_rewards', {
      daily_post: { coins: 50, cash: 200, enabled: true },
      daily_likes: { coins: 30, cash: 100, enabled: true },
      daily_comments: { coins: 40, cash: 150, enabled: true },
      daily_invite: { coins: 100, cash: 500, enabled: true },
      daily_marketplace: { coins: 25, cash: 100, enabled: true },
      viral_post: { coins: 500, cash: 2000, enabled: true },
      marketplace_sale: { coins: 200, cash: 1000, enabled: true },
    });

    // 🔥 STREAK BONUSES CONTROL
    this.rewardSettings.set('streak_bonuses', {
      login_3_days: { coins: 50, cash: 200, enabled: true },
      login_7_days: { coins: 200, cash: 1000, enabled: true },
      login_14_days: { coins: 500, cash: 2500, enabled: true },
      login_30_days: { coins: 1500, cash: 10000, enabled: true },
      login_100_days: { coins: 5000, cash: 50000, enabled: true },
      post_7_days: { coins: 100, cash: 500, enabled: true },
      post_30_days: { coins: 1000, cash: 5000, enabled: true },
    });
  }

  setupFinancialPolicies() {
    this.financialPolicies.set('global_settings', {
      max_daily_earnings_per_user: 50000, // TZS 50,000 per day
      max_monthly_earnings_per_user: 500000, // TZS 500,000 per month
      coin_exchange_rate: 10, // 10 coins = TZS 1
      minimum_cashout: 1000, // TZS 1,000 minimum
      maximum_cashout_daily: 100000, // TZS 100,000 per day
      reward_multiplier_global: 1.0, // Global multiplier
      emergency_mode_multiplier: 0.1, // 10% rewards during emergency
    });

    this.financialPolicies.set('user_limits', {
      new_user_daily_limit: 5000, // TZS 5,000 for new users
      verified_user_daily_limit: 20000, // TZS 20,000 for verified users
      premium_user_daily_limit: 50000, // TZS 50,000 for premium users
      suspicious_user_limit: 1000, // TZS 1,000 for flagged users
    });

    this.financialPolicies.set('platform_economics', {
      total_daily_budget: 10000000, // TZS 10M daily budget
      reserve_fund_percentage: 20, // 20% kept in reserve
      marketing_budget_percentage: 30, // 30% for marketing rewards
      operational_budget_percentage: 50, // 50% for operations
    });
  }

  setupTransactionLimits() {
    this.transactionLimits.set('reward_transactions', {
      max_per_action: 10000, // TZS 10,000 max per single action
      max_per_hour: 20000, // TZS 20,000 per hour per user
      max_per_day: 50000, // TZS 50,000 per day per user
      cooldown_between_rewards: 60000, // 1 minute cooldown
    });

    this.transactionLimits.set('cashout_limits', {
      min_amount: 1000, // TZS 1,000 minimum
      max_amount_daily: 100000, // TZS 100,000 daily
      max_amount_monthly: 1000000, // TZS 1M monthly
      processing_fee_percentage: 2, // 2% processing fee
    });
  }

  setupEmergencyControls() {
    this.emergencyControls.set('financial_emergency', {
      freeze_all_rewards: false,
      freeze_cashouts: false,
      reduce_reward_rates: false,
      emergency_multiplier: 0.1,
      max_emergency_budget: 1000000, // TZS 1M emergency budget
    });
  }

  // 💰 ADMIN REWARD CONTROL METHODS
  async updateRewardRates(adminSessionId, category, actionType, newRates) {
    try {
      // Verify admin permissions
      const hasPermission = await this.verifyAdminPermission(adminSessionId, 'financial.rewards.modify');
      if (!hasPermission) {
        throw new Error('Insufficient permissions to modify reward rates');
      }

      // Get current settings
      const currentSettings = this.rewardSettings.get(category);
      if (!currentSettings || !currentSettings[actionType]) {
        throw new Error('Invalid reward category or action type');
      }

      // Validate new rates
      this.validateRewardRates(newRates);

      // Update rates
      const oldRates = { ...currentSettings[actionType] };
      currentSettings[actionType] = { ...currentSettings[actionType], ...newRates };
      this.rewardSettings.set(category, currentSettings);

      // Log the change
      await this.logFinancialChange(adminSessionId, 'reward_rates_updated', {
        category,
        actionType,
        oldRates,
        newRates,
        timestamp: Date.now(),
      });

      // Audit compliance
      complianceAuditService.logAuditEvent('financial_policy_change', {
        adminId: adminSessionId,
        changeType: 'reward_rates',
        category,
        actionType,
        oldValue: oldRates,
        newValue: newRates,
      });

      return {
        success: true,
        message: `Reward rates updated for ${category}.${actionType}`,
        oldRates,
        newRates,
      };

    } catch (error) {
      SecurityLogger.logSecurityEvent('FINANCIAL_CONTROL_ERROR', {
        adminSessionId,
        action: 'update_reward_rates',
        error: error.message,
      });
      throw error;
    }
  }

  async toggleRewardCategory(adminSessionId, category, enabled) {
    try {
      const hasPermission = await this.verifyAdminPermission(adminSessionId, 'financial.rewards.toggle');
      if (!hasPermission) {
        throw new Error('Insufficient permissions to toggle rewards');
      }

      const settings = this.rewardSettings.get(category);
      if (!settings) {
        throw new Error('Invalid reward category');
      }

      // Toggle all actions in category
      Object.keys(settings).forEach(actionType => {
        settings[actionType].enabled = enabled;
      });

      this.rewardSettings.set(category, settings);

      await this.logFinancialChange(adminSessionId, 'reward_category_toggled', {
        category,
        enabled,
        timestamp: Date.now(),
      });

      return {
        success: true,
        message: `Reward category ${category} ${enabled ? 'enabled' : 'disabled'}`,
        category,
        enabled,
      };

    } catch (error) {
      throw error;
    }
  }

  async setGlobalRewardMultiplier(adminSessionId, multiplier) {
    try {
      const hasPermission = await this.verifyAdminPermission(adminSessionId, 'financial.global.modify');
      if (!hasPermission) {
        throw new Error('Insufficient permissions to modify global settings');
      }

      if (multiplier < 0 || multiplier > 10) {
        throw new Error('Multiplier must be between 0 and 10');
      }

      const globalSettings = this.financialPolicies.get('global_settings');
      const oldMultiplier = globalSettings.reward_multiplier_global;
      globalSettings.reward_multiplier_global = multiplier;

      await this.logFinancialChange(adminSessionId, 'global_multiplier_changed', {
        oldMultiplier,
        newMultiplier: multiplier,
        timestamp: Date.now(),
      });

      return {
        success: true,
        message: `Global reward multiplier set to ${multiplier}x`,
        oldMultiplier,
        newMultiplier: multiplier,
      };

    } catch (error) {
      throw error;
    }
  }

  async setUserDailyLimit(adminSessionId, userId, newLimit) {
    try {
      const hasPermission = await this.verifyAdminPermission(adminSessionId, 'financial.user_limits.modify');
      if (!hasPermission) {
        throw new Error('Insufficient permissions to modify user limits');
      }

      if (newLimit < 0 || newLimit > 1000000) {
        throw new Error('Daily limit must be between 0 and TZS 1,000,000');
      }

      // Store user-specific limit
      await this.setUserSpecificLimit(userId, 'daily_limit', newLimit);

      await this.logFinancialChange(adminSessionId, 'user_limit_changed', {
        userId,
        limitType: 'daily_limit',
        newLimit,
        timestamp: Date.now(),
      });

      return {
        success: true,
        message: `Daily limit for user ${userId} set to TZS ${newLimit.toLocaleString()}`,
        userId,
        newLimit,
      };

    } catch (error) {
      throw error;
    }
  }

  // 💸 FINANCIAL TRANSACTION CONTROL
  async processRewardTransaction(userId, actionType, metadata = {}) {
    try {
      // Check if rewards are enabled
      if (!this.areRewardsEnabled(actionType)) {
        return { success: false, reason: 'Rewards disabled for this action' };
      }

      // Check user limits
      const limitCheck = await this.checkUserLimits(userId, actionType);
      if (!limitCheck.allowed) {
        return { success: false, reason: limitCheck.reason };
      }

      // Calculate reward amount
      const rewardAmount = await this.calculateRewardAmount(userId, actionType, metadata);
      
      if (rewardAmount.coins === 0 && rewardAmount.cash === 0) {
        return { success: false, reason: 'No reward calculated' };
      }

      // Check platform budget
      const budgetCheck = await this.checkPlatformBudget(rewardAmount.cash);
      if (!budgetCheck.allowed) {
        return { success: false, reason: 'Platform budget exceeded' };
      }

      // Process the reward
      const transactionResult = await this.executeRewardTransaction(userId, actionType, rewardAmount, metadata);

      // Update user limits tracking
      await this.updateUserLimitsTracking(userId, rewardAmount.cash);

      // Update platform budget tracking
      await this.updatePlatformBudgetTracking(rewardAmount.cash);

      return {
        success: true,
        reward: rewardAmount,
        transactionId: transactionResult.transactionId,
        message: `Reward processed: TZS ${rewardAmount.cash} + ${rewardAmount.coins} coins`,
      };

    } catch (error) {
      SecurityLogger.logSecurityEvent('REWARD_TRANSACTION_ERROR', {
        userId,
        actionType,
        error: error.message,
      });
      return { success: false, reason: 'Transaction processing failed' };
    }
  }

  async bulkAdjustUserBalances(adminSessionId, adjustments) {
    try {
      const hasPermission = await this.verifyAdminPermission(adminSessionId, 'financial.bulk_adjust');
      if (!hasPermission) {
        throw new Error('Insufficient permissions for bulk adjustments');
      }

      const results = [];

      for (const adjustment of adjustments) {
        const { userId, amount, reason, type } = adjustment;
        
        try {
          const result = await this.adjustUserBalance(adminSessionId, userId, amount, reason, type);
          results.push({ userId, success: true, result });
        } catch (error) {
          results.push({ userId, success: false, error: error.message });
        }
      }

      await this.logFinancialChange(adminSessionId, 'bulk_balance_adjustment', {
        adjustmentCount: adjustments.length,
        successCount: results.filter(r => r.success).length,
        failureCount: results.filter(r => !r.success).length,
        timestamp: Date.now(),
      });

      return {
        success: true,
        results,
        summary: {
          total: adjustments.length,
          successful: results.filter(r => r.success).length,
          failed: results.filter(r => !r.success).length,
        },
      };

    } catch (error) {
      throw error;
    }
  }

  async adjustUserBalance(adminSessionId, userId, amount, reason, type = 'adjustment') {
    try {
      const hasPermission = await this.verifyAdminPermission(adminSessionId, 'financial.user_balance.modify');
      if (!hasPermission) {
        throw new Error('Insufficient permissions to adjust user balance');
      }

      // Validate amount
      if (Math.abs(amount) > 1000000) {
        throw new Error('Adjustment amount cannot exceed TZS 1,000,000');
      }

      // Process the adjustment
      const transactionData = {
        amount: Math.abs(amount),
        type: amount > 0 ? 'admin_credit' : 'admin_debit',
        description: `Admin adjustment: ${reason}`,
        recipient: userId,
        adminId: adminSessionId,
      };

      const authData = {
        method: 'admin',
        adminSessionId,
        reason,
      };

      const result = await walletSecurityService.processSecureTransaction(
        'admin_system',
        transactionData,
        authData
      );

      await this.logFinancialChange(adminSessionId, 'user_balance_adjusted', {
        userId,
        amount,
        reason,
        type,
        transactionId: result.transactionId,
        timestamp: Date.now(),
      });

      return {
        success: true,
        transactionId: result.transactionId,
        newBalance: result.newBalance,
        adjustment: amount,
        reason,
      };

    } catch (error) {
      throw error;
    }
  }

  // 🚨 EMERGENCY FINANCIAL CONTROLS
  async activateFinancialEmergencyMode(adminSessionId, reason, restrictions = {}) {
    try {
      const hasPermission = await this.verifyAdminPermission(adminSessionId, 'financial.emergency.activate');
      if (!hasPermission) {
        throw new Error('Insufficient permissions to activate financial emergency mode');
      }

      const emergencySettings = this.emergencyControls.get('financial_emergency');
      
      // Apply restrictions
      emergencySettings.freeze_all_rewards = restrictions.freezeRewards || false;
      emergencySettings.freeze_cashouts = restrictions.freezeCashouts || false;
      emergencySettings.reduce_reward_rates = restrictions.reduceRates || false;
      emergencySettings.emergency_multiplier = restrictions.multiplier || 0.1;
      emergencySettings.active = true;
      emergencySettings.activatedBy = adminSessionId;
      emergencySettings.activatedAt = Date.now();
      emergencySettings.reason = reason;

      await this.logFinancialChange(adminSessionId, 'financial_emergency_activated', {
        reason,
        restrictions,
        timestamp: Date.now(),
      });

      // Notify all financial services
      await this.notifyFinancialEmergency(emergencySettings);

      return {
        success: true,
        message: 'Financial emergency mode activated',
        restrictions: emergencySettings,
      };

    } catch (error) {
      throw error;
    }
  }

  async deactivateFinancialEmergencyMode(adminSessionId) {
    try {
      const hasPermission = await this.verifyAdminPermission(adminSessionId, 'financial.emergency.deactivate');
      if (!hasPermission) {
        throw new Error('Insufficient permissions to deactivate financial emergency mode');
      }

      const emergencySettings = this.emergencyControls.get('financial_emergency');
      emergencySettings.active = false;
      emergencySettings.deactivatedBy = adminSessionId;
      emergencySettings.deactivatedAt = Date.now();

      await this.logFinancialChange(adminSessionId, 'financial_emergency_deactivated', {
        duration: Date.now() - emergencySettings.activatedAt,
        timestamp: Date.now(),
      });

      return {
        success: true,
        message: 'Financial emergency mode deactivated',
      };

    } catch (error) {
      throw error;
    }
  }

  // 📊 FINANCIAL REPORTING & ANALYTICS
  async getFinancialDashboard(adminSessionId) {
    try {
      const hasPermission = await this.verifyAdminPermission(adminSessionId, 'financial.dashboard.view');
      if (!hasPermission) {
        throw new Error('Insufficient permissions to view financial dashboard');
      }

      const dashboard = {
        dailyStats: await this.getDailyFinancialStats(),
        rewardSettings: this.getCurrentRewardSettings(),
        platformBudget: await this.getPlatformBudgetStatus(),
        userLimits: await this.getUserLimitsOverview(),
        emergencyStatus: this.getEmergencyStatus(),
        recentTransactions: await this.getRecentFinancialTransactions(50),
        topEarners: await this.getTopEarners(10),
        suspiciousActivity: await this.getSuspiciousFinancialActivity(),
      };

      return dashboard;

    } catch (error) {
      throw error;
    }
  }

  // Utility methods
  validateRewardRates(rates) {
    if (rates.coins && (rates.coins < 0 || rates.coins > 10000)) {
      throw new Error('Coin rewards must be between 0 and 10,000');
    }
    if (rates.cash && (rates.cash < 0 || rates.cash > 100000)) {
      throw new Error('Cash rewards must be between 0 and TZS 100,000');
    }
    if (rates.percentage && (rates.percentage < 0 || rates.percentage > 50)) {
      throw new Error('Percentage rewards must be between 0% and 50%');
    }
  }

  async verifyAdminPermission(sessionId, permission) {
    const sessionInfo = adminSecurityService.getAdminSessionInfo(sessionId);
    if (!sessionInfo || !sessionInfo.active) {
      return false;
    }

    return sessionInfo.permissions.includes(permission) || 
           sessionInfo.permissions.includes('all_permissions');
  }

  async logFinancialChange(adminSessionId, changeType, details) {
    const logId = this.generateLogId();
    const logEntry = {
      id: logId,
      adminSessionId,
      changeType,
      details,
      timestamp: Date.now(),
    };

    this.auditTrail.set(logId, logEntry);

    SecurityLogger.logSecurityEvent('FINANCIAL_CHANGE', {
      logId,
      adminSessionId,
      changeType,
      details,
    });
  }

  generateLogId() {
    return `fin_log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Mock methods (implement with real data)
  areRewardsEnabled(actionType) { return true; }
  async checkUserLimits(userId, actionType) { return { allowed: true }; }
  async calculateRewardAmount(userId, actionType, metadata) { return { coins: 10, cash: 100 }; }
  async checkPlatformBudget(amount) { return { allowed: true }; }
  async executeRewardTransaction(userId, actionType, amount, metadata) { return { transactionId: 'tx_123' }; }
  async updateUserLimitsTracking(userId, amount) { /* Update tracking */ }
  async updatePlatformBudgetTracking(amount) { /* Update tracking */ }
  async setUserSpecificLimit(userId, limitType, amount) { /* Set limit */ }
  async notifyFinancialEmergency(settings) { /* Notify services */ }
  async getDailyFinancialStats() { return {}; }
  getCurrentRewardSettings() { return Object.fromEntries(this.rewardSettings); }
  async getPlatformBudgetStatus() { return {}; }
  async getUserLimitsOverview() { return {}; }
  getEmergencyStatus() { return this.emergencyControls.get('financial_emergency'); }
  async getRecentFinancialTransactions(limit) { return []; }
  async getTopEarners(limit) { return []; }
  async getSuspiciousFinancialActivity() { return []; }

  // Public API methods
  getRewardSettings() {
    return Object.fromEntries(this.rewardSettings);
  }

  getFinancialPolicies() {
    return Object.fromEntries(this.financialPolicies);
  }

  getAuditTrail(limit = 100) {
    return Array.from(this.auditTrail.values())
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }
}

// Create singleton instance
const adminFinancialControlService = new AdminFinancialControlService();

export default adminFinancialControlService;
