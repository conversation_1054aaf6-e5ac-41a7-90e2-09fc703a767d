// ProChat Advanced Security Test Suite
// Comprehensive testing for military-grade security features

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

// Import security services
import fraudDetectionService from '../services/fraudDetection';
import walletSecurityService from '../services/walletSecurity';
import threatIntelligenceService from '../services/threatIntelligence';
import complianceAuditService from '../services/complianceAudit';
import backupRecoveryService from '../services/backupRecovery';
import {
  ipWhitelistManager,
  deviceAuthManager,
  rbacManager,
  emergencyModeManager,
  SECURITY_LEVELS,
} from '../config/bankingSecurity';

describe('ProChat Security Test Suite', () => {
  beforeEach(() => {
    // Reset all security services before each test
    jest.clearAllMocks();
    localStorage.clear();
    sessionStorage.clear();
  });

  describe('IP Whitelisting & Network Security', () => {
    test('should allow access from whitelisted IP', async () => {
      const mockIP = '*************';
      jest.spyOn(ipWhitelistManager, 'getCurrentIP').mockResolvedValue(mockIP);
      
      const result = await ipWhitelistManager.checkIPAccess(SECURITY_LEVELS.BASIC);
      
      expect(result.allowed).toBe(true);
      expect(result.ip).toBe(mockIP);
    });

    test('should block access from non-whitelisted IP for admin operations', async () => {
      const mockIP = '*******'; // Suspicious IP
      jest.spyOn(ipWhitelistManager, 'getCurrentIP').mockResolvedValue(mockIP);
      
      const result = await ipWhitelistManager.checkIPAccess(SECURITY_LEVELS.ADMIN);
      
      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('Admin access requires whitelisted IP');
    });

    test('should allow financial operations from office network', async () => {
      const mockIP = '************'; // Office network IP
      jest.spyOn(ipWhitelistManager, 'getCurrentIP').mockResolvedValue(mockIP);
      
      const result = await ipWhitelistManager.checkIPAccess(SECURITY_LEVELS.FINANCIAL);
      
      expect(result.allowed).toBe(true);
    });
  });

  describe('Device Authorization System', () => {
    test('should generate unique device fingerprint', async () => {
      const fingerprint1 = await deviceAuthManager.generateDeviceFingerprint();
      const fingerprint2 = await deviceAuthManager.generateDeviceFingerprint();
      
      expect(fingerprint1).toBeDefined();
      expect(fingerprint2).toBeDefined();
      expect(fingerprint1.length).toBeGreaterThan(32);
      // Note: Fingerprints might be same in test environment due to same browser context
    });

    test('should authorize device for basic operations', async () => {
      const result = await deviceAuthManager.authorizeDevice(SECURITY_LEVELS.BASIC);
      
      expect(result.authorized).toBe(true);
      expect(result.deviceId).toBeDefined();
      expect(result.deviceInfo).toBeDefined();
    });

    test('should require approval for financial operations on new device', async () => {
      const result = await deviceAuthManager.authorizeDevice(SECURITY_LEVELS.FINANCIAL);
      
      if (!result.authorized) {
        expect(result.requiresApproval).toBe(true);
        expect(result.reason).toContain('Financial operations require trusted device');
      }
    });

    test('should trust device after manual approval', async () => {
      const deviceId = 'test_device_123';
      const result = deviceAuthManager.trustDevice(deviceId);
      
      expect(result).toBe(true);
    });
  });

  describe('Role-Based Access Control (RBAC)', () => {
    test('should assign role to user', () => {
      const userId = 'user_123';
      const roleName = 'user';
      
      rbacManager.assignRole(userId, roleName);
      const userRole = rbacManager.getUserRole(userId);
      
      expect(userRole).toBeDefined();
      expect(userRole.name).toBe('Regular User');
    });

    test('should check user permissions correctly', () => {
      const userId = 'user_123';
      rbacManager.assignRole(userId, 'user');
      
      const hasWalletView = rbacManager.hasPermission(userId, 'wallet.view');
      const hasAdminAccess = rbacManager.hasPermission(userId, 'admin.users.view');
      
      expect(hasWalletView).toBe(true);
      expect(hasAdminAccess).toBe(false);
    });

    test('should get user permissions list', () => {
      const userId = 'admin_123';
      rbacManager.assignRole(userId, 'admin');
      
      const permissions = rbacManager.getUserPermissions(userId);
      
      expect(permissions).toContain('admin.users.view');
      expect(permissions).toContain('wallet.view');
    });
  });

  describe('Emergency Mode System', () => {
    test('should activate emergency mode', () => {
      const reason = 'Security test';
      const allowedOps = ['view_only'];
      
      emergencyModeManager.activateEmergencyMode(reason, allowedOps);
      const status = emergencyModeManager.getEmergencyStatus();
      
      expect(status.active).toBe(true);
      expect(status.reason).toBe(reason);
      expect(status.allowedOperations).toEqual(allowedOps);
    });

    test('should check operation permissions in emergency mode', () => {
      emergencyModeManager.activateEmergencyMode('Test', ['view_only']);
      
      const viewAllowed = emergencyModeManager.isOperationAllowed('view_only');
      const walletAllowed = emergencyModeManager.isOperationAllowed('wallet_transaction');
      
      expect(viewAllowed).toBe(true);
      expect(walletAllowed).toBe(false);
    });

    test('should deactivate emergency mode', () => {
      emergencyModeManager.activateEmergencyMode('Test', []);
      emergencyModeManager.deactivateEmergencyMode();
      
      const status = emergencyModeManager.getEmergencyStatus();
      
      expect(status.active).toBe(false);
    });
  });

  describe('Fraud Detection System', () => {
    test('should track user actions', () => {
      const userId = 'user_123';
      const action = 'like';
      const metadata = { postId: 'post_456' };
      
      fraudDetectionService.trackUserAction(userId, action, metadata);
      
      const riskScore = fraudDetectionService.getUserRiskScore(userId);
      expect(riskScore).toBeGreaterThanOrEqual(0);
    });

    test('should detect rapid liking pattern', () => {
      const userId = 'user_123';
      
      // Simulate rapid liking
      for (let i = 0; i < 15; i++) {
        fraudDetectionService.trackUserAction(userId, 'like', { postId: `post_${i}` });
      }
      
      const riskLevel = fraudDetectionService.getUserRiskLevel(userId);
      expect(['MEDIUM', 'HIGH', 'CRITICAL']).toContain(riskLevel);
    });

    test('should block user after suspicious activity', () => {
      const userId = 'user_123';
      
      fraudDetectionService.blockUser(userId, 'Test block');
      const isBlocked = fraudDetectionService.isUserBlocked(userId);
      
      expect(isBlocked).toBe(true);
    });

    test('should get system statistics', () => {
      const stats = fraudDetectionService.getSystemStats();
      
      expect(stats).toHaveProperty('totalUsers');
      expect(stats).toHaveProperty('blockedUsers');
      expect(stats).toHaveProperty('suspiciousActivities');
      expect(stats).toHaveProperty('activeRules');
    });
  });

  describe('Wallet Security System', () => {
    test('should validate transaction with all security checks', async () => {
      const userId = 'user_123';
      const transactionData = {
        amount: 50000, // TZS 50,000
        recipient: 'recipient_456',
        type: 'transfer',
      };
      
      rbacManager.assignRole(userId, 'user');
      
      const validation = await walletSecurityService.validateTransaction(userId, transactionData);
      
      expect(validation).toHaveProperty('allowed');
      expect(validation).toHaveProperty('securityChecks');
      expect(validation.securityChecks).toContain('PERMISSION_VERIFIED');
    });

    test('should check transaction limits', async () => {
      const userId = 'user_123';
      const transactionData = {
        amount: 2000000, // TZS 2M - exceeds basic user limit
        recipient: 'recipient_456',
        type: 'transfer',
      };
      
      rbacManager.assignRole(userId, 'user'); // Basic user
      
      const validation = await walletSecurityService.validateTransaction(userId, transactionData);
      
      if (!validation.allowed) {
        expect(validation.errors.some(error => 
          error.includes('single transaction limit')
        )).toBe(true);
      }
    });

    test('should process secure transaction', async () => {
      const userId = 'user_123';
      const transactionData = {
        amount: 10000,
        recipient: 'recipient_456',
        type: 'transfer',
      };
      const authData = {
        method: 'pin',
        pin: '1234',
        deviceId: 'device_123',
        ipAddress: '*************',
      };
      
      // Mock PIN validation
      localStorage.setItem(`prochat_pin_${userId}`, '1234');
      
      const result = await walletSecurityService.processSecureTransaction(
        userId, 
        transactionData, 
        authData
      );
      
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('transactionId');
    });
  });

  describe('Threat Intelligence System', () => {
    test('should analyze request for threats', () => {
      const requestData = {
        url: '/api/users',
        method: 'GET',
        headers: {},
        body: null,
        ip: '*************',
        userAgent: 'Mozilla/5.0...',
        userId: 'user_123',
      };
      
      const analysis = threatIntelligenceService.analyzeRequest(requestData);
      
      expect(analysis).toHaveProperty('safe');
      expect(analysis).toHaveProperty('threats');
      expect(analysis).toHaveProperty('riskScore');
    });

    test('should detect SQL injection attempt', () => {
      const requestData = {
        url: "/api/users?id=1' OR '1'='1",
        method: 'GET',
        headers: {},
        body: null,
        ip: '*******',
        userAgent: 'curl/7.68.0',
        userId: null,
      };
      
      const analysis = threatIntelligenceService.analyzeRequest(requestData);
      
      expect(analysis.safe).toBe(false);
      expect(analysis.threats.some(threat => threat.type === 'sql_injection')).toBe(true);
    });

    test('should detect XSS attempt', () => {
      const requestData = {
        url: '/api/posts',
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content: '<script>alert("xss")</script>' }),
        ip: '*******',
        userAgent: 'Mozilla/5.0...',
        userId: 'user_123',
      };
      
      const analysis = threatIntelligenceService.analyzeRequest(requestData);
      
      expect(analysis.safe).toBe(false);
      expect(analysis.threats.some(threat => threat.type === 'xss_attack')).toBe(true);
    });

    test('should get threat statistics', () => {
      const stats = threatIntelligenceService.getThreatStatistics();
      
      expect(stats).toHaveProperty('totalThreats');
      expect(stats).toHaveProperty('activeThreats');
      expect(stats).toHaveProperty('criticalThreats');
      expect(stats).toHaveProperty('threatTypes');
    });
  });

  describe('Compliance Audit System', () => {
    test('should log audit event', () => {
      const eventType = 'user_login';
      const details = {
        userId: 'user_123',
        sessionId: 'session_456',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0...',
      };
      
      const auditId = complianceAuditService.logAuditEvent(eventType, details);
      
      expect(auditId).toBeDefined();
      expect(auditId).toMatch(/^audit_/);
    });

    test('should get compliance status', () => {
      const status = complianceAuditService.getComplianceStatus();
      
      expect(status).toHaveProperty('total_frameworks');
      expect(status).toHaveProperty('overall_score');
      expect(status).toHaveProperty('critical_violations');
    });

    test('should get audit trail for user', () => {
      const userId = 'user_123';
      
      // Log some events
      complianceAuditService.logAuditEvent('user_login', { userId });
      complianceAuditService.logAuditEvent('password_change', { userId });
      
      const auditTrail = complianceAuditService.getAuditTrail(userId);
      
      expect(Array.isArray(auditTrail)).toBe(true);
      expect(auditTrail.length).toBeGreaterThan(0);
    });

    test('should export audit data', () => {
      const startDate = new Date(Date.now() - 24 * 60 * 60 * 1000); // Yesterday
      const endDate = new Date(); // Today
      
      const auditData = complianceAuditService.exportAuditData(startDate, endDate);
      
      expect(Array.isArray(auditData)).toBe(true);
    });
  });

  describe('Backup & Recovery System', () => {
    test('should get backup status', () => {
      const status = backupRecoveryService.getBackupStatus();
      
      expect(status).toHaveProperty('totalBackups');
      expect(status).toHaveProperty('recentBackups');
      expect(status).toHaveProperty('successfulBackups');
      expect(status).toHaveProperty('storageLocations');
    });

    test('should get recovery capabilities', () => {
      const capabilities = backupRecoveryService.getRecoveryCapabilities();
      
      expect(capabilities).toHaveProperty('availablePlans');
      expect(capabilities).toHaveProperty('rtoTargets');
      expect(capabilities).toHaveProperty('rpoTargets');
      expect(Array.isArray(capabilities.availablePlans)).toBe(true);
    });

    test('should test recovery procedure', async () => {
      const recoveryType = 'data_corruption';
      
      const testResult = await backupRecoveryService.testRecoveryProcedure(recoveryType);
      
      expect(testResult).toHaveProperty('recoveryType');
      expect(testResult).toHaveProperty('procedures');
      expect(testResult).toHaveProperty('testResult');
      expect(testResult.testResult).toBe('passed');
    });

    test('should get backup history', () => {
      const history = backupRecoveryService.getBackupHistory(10);
      
      expect(Array.isArray(history)).toBe(true);
    });
  });

  describe('Integration Tests', () => {
    test('should handle security violation workflow', async () => {
      const userId = 'user_123';
      
      // 1. User performs suspicious action
      fraudDetectionService.trackUserAction(userId, 'rapid_login', {
        ip: '*******',
        deviceId: 'suspicious_device',
      });
      
      // 2. Check if user is flagged
      const riskLevel = fraudDetectionService.getUserRiskLevel(userId);
      
      // 3. If high risk, emergency mode might be triggered
      if (riskLevel === 'CRITICAL') {
        emergencyModeManager.activateEmergencyMode('Critical fraud detected', ['view_only']);
        
        const emergencyStatus = emergencyModeManager.getEmergencyStatus();
        expect(emergencyStatus.active).toBe(true);
      }
      
      // 4. Audit the security event
      complianceAuditService.logAuditEvent('security_violation', {
        userId,
        riskLevel,
        action: 'rapid_login',
      });
      
      const auditTrail = complianceAuditService.getAuditTrail(userId);
      expect(auditTrail.length).toBeGreaterThan(0);
    });

    test('should handle wallet transaction security workflow', async () => {
      const userId = 'user_123';
      const transactionData = {
        amount: 100000,
        recipient: 'recipient_456',
        type: 'transfer',
      };
      
      // 1. Assign user role
      rbacManager.assignRole(userId, 'verified');
      
      // 2. Validate transaction
      const validation = await walletSecurityService.validateTransaction(userId, transactionData);
      
      // 3. If validation passes, process transaction
      if (validation.allowed) {
        const authData = {
          method: 'pin',
          pin: '1234',
          deviceId: 'device_123',
          ipAddress: '*************',
        };
        
        localStorage.setItem(`prochat_pin_${userId}`, '1234');
        
        const result = await walletSecurityService.processSecureTransaction(
          userId, 
          transactionData, 
          authData
        );
        
        expect(result.success).toBe(true);
      }
      
      // 4. Audit the transaction
      complianceAuditService.logAuditEvent('financial_transaction', {
        userId,
        amount: transactionData.amount,
        recipient: transactionData.recipient,
      });
    });
  });

  describe('Performance Tests', () => {
    test('should handle multiple concurrent security checks', async () => {
      const startTime = Date.now();
      
      const promises = [];
      
      // Simulate 100 concurrent security checks
      for (let i = 0; i < 100; i++) {
        promises.push(
          ipWhitelistManager.checkIPAccess(SECURITY_LEVELS.BASIC),
          deviceAuthManager.authorizeDevice(SECURITY_LEVELS.BASIC),
          fraudDetectionService.getUserRiskScore(`user_${i}`)
        );
      }
      
      await Promise.all(promises);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within 5 seconds
      expect(duration).toBeLessThan(5000);
    });

    test('should handle large audit log efficiently', () => {
      const startTime = Date.now();
      
      // Generate 1000 audit events
      for (let i = 0; i < 1000; i++) {
        complianceAuditService.logAuditEvent('test_event', {
          userId: `user_${i % 10}`,
          eventId: i,
        });
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within 2 seconds
      expect(duration).toBeLessThan(2000);
    });
  });
});

// Test utilities
export const SecurityTestUtils = {
  createMockUser: (userId = 'test_user') => ({
    id: userId,
    email: `${userId}@test.com`,
    role: 'user',
  }),
  
  createMockTransaction: (amount = 10000) => ({
    amount,
    recipient: 'test_recipient',
    type: 'transfer',
    timestamp: new Date().toISOString(),
  }),
  
  createMockThreat: (type = 'sql_injection') => ({
    type,
    severity: 'HIGH',
    description: 'Test threat',
    evidence: 'test evidence',
  }),
  
  simulateSecurityBreach: () => {
    emergencyModeManager.activateEmergencyMode('Simulated breach', ['view_only']);
  },
  
  cleanupSecurityState: () => {
    emergencyModeManager.deactivateEmergencyMode();
    localStorage.clear();
    sessionStorage.clear();
  },
};
