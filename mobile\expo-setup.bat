@echo off
echo ========================================
echo ProChat Expo Setup & Start
echo ========================================

echo.
echo Step 1: Checking environment...
echo Current directory: %CD%

echo.
echo Step 2: Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js not found! Please install Node.js first.
    pause
    exit /b 1
)

echo.
echo Step 3: Checking npm...
npm --version
if %errorlevel% neq 0 (
    echo ❌ npm not found!
    pause
    exit /b 1
)

echo.
echo Step 4: Installing Expo CLI globally...
npm install -g @expo/cli
if %errorlevel% neq 0 (
    echo ⚠️ Expo CLI installation failed, trying alternative...
    npm install -g expo-cli
)

echo.
echo Step 5: Checking Expo installation...
expo --version
if %errorlevel% neq 0 (
    echo ⚠️ Expo not found, using npx...
    set USE_NPX=1
) else (
    echo ✅ Expo CLI installed successfully!
    set USE_NPX=0
)

echo.
echo Step 6: Creating basic node_modules if missing...
if not exist "node_modules" (
    echo Creating node_modules directory...
    mkdir node_modules
    echo Basic structure created
)

echo.
echo Step 7: Starting Expo development server...
echo.
echo 🚀 ProChat Mobile App Starting...
echo 📱 Use Expo Go app on your phone to scan QR code
echo 🌐 Or press 'w' to open in web browser
echo.

if %USE_NPX%==1 (
    echo Using npx expo start...
    npx expo start --tunnel --clear
) else (
    echo Using expo start...
    expo start --tunnel --clear
)

echo.
echo ========================================
echo Expo setup completed!
echo ========================================
pause
