@echo off
echo ========================================
echo MySQL Password Reset for ProChat
echo ========================================

echo.
echo Step 1: Stopping MySQL service...
net stop mysql80 2>nul
net stop mysql 2>nul
taskkill /f /im mysqld.exe 2>nul

echo.
echo Step 2: Creating password reset file...
echo ALTER USER 'root'@'localhost' IDENTIFIED BY ''; > mysql-init.sql
echo FLUSH PRIVILEGES; >> mysql-init.sql

echo.
echo Step 3: Starting MySQL in safe mode...
start /b mysqld --init-file=mysql-init.sql --console

echo.
echo Step 4: Waiting for MySQL to start...
timeout /t 15 /nobreak >nul

echo.
echo Step 5: Testing connection with empty password...
mysql -u root -e "SELECT 'Connection successful!' as status;"
if %errorlevel% equ 0 (
    echo ✅ MySQL connection successful with empty password!
    
    echo.
    echo Step 6: Creating ProChat database...
    mysql -u root -e "CREATE DATABASE IF NOT EXISTS prochat_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    
    echo.
    echo Step 7: Setting new password...
    mysql -u root -e "ALTER USER 'root'@'localhost' IDENTIFIED BY 'Ram$0101';"
    mysql -u root -e "FLUSH PRIVILEGES;"
    
    echo.
    echo Step 8: Testing new password...
    mysql -u root -p"Ram$0101" -e "SELECT 'New password works!' as status;"
    
    if %errorlevel% equ 0 (
        echo ✅ Password successfully changed to Ram$0101
    ) else (
        echo ⚠️ Password change may have failed, but database is accessible
    )
) else (
    echo ❌ Could not connect to MySQL
)

echo.
echo Step 9: Stopping safe mode MySQL...
taskkill /f /im mysqld.exe 2>nul

echo.
echo Step 10: Starting MySQL service normally...
net start mysql80

echo.
echo Step 11: Final test...
mysql -u root -p"Ram$0101" -e "USE prochat_db; SELECT 'ProChat database ready!' as status;"

echo.
echo Cleaning up...
del mysql-init.sql 2>nul

echo.
echo ========================================
echo MySQL Reset Complete!
echo Database: prochat_db
echo Username: root
echo Password: Ram$0101 (or empty if reset failed)
echo ========================================
pause
