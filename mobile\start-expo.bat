@echo off
echo ========================================
echo ProChat Mobile App - Expo Start
echo ========================================

echo.
echo Current directory: %CD%
echo.

echo Checking if we're in mobile directory...
if not exist "App.js" (
    echo Error: App.js not found. Make sure you're in the mobile directory.
    pause
    exit /b 1
)

echo ✅ Found App.js - we're in the right directory

echo.
echo Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js not found!
    pause
    exit /b 1
)

echo.
echo Checking npm...
npm --version
if %errorlevel% neq 0 (
    echo ❌ npm not found!
    pause
    exit /b 1
)

echo.
echo Installing dependencies...
npm install --force --legacy-peer-deps

echo.
echo Starting Expo development server...
npx expo start --clear

pause
