// ProChat Me Tab Component
// User profile and settings with ProPay integration

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Avatar,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Divider,
  Chip,
  Grid,
  Paper,
  Switch,
  Badge,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  AccountBalanceWallet,
  Settings,
  Security,
  Notifications,
  Help,
  Info,
  ExitToApp,
  Edit,
  Verified,
  Star,
  Group,
  Work,
  School,
  Event,
  Receipt,
  CreditCard,
  Savings,
  TrendingUp,
  Shield,
  Language,
  DarkMode,
  VolumeUp,
  LocationOn,
  ChevronRight,
  QrCode,
  Send,
  History,
  CardGiftcard,
} from '@mui/icons-material';
import ProPayDashboard from '../ProPay/ProPayDashboard';

const MeTab = () => {
  const [user, setUser] = useState(null);
  const [showProPay, setShowProPay] = useState(false);
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = () => {
    // Mock user data
    const mockUser = {
      id: 'user_123',
      name: 'John Mwangi',
      username: '@johnmwangi',
      email: '<EMAIL>',
      phone: '+************',
      avatar: '/avatars/john.jpg',
      coverImage: '/covers/john-cover.jpg',
      isVerified: true,
      bio: 'Software Developer & Tech Enthusiast | Building the future with ProChat 🚀',
      location: 'Dar es Salaam, Tanzania',
      joinDate: '2023-01-15',
      stats: {
        posts: 156,
        followers: 2340,
        following: 890,
        likes: 12500,
      },
      wallet: {
        balance: 2500000, // TZS 2.5M
        accountNumber: 'PP-2024-001234',
        isVerified: true,
      },
      achievements: [
        { id: 1, title: 'Early Adopter', icon: '🌟', description: 'Joined ProChat in the first month' },
        { id: 2, title: 'Content Creator', icon: '📝', description: 'Posted 100+ quality posts' },
        { id: 3, title: 'Community Builder', icon: '👥', description: 'Helped 50+ users' },
        { id: 4, title: 'ProPay Pioneer', icon: '💰', description: 'First 1000 ProPay users' },
      ],
    };

    setUser(mockUser);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const menuSections = [
    {
      title: 'ProPay Wallet',
      items: [
        {
          icon: <AccountBalanceWallet />,
          title: 'ProPay Dashboard',
          subtitle: `Salio: ${formatCurrency(user?.wallet?.balance || 0)}`,
          action: () => setShowProPay(true),
          badge: user?.wallet?.isVerified ? 'Imethibitishwa' : null,
          color: 'primary.main',
        },
        {
          icon: <Send />,
          title: 'Tuma Pesa',
          subtitle: 'Tuma pesa kwa haraka na usalama',
          action: () => console.log('Send money'),
          color: 'success.main',
        },
        {
          icon: <QrCode />,
          title: 'Pokea Pesa',
          subtitle: 'Pokea pesa kwa QR code',
          action: () => console.log('Receive money'),
          color: 'info.main',
        },
        {
          icon: <History />,
          title: 'Historia ya Malipo',
          subtitle: 'Ona miamala yako yote',
          action: () => console.log('Payment history'),
          color: 'text.secondary',
        },
      ],
    },
    {
      title: 'Huduma za ProPay',
      items: [
        {
          icon: <CreditCard />,
          title: 'Kadi ya ProPay',
          subtitle: 'Omba kadi yako ya ProPay',
          action: () => console.log('ProPay card'),
          badge: 'Mpya',
          color: 'primary.main',
        },
        {
          icon: <Savings />,
          title: 'Akiba ya ProPay',
          subtitle: 'Okoa pesa na upate riba',
          action: () => console.log('ProPay savings'),
          color: 'success.main',
        },
        {
          icon: <TrendingUp />,
          title: 'Uwekezaji wa ProPay',
          subtitle: 'Wekeza na upate mapato',
          action: () => console.log('ProPay investment'),
          color: 'warning.main',
        },
        {
          icon: <Shield />,
          title: 'Bima ya ProPay',
          subtitle: 'Linda pesa zako',
          action: () => console.log('ProPay insurance'),
          color: 'error.main',
        },
      ],
    },
    {
      title: 'Akaunti',
      items: [
        {
          icon: <Edit />,
          title: 'Hariri Wasifu',
          subtitle: 'Badilisha taarifa za kibinafsi',
          action: () => console.log('Edit profile'),
          color: 'text.secondary',
        },
        {
          icon: <Security />,
          title: 'Usalama na Faragha',
          subtitle: 'Dhibiti mipangilio ya usalama',
          action: () => console.log('Security settings'),
          color: 'text.secondary',
        },
        {
          icon: <Verified />,
          title: 'Uthibitisho wa Akaunti',
          subtitle: 'Thibitisha akaunti yako',
          action: () => console.log('Account verification'),
          badge: user?.isVerified ? 'Imethibitishwa' : 'Haijathibitishwa',
          color: 'text.secondary',
        },
      ],
    },
    {
      title: 'Mipangilio',
      items: [
        {
          icon: <Notifications />,
          title: 'Arifa',
          subtitle: 'Dhibiti arifa za app',
          action: () => setNotifications(!notifications),
          toggle: true,
          toggleValue: notifications,
          color: 'text.secondary',
        },
        {
          icon: <DarkMode />,
          title: 'Hali ya Giza',
          subtitle: 'Badilisha muonekano wa app',
          action: () => setDarkMode(!darkMode),
          toggle: true,
          toggleValue: darkMode,
          color: 'text.secondary',
        },
        {
          icon: <VolumeUp />,
          title: 'Sauti',
          subtitle: 'Washa au zima sauti',
          action: () => setSoundEnabled(!soundEnabled),
          toggle: true,
          toggleValue: soundEnabled,
          color: 'text.secondary',
        },
        {
          icon: <Language />,
          title: 'Lugha',
          subtitle: 'Chagua lugha ya app',
          action: () => console.log('Language settings'),
          color: 'text.secondary',
        },
      ],
    },
    {
      title: 'Msaada',
      items: [
        {
          icon: <Help />,
          title: 'Kituo cha Msaada',
          subtitle: 'Pata msaada na majibu',
          action: () => console.log('Help center'),
          color: 'text.secondary',
        },
        {
          icon: <Info />,
          title: 'Kuhusu ProChat',
          subtitle: 'Taarifa za app na toleo',
          action: () => console.log('About ProChat'),
          color: 'text.secondary',
        },
        {
          icon: <Receipt />,
          title: 'Masharti ya Matumizi',
          subtitle: 'Soma masharti yetu',
          action: () => console.log('Terms of service'),
          color: 'text.secondary',
        },
      ],
    },
  ];

  const renderUserProfile = () => (
    <Card sx={{ mb: 3 }}>
      <Box
        sx={{
          height: 120,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          position: 'relative',
        }}
      >
        <Avatar
          src={user?.avatar}
          sx={{
            width: 80,
            height: 80,
            position: 'absolute',
            bottom: -40,
            left: 20,
            border: 4,
            borderColor: 'background.paper',
          }}
        />
      </Box>
      
      <CardContent sx={{ pt: 6 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Typography variant="h5" sx={{ fontWeight: 'bold', mr: 1 }}>
            {user?.name}
          </Typography>
          {user?.isVerified && <Verified sx={{ color: 'primary.main' }} />}
        </Box>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          {user?.username}
        </Typography>
        
        <Typography variant="body1" sx={{ mb: 2 }}>
          {user?.bio}
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <LocationOn sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
          <Typography variant="body2" color="text.secondary">
            {user?.location}
          </Typography>
        </Box>

        {/* Stats */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {user?.stats?.posts}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Machapisho
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {user?.stats?.followers?.toLocaleString()}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Wafuasi
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {user?.stats?.following}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Anawafuata
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {user?.stats?.likes?.toLocaleString()}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Likes
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Achievements */}
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
          Mafanikio
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {user?.achievements?.map((achievement) => (
            <Chip
              key={achievement.id}
              label={`${achievement.icon} ${achievement.title}`}
              size="small"
              variant="outlined"
              title={achievement.description}
            />
          ))}
        </Box>
      </CardContent>
    </Card>
  );

  const renderMenuSection = (section) => (
    <Paper key={section.title} sx={{ mb: 2 }}>
      <Typography variant="h6" sx={{ p: 2, fontWeight: 'bold' }}>
        {section.title}
      </Typography>
      <List>
        {section.items.map((item, index) => (
          <React.Fragment key={index}>
            <ListItem button onClick={item.action}>
              <ListItemIcon>
                <Box sx={{ color: item.color }}>
                  {item.icon}
                </Box>
              </ListItemIcon>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="subtitle1">
                      {item.title}
                    </Typography>
                    {item.badge && (
                      <Chip
                        label={item.badge}
                        size="small"
                        color={item.badge === 'Imethibitishwa' ? 'success' : 'primary'}
                      />
                    )}
                  </Box>
                }
                secondary={item.subtitle}
              />
              <ListItemSecondaryAction>
                {item.toggle ? (
                  <Switch
                    checked={item.toggleValue}
                    onChange={item.action}
                    color="primary"
                  />
                ) : (
                  <IconButton edge="end">
                    <ChevronRight />
                  </IconButton>
                )}
              </ListItemSecondaryAction>
            </ListItem>
            {index < section.items.length - 1 && <Divider />}
          </React.Fragment>
        ))}
      </List>
    </Paper>
  );

  if (showProPay) {
    return (
      <Box>
        <Box sx={{ display: 'flex', alignItems: 'center', p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Button onClick={() => setShowProPay(false)} sx={{ mr: 2 }}>
            ← Rudi
          </Button>
          <Typography variant="h6">ProPay Dashboard</Typography>
        </Box>
        <ProPayDashboard />
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: 600, mx: 'auto', p: isMobile ? 1 : 3 }}>
      {/* User Profile */}
      {renderUserProfile()}

      {/* Menu Sections */}
      {menuSections.map(renderMenuSection)}

      {/* Logout Button */}
      <Paper sx={{ mb: 2 }}>
        <List>
          <ListItem button sx={{ color: 'error.main' }}>
            <ListItemIcon>
              <ExitToApp sx={{ color: 'error.main' }} />
            </ListItemIcon>
            <ListItemText primary="Toka" />
          </ListItem>
        </List>
      </Paper>

      {/* App Version */}
      <Box sx={{ textAlign: 'center', py: 2 }}>
        <Typography variant="caption" color="text.secondary">
          ProChat v2.1.0 • Made with ❤️ in Tanzania
        </Typography>
      </Box>
    </Box>
  );
};

export default MeTab;
