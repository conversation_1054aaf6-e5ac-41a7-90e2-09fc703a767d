// ProChat Military-Grade Banking Security Architecture
// Enterprise-level security for financial operations

import { SecurityLogger } from './security';
import { detectDeviceType } from './responsive';

// Security Levels Configuration
export const SECURITY_LEVELS = {
  PUBLIC: 0,      // Public content (posts, news)
  BASIC: 1,       // Basic user actions (likes, comments)
  FINANCIAL: 2,   // Wallet operations
  ADMIN: 3,       // Administrative functions
  CRITICAL: 4,    // Core system operations
};

// IP Whitelisting Configuration
export class IPWhitelistManager {
  constructor() {
    this.allowedIPs = new Set();
    this.allowedRanges = [];
    this.adminIPs = new Set();
    this.officeNetworks = new Set();
    
    this.loadWhitelistConfig();
  }

  loadWhitelistConfig() {
    // Office IP ranges (example)
    this.officeNetworks.add('***********/24');
    this.officeNetworks.add('10.0.0.0/8');
    
    // Admin IPs (example)
    this.adminIPs.add('***********');
    this.adminIPs.add('************');
    
    // VPN exit points
    this.allowedIPs.add('*************');
  }

  async getCurrentIP() {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch (error) {
      console.error('Failed to get IP:', error);
      return null;
    }
  }

  isIPInRange(ip, range) {
    // Simple CIDR check (production should use proper library)
    const [network, bits] = range.split('/');
    const mask = ~(2 ** (32 - parseInt(bits)) - 1);
    
    const ipInt = this.ipToInt(ip);
    const networkInt = this.ipToInt(network);
    
    return (ipInt & mask) === (networkInt & mask);
  }

  ipToInt(ip) {
    return ip.split('.').reduce((int, oct) => (int << 8) + parseInt(oct, 10), 0) >>> 0;
  }

  async checkIPAccess(securityLevel = SECURITY_LEVELS.BASIC) {
    const currentIP = await this.getCurrentIP();
    if (!currentIP) return { allowed: false, reason: 'Cannot determine IP' };

    // Public access - allow all IPs
    if (securityLevel === SECURITY_LEVELS.PUBLIC) {
      return { allowed: true, ip: currentIP };
    }

    // Admin access - strict IP checking
    if (securityLevel >= SECURITY_LEVELS.ADMIN) {
      if (!this.adminIPs.has(currentIP)) {
        SecurityLogger.logSecurityEvent('ADMIN_ACCESS_DENIED_IP', {
          ip: currentIP,
          securityLevel,
          timestamp: new Date().toISOString(),
        });
        return { allowed: false, reason: 'Admin access requires whitelisted IP' };
      }
    }

    // Financial operations - office network or VPN required
    if (securityLevel >= SECURITY_LEVELS.FINANCIAL) {
      const isOfficeNetwork = Array.from(this.officeNetworks).some(range => 
        this.isIPInRange(currentIP, range)
      );
      
      const isVPN = this.allowedIPs.has(currentIP);
      
      if (!isOfficeNetwork && !isVPN) {
        SecurityLogger.logSecurityEvent('FINANCIAL_ACCESS_DENIED_IP', {
          ip: currentIP,
          securityLevel,
          timestamp: new Date().toISOString(),
        });
        return { allowed: false, reason: 'Financial operations require secure network' };
      }
    }

    return { allowed: true, ip: currentIP };
  }
}

// Device Authorization System
export class DeviceAuthManager {
  constructor() {
    this.authorizedDevices = new Map();
    this.deviceFingerprints = new Map();
    this.suspiciousDevices = new Set();
    
    this.loadDeviceConfig();
  }

  loadDeviceConfig() {
    // Load from localStorage or backend
    const stored = localStorage.getItem('prochat_authorized_devices');
    if (stored) {
      try {
        const devices = JSON.parse(stored);
        devices.forEach(device => {
          this.authorizedDevices.set(device.id, device);
        });
      } catch (error) {
        console.error('Failed to load device config:', error);
      }
    }
  }

  async generateDeviceFingerprint() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('ProChat Device Fingerprint', 2, 2);

    const fingerprint = {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      screen: `${screen.width}x${screen.height}x${screen.colorDepth}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      canvas: canvas.toDataURL(),
      webgl: this.getWebGLFingerprint(),
      audio: await this.getAudioFingerprint(),
      fonts: this.getFontFingerprint(),
      plugins: Array.from(navigator.plugins).map(p => p.name).join(','),
      hardwareConcurrency: navigator.hardwareConcurrency,
      deviceMemory: navigator.deviceMemory,
      connection: navigator.connection ? {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
      } : null,
    };

    const fingerprintString = JSON.stringify(fingerprint);
    const encoder = new TextEncoder();
    const data = encoder.encode(fingerprintString);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  getWebGLFingerprint() {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (!gl) return null;

      return {
        vendor: gl.getParameter(gl.VENDOR),
        renderer: gl.getParameter(gl.RENDERER),
        version: gl.getParameter(gl.VERSION),
        shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
      };
    } catch (error) {
      return null;
    }
  }

  async getAudioFingerprint() {
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const analyser = audioContext.createAnalyser();
      const gainNode = audioContext.createGain();
      
      oscillator.type = 'triangle';
      oscillator.frequency.setValueAtTime(10000, audioContext.currentTime);
      
      gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      
      oscillator.connect(analyser);
      analyser.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.start(0);
      
      const frequencyData = new Uint8Array(analyser.frequencyBinCount);
      analyser.getByteFrequencyData(frequencyData);
      
      oscillator.stop();
      audioContext.close();
      
      return Array.from(frequencyData).slice(0, 30).join(',');
    } catch (error) {
      return null;
    }
  }

  getFontFingerprint() {
    const testFonts = [
      'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
      'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS',
      'Trebuchet MS', 'Arial Black', 'Impact'
    ];

    const baseFonts = ['monospace', 'sans-serif', 'serif'];
    const testString = 'mmmmmmmmmmlli';
    const testSize = '72px';
    
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    
    const baseFontWidths = {};
    baseFonts.forEach(baseFont => {
      context.font = testSize + ' ' + baseFont;
      baseFontWidths[baseFont] = context.measureText(testString).width;
    });

    const availableFonts = [];
    testFonts.forEach(font => {
      baseFonts.forEach(baseFont => {
        context.font = testSize + ' ' + font + ',' + baseFont;
        const width = context.measureText(testString).width;
        if (width !== baseFontWidths[baseFont]) {
          availableFonts.push(font);
        }
      });
    });

    return availableFonts.join(',');
  }

  async authorizeDevice(securityLevel = SECURITY_LEVELS.BASIC) {
    const deviceId = await this.generateDeviceFingerprint();
    const deviceInfo = {
      id: deviceId,
      type: detectDeviceType(),
      userAgent: navigator.userAgent,
      authorizedAt: new Date().toISOString(),
      securityLevel,
      lastSeen: new Date().toISOString(),
      trusted: false,
    };

    // Check if device is suspicious
    if (this.suspiciousDevices.has(deviceId)) {
      SecurityLogger.logSecurityEvent('SUSPICIOUS_DEVICE_ACCESS_ATTEMPT', {
        deviceId,
        deviceInfo,
      });
      return { authorized: false, reason: 'Device flagged as suspicious' };
    }

    // For financial operations, require manual approval
    if (securityLevel >= SECURITY_LEVELS.FINANCIAL) {
      const existingDevice = this.authorizedDevices.get(deviceId);
      if (!existingDevice || !existingDevice.trusted) {
        SecurityLogger.logSecurityEvent('FINANCIAL_DEVICE_AUTHORIZATION_REQUIRED', {
          deviceId,
          deviceInfo,
        });
        return { 
          authorized: false, 
          reason: 'Financial operations require trusted device',
          requiresApproval: true,
          deviceId 
        };
      }
    }

    // Store device info
    this.authorizedDevices.set(deviceId, deviceInfo);
    this.saveDeviceConfig();

    SecurityLogger.logSecurityEvent('DEVICE_AUTHORIZED', {
      deviceId,
      securityLevel,
    });

    return { authorized: true, deviceId, deviceInfo };
  }

  trustDevice(deviceId) {
    const device = this.authorizedDevices.get(deviceId);
    if (device) {
      device.trusted = true;
      device.trustedAt = new Date().toISOString();
      this.authorizedDevices.set(deviceId, device);
      this.saveDeviceConfig();
      
      SecurityLogger.logSecurityEvent('DEVICE_TRUSTED', { deviceId });
      return true;
    }
    return false;
  }

  flagSuspiciousDevice(deviceId, reason) {
    this.suspiciousDevices.add(deviceId);
    SecurityLogger.logSecurityEvent('DEVICE_FLAGGED_SUSPICIOUS', {
      deviceId,
      reason,
    });
  }

  saveDeviceConfig() {
    try {
      const devices = Array.from(this.authorizedDevices.values());
      localStorage.setItem('prochat_authorized_devices', JSON.stringify(devices));
    } catch (error) {
      console.error('Failed to save device config:', error);
    }
  }

  getAuthorizedDevices() {
    return Array.from(this.authorizedDevices.values());
  }

  revokeDevice(deviceId) {
    this.authorizedDevices.delete(deviceId);
    this.saveDeviceConfig();
    
    SecurityLogger.logSecurityEvent('DEVICE_REVOKED', { deviceId });
  }
}

// Role-Based Access Control
export class RBACManager {
  constructor() {
    this.roles = new Map();
    this.permissions = new Map();
    this.userRoles = new Map();
    
    this.initializeRoles();
  }

  initializeRoles() {
    // Define permissions
    const permissions = {
      // Social permissions
      'social.post.create': 'Create posts',
      'social.post.edit': 'Edit posts',
      'social.post.delete': 'Delete posts',
      'social.comment.create': 'Create comments',
      'social.like.create': 'Like posts',
      
      // Wallet permissions
      'wallet.view': 'View wallet balance',
      'wallet.send': 'Send money',
      'wallet.receive': 'Receive money',
      'wallet.withdraw': 'Withdraw money',
      'wallet.history': 'View transaction history',
      
      // Admin permissions
      'admin.users.view': 'View users',
      'admin.users.edit': 'Edit users',
      'admin.users.delete': 'Delete users',
      'admin.transactions.view': 'View all transactions',
      'admin.system.config': 'System configuration',
      
      // Critical permissions
      'critical.database.access': 'Direct database access',
      'critical.system.shutdown': 'System shutdown',
      'critical.security.config': 'Security configuration',
    };

    permissions.forEach((description, permission) => {
      this.permissions.set(permission, { permission, description });
    });

    // Define roles
    const roles = {
      'user': {
        name: 'Regular User',
        permissions: [
          'social.post.create', 'social.comment.create', 'social.like.create',
          'wallet.view', 'wallet.send', 'wallet.receive', 'wallet.history'
        ],
        securityLevel: SECURITY_LEVELS.BASIC,
      },
      'premium_user': {
        name: 'Premium User',
        permissions: [
          'social.post.create', 'social.post.edit', 'social.comment.create', 'social.like.create',
          'wallet.view', 'wallet.send', 'wallet.receive', 'wallet.withdraw', 'wallet.history'
        ],
        securityLevel: SECURITY_LEVELS.FINANCIAL,
      },
      'moderator': {
        name: 'Content Moderator',
        permissions: [
          'social.post.create', 'social.post.edit', 'social.post.delete',
          'social.comment.create', 'social.like.create',
          'admin.users.view'
        ],
        securityLevel: SECURITY_LEVELS.ADMIN,
      },
      'admin': {
        name: 'Administrator',
        permissions: [
          'social.post.create', 'social.post.edit', 'social.post.delete',
          'social.comment.create', 'social.like.create',
          'wallet.view', 'wallet.send', 'wallet.receive', 'wallet.withdraw', 'wallet.history',
          'admin.users.view', 'admin.users.edit', 'admin.transactions.view',
          'admin.system.config'
        ],
        securityLevel: SECURITY_LEVELS.ADMIN,
      },
      'super_admin': {
        name: 'Super Administrator',
        permissions: Array.from(this.permissions.keys()),
        securityLevel: SECURITY_LEVELS.CRITICAL,
      },
    };

    roles.forEach((roleData, roleName) => {
      this.roles.set(roleName, roleData);
    });
  }

  assignRole(userId, roleName) {
    if (!this.roles.has(roleName)) {
      throw new Error(`Role ${roleName} does not exist`);
    }

    this.userRoles.set(userId, roleName);
    
    SecurityLogger.logSecurityEvent('ROLE_ASSIGNED', {
      userId,
      roleName,
      timestamp: new Date().toISOString(),
    });
  }

  hasPermission(userId, permission) {
    const roleName = this.userRoles.get(userId);
    if (!roleName) return false;

    const role = this.roles.get(roleName);
    if (!role) return false;

    return role.permissions.includes(permission);
  }

  getRequiredSecurityLevel(permission) {
    // Map permissions to security levels
    if (permission.startsWith('critical.')) return SECURITY_LEVELS.CRITICAL;
    if (permission.startsWith('admin.')) return SECURITY_LEVELS.ADMIN;
    if (permission.startsWith('wallet.')) return SECURITY_LEVELS.FINANCIAL;
    return SECURITY_LEVELS.BASIC;
  }

  getUserRole(userId) {
    const roleName = this.userRoles.get(userId);
    return roleName ? this.roles.get(roleName) : null;
  }

  getUserPermissions(userId) {
    const role = this.getUserRole(userId);
    return role ? role.permissions : [];
  }
}

// Emergency Mode Manager
export class EmergencyModeManager {
  constructor() {
    this.isEmergencyMode = false;
    this.emergencyReason = null;
    this.emergencyStartTime = null;
    this.allowedOperations = new Set();
    
    this.checkEmergencyStatus();
  }

  checkEmergencyStatus() {
    const stored = localStorage.getItem('prochat_emergency_mode');
    if (stored) {
      try {
        const data = JSON.parse(stored);
        this.isEmergencyMode = data.active;
        this.emergencyReason = data.reason;
        this.emergencyStartTime = data.startTime;
      } catch (error) {
        console.error('Failed to load emergency status:', error);
      }
    }
  }

  activateEmergencyMode(reason, allowedOperations = []) {
    this.isEmergencyMode = true;
    this.emergencyReason = reason;
    this.emergencyStartTime = new Date().toISOString();
    this.allowedOperations = new Set(allowedOperations);

    // Save to storage
    localStorage.setItem('prochat_emergency_mode', JSON.stringify({
      active: true,
      reason,
      startTime: this.emergencyStartTime,
      allowedOperations: Array.from(this.allowedOperations),
    }));

    // Log security event
    SecurityLogger.logSecurityEvent('EMERGENCY_MODE_ACTIVATED', {
      reason,
      allowedOperations: Array.from(this.allowedOperations),
      timestamp: this.emergencyStartTime,
    });

    // Notify all users
    this.notifyEmergencyMode();

    console.warn('🚨 EMERGENCY MODE ACTIVATED:', reason);
  }

  deactivateEmergencyMode() {
    const duration = Date.now() - new Date(this.emergencyStartTime).getTime();
    
    this.isEmergencyMode = false;
    this.emergencyReason = null;
    this.emergencyStartTime = null;
    this.allowedOperations.clear();

    // Remove from storage
    localStorage.removeItem('prochat_emergency_mode');

    // Log security event
    SecurityLogger.logSecurityEvent('EMERGENCY_MODE_DEACTIVATED', {
      duration,
      timestamp: new Date().toISOString(),
    });

    console.log('✅ Emergency mode deactivated');
  }

  isOperationAllowed(operation) {
    if (!this.isEmergencyMode) return true;
    return this.allowedOperations.has(operation);
  }

  notifyEmergencyMode() {
    // Show emergency notification to users
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('ProChat Emergency Mode', {
        body: `System is in emergency mode: ${this.emergencyReason}`,
        icon: '/logo192.png',
        tag: 'emergency-mode',
      });
    }
  }

  getEmergencyStatus() {
    return {
      active: this.isEmergencyMode,
      reason: this.emergencyReason,
      startTime: this.emergencyStartTime,
      duration: this.emergencyStartTime ? 
        Date.now() - new Date(this.emergencyStartTime).getTime() : 0,
      allowedOperations: Array.from(this.allowedOperations),
    };
  }
}

// Create singleton instances
export const ipWhitelistManager = new IPWhitelistManager();
export const deviceAuthManager = new DeviceAuthManager();
export const rbacManager = new RBACManager();
export const emergencyModeManager = new EmergencyModeManager();

export default {
  SECURITY_LEVELS,
  IPWhitelistManager,
  DeviceAuthManager,
  RBACManager,
  EmergencyModeManager,
  ipWhitelistManager,
  deviceAuthManager,
  rbacManager,
  emergencyModeManager,
};
