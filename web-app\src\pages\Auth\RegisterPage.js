import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  IconButton,
  InputAdornment,
  Divider,
  Alert,
  CircularProgress,
  FormControlLabel,
  Checkbox,
  MenuItem,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Person,
  Phone,
  Email,
  Lock,
  Google,
  Facebook,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { useAuth } from '../../contexts/AuthContext';
import { useDeviceDetection } from '../../hooks/useDeviceDetection';

const RegisterPage = () => {
  const navigate = useNavigate();
  const { register: registerUser, isLoading, error } = useAuth();
  const { isMobile } = useDeviceDetection();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm();

  const password = watch('password');

  const onSubmit = async (data) => {
    const { confirmPassword, ...userData } = data;
    const result = await registerUser(userData);
    if (result.success) {
      navigate('/');
    }
  };

  const handleGoogleRegister = () => {
    console.log('Google register clicked');
  };

  const handleFacebookRegister = () => {
    console.log('Facebook register clicked');
  };

  const regions = [
    'Arusha', 'Dar es Salaam', 'Dodoma', 'Geita', 'Iringa', 'Kagera',
    'Katavi', 'Kigoma', 'Kilimanjaro', 'Lindi', 'Manyara', 'Mara',
    'Mbeya', 'Morogoro', 'Mtwara', 'Mwanza', 'Njombe', 'Pemba Kaskazini',
    'Pemba Kusini', 'Pwani', 'Rukwa', 'Ruvuma', 'Shinyanga', 'Simiyu',
    'Singida', 'Songwe', 'Tabora', 'Tanga', 'Unguja Kaskazini',
    'Unguja Kusini', 'Zanzibar Mjini'
  ];

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: isMobile 
          ? 'linear-gradient(135deg, #007AFF 0%, #4DA3FF 100%)'
          : 'linear-gradient(135deg, #007AFF 0%, #4DA3FF 50%, #FFFFFF 100%)',
        p: 2,
        py: 4,
      }}
    >
      <Paper
        elevation={isMobile ? 0 : 8}
        sx={{
          width: '100%',
          maxWidth: 500,
          p: isMobile ? 3 : 4,
          borderRadius: isMobile ? 0 : 3,
          bgcolor: isMobile ? 'transparent' : 'background.paper',
          boxShadow: isMobile ? 'none' : '0 8px 32px rgba(0,0,0,0.1)',
        }}
      >
        {/* Logo/Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography
            variant="h3"
            sx={{
              fontWeight: 'bold',
              color: isMobile ? 'white' : 'primary.main',
              mb: 1,
            }}
          >
            ProChat
          </Typography>
          <Typography
            variant="subtitle1"
            sx={{
              color: isMobile ? 'rgba(255,255,255,0.9)' : 'text.secondary',
            }}
          >
            Unda akaunti yako ya ProChat
          </Typography>
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Register Form */}
        <Box component="form" onSubmit={handleSubmit(onSubmit)}>
          {/* Full Name Field */}
          <TextField
            fullWidth
            label="Jina Kamili"
            placeholder="Jina lako kamili"
            variant="outlined"
            margin="normal"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Person color="action" />
                </InputAdornment>
              ),
              sx: {
                bgcolor: isMobile ? 'rgba(255,255,255,0.9)' : 'background.paper',
                borderRadius: 2,
              },
            }}
            InputLabelProps={{
              sx: {
                color: isMobile ? 'rgba(0,0,0,0.6)' : 'text.secondary',
              },
            }}
            {...register('fullName', {
              required: 'Jina kamili linahitajika',
              minLength: {
                value: 2,
                message: 'Jina lazima liwe na angalau herufi 2',
              },
            })}
            error={!!errors.fullName}
            helperText={errors.fullName?.message}
          />

          {/* Phone Number Field */}
          <TextField
            fullWidth
            label="Namba ya Simu"
            placeholder="+255 123 456 789"
            variant="outlined"
            margin="normal"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Phone color="action" />
                </InputAdornment>
              ),
              sx: {
                bgcolor: isMobile ? 'rgba(255,255,255,0.9)' : 'background.paper',
                borderRadius: 2,
              },
            }}
            InputLabelProps={{
              sx: {
                color: isMobile ? 'rgba(0,0,0,0.6)' : 'text.secondary',
              },
            }}
            {...register('phoneNumber', {
              required: 'Namba ya simu inahitajika',
              pattern: {
                value: /^(\+255|0)[67]\d{8}$/,
                message: 'Namba ya simu si sahihi',
              },
            })}
            error={!!errors.phoneNumber}
            helperText={errors.phoneNumber?.message}
          />

          {/* Email Field */}
          <TextField
            fullWidth
            label="Barua Pepe"
            placeholder="<EMAIL>"
            type="email"
            variant="outlined"
            margin="normal"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Email color="action" />
                </InputAdornment>
              ),
              sx: {
                bgcolor: isMobile ? 'rgba(255,255,255,0.9)' : 'background.paper',
                borderRadius: 2,
              },
            }}
            InputLabelProps={{
              sx: {
                color: isMobile ? 'rgba(0,0,0,0.6)' : 'text.secondary',
              },
            }}
            {...register('email', {
              required: 'Barua pepe inahitajika',
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Barua pepe si sahihi',
              },
            })}
            error={!!errors.email}
            helperText={errors.email?.message}
          />

          {/* Region Field */}
          <TextField
            fullWidth
            select
            label="Mkoa"
            variant="outlined"
            margin="normal"
            InputProps={{
              sx: {
                bgcolor: isMobile ? 'rgba(255,255,255,0.9)' : 'background.paper',
                borderRadius: 2,
              },
            }}
            InputLabelProps={{
              sx: {
                color: isMobile ? 'rgba(0,0,0,0.6)' : 'text.secondary',
              },
            }}
            {...register('region', {
              required: 'Mkoa unahitajika',
            })}
            error={!!errors.region}
            helperText={errors.region?.message}
          >
            {regions.map((region) => (
              <MenuItem key={region} value={region}>
                {region}
              </MenuItem>
            ))}
          </TextField>

          {/* Password Field */}
          <TextField
            fullWidth
            label="Nywila"
            type={showPassword ? 'text' : 'password'}
            variant="outlined"
            margin="normal"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Lock color="action" />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowPassword(!showPassword)}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
              sx: {
                bgcolor: isMobile ? 'rgba(255,255,255,0.9)' : 'background.paper',
                borderRadius: 2,
              },
            }}
            InputLabelProps={{
              sx: {
                color: isMobile ? 'rgba(0,0,0,0.6)' : 'text.secondary',
              },
            }}
            {...register('password', {
              required: 'Nywila inahitajika',
              minLength: {
                value: 8,
                message: 'Nywila lazima iwe na angalau herufi 8',
              },
              pattern: {
                value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                message: 'Nywila lazima iwe na herufi ndogo, kubwa na namba',
              },
            })}
            error={!!errors.password}
            helperText={errors.password?.message}
          />

          {/* Confirm Password Field */}
          <TextField
            fullWidth
            label="Thibitisha Nywila"
            type={showConfirmPassword ? 'text' : 'password'}
            variant="outlined"
            margin="normal"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Lock color="action" />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    edge="end"
                  >
                    {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
              sx: {
                bgcolor: isMobile ? 'rgba(255,255,255,0.9)' : 'background.paper',
                borderRadius: 2,
              },
            }}
            InputLabelProps={{
              sx: {
                color: isMobile ? 'rgba(0,0,0,0.6)' : 'text.secondary',
              },
            }}
            {...register('confirmPassword', {
              required: 'Thibitisha nywila',
              validate: value =>
                value === password || 'Nywila hazifanani',
            })}
            error={!!errors.confirmPassword}
            helperText={errors.confirmPassword?.message}
          />

          {/* Terms and Conditions */}
          <FormControlLabel
            control={
              <Checkbox
                {...register('acceptTerms', {
                  required: 'Lazima ukubali masharti',
                })}
                sx={{
                  color: isMobile ? 'rgba(255,255,255,0.8)' : 'primary.main',
                  '&.Mui-checked': {
                    color: isMobile ? 'white' : 'primary.main',
                  },
                }}
              />
            }
            label={
              <Typography
                variant="body2"
                sx={{
                  color: isMobile ? 'rgba(255,255,255,0.8)' : 'text.secondary',
                }}
              >
                Nakubali{' '}
                <Link
                  to="/terms"
                  style={{
                    color: isMobile ? 'white' : '#007AFF',
                    textDecoration: 'underline',
                  }}
                >
                  Masharti na Vigezo
                </Link>
              </Typography>
            }
            sx={{ mt: 2, mb: 3 }}
          />

          {errors.acceptTerms && (
            <Typography
              variant="caption"
              color="error"
              sx={{ display: 'block', mt: -2, mb: 2 }}
            >
              {errors.acceptTerms.message}
            </Typography>
          )}

          {/* Register Button */}
          <Button
            type="submit"
            fullWidth
            variant="contained"
            size="large"
            disabled={isLoading}
            sx={{
              py: 1.5,
              borderRadius: 2,
              fontSize: '1.1rem',
              fontWeight: 'bold',
              bgcolor: isMobile ? 'white' : 'primary.main',
              color: isMobile ? 'primary.main' : 'white',
              '&:hover': {
                bgcolor: isMobile ? 'rgba(255,255,255,0.9)' : 'primary.dark',
              },
              mb: 3,
            }}
          >
            {isLoading ? (
              <CircularProgress size={24} color="inherit" />
            ) : (
              'Jisajili'
            )}
          </Button>

          {/* Divider */}
          <Divider sx={{ my: 3 }}>
            <Typography
              variant="body2"
              sx={{
                color: isMobile ? 'rgba(255,255,255,0.8)' : 'text.secondary',
                px: 2,
              }}
            >
              Au jisajili kwa
            </Typography>
          </Divider>

          {/* Social Register Buttons */}
          <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Google />}
              onClick={handleGoogleRegister}
              sx={{
                py: 1.2,
                borderRadius: 2,
                borderColor: isMobile ? 'rgba(255,255,255,0.5)' : 'divider',
                color: isMobile ? 'white' : 'text.primary',
                '&:hover': {
                  borderColor: isMobile ? 'white' : 'primary.main',
                  bgcolor: isMobile ? 'rgba(255,255,255,0.1)' : 'action.hover',
                },
              }}
            >
              Google
            </Button>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Facebook />}
              onClick={handleFacebookRegister}
              sx={{
                py: 1.2,
                borderRadius: 2,
                borderColor: isMobile ? 'rgba(255,255,255,0.5)' : 'divider',
                color: isMobile ? 'white' : 'text.primary',
                '&:hover': {
                  borderColor: isMobile ? 'white' : 'primary.main',
                  bgcolor: isMobile ? 'rgba(255,255,255,0.1)' : 'action.hover',
                },
              }}
            >
              Facebook
            </Button>
          </Box>

          {/* Login Link */}
          <Box sx={{ textAlign: 'center' }}>
            <Typography
              variant="body2"
              sx={{
                color: isMobile ? 'rgba(255,255,255,0.8)' : 'text.secondary',
              }}
            >
              Una akaunti tayari?{' '}
              <Link
                to="/login"
                style={{
                  color: isMobile ? 'white' : '#007AFF',
                  textDecoration: 'none',
                  fontWeight: 'bold',
                }}
              >
                Ingia hapa
              </Link>
            </Typography>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default RegisterPage;
