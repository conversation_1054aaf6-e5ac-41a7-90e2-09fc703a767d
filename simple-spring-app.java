// Simple ProChat Spring Boot Application
import java.util.*;
import java.time.LocalDateTime;

// Mock Spring Boot Application
public class ProChatApplication {
    
    // Mock data
    private static List<User> users = new ArrayList<>();
    private static List<Post> posts = new ArrayList<>();
    private static List<Transaction> transactions = new ArrayList<>();
    
    static {
        // Initialize mock data
        users.add(new User(1L, "john_doe", "<EMAIL>", 250000.0));
        users.add(new User(2L, "jane_smith", "<EMAIL>", 180000.0));
        users.add(new User(3L, "mike_wilson", "<EMAIL>", 320000.0));
        
        posts.add(new Post(1L, 1L, "Habari za asubuhi! Leo ni siku nzuri ya kufanya biashara.", 15, 3));
        posts.add(new Post(2L, 2L, "Nimepata kazi mpya kupitia ProChat Jobs! Asante sana.", 28, 7));
        posts.add(new Post(3L, 3L, "Event ya muziki leo jioni Mlimani City. Tiketi zinapatikana ProChat.", 42, 12));
        
        transactions.add(new Transaction(1L, 1L, "SEND_MONEY", 50000.0, "COMPLETED", "Malipo ya bidhaa"));
        transactions.add(new Transaction(2L, 2L, "RECEIVE_MONEY", 75000.0, "COMPLETED", "Mshahara"));
        transactions.add(new Transaction(3L, 3L, "BILL_PAYMENT", 25000.0, "COMPLETED", "LUKU - Umeme"));
    }
    
    public static void main(String[] args) {
        System.out.println("🚀 ProChat Backend Starting...");
        System.out.println("📊 Loaded " + users.size() + " users");
        System.out.println("📱 Loaded " + posts.size() + " posts");
        System.out.println("💰 Loaded " + transactions.size() + " transactions");
        System.out.println("✅ ProChat Backend Ready!");
        System.out.println("🌐 API would be available at: http://localhost:8080/api");
        
        // Simulate API endpoints
        System.out.println("\n📋 Available API Endpoints:");
        System.out.println("GET /api/health - Health check");
        System.out.println("GET /api/users - Get all users");
        System.out.println("GET /api/posts - Get all posts");
        System.out.println("GET /api/wallet/balance - Get wallet balance");
        System.out.println("GET /api/wallet/transactions - Get transactions");
        System.out.println("POST /api/auth/login - User login");
        System.out.println("POST /api/posts - Create new post");
        System.out.println("POST /api/wallet/send - Send money");
        
        // Show sample data
        System.out.println("\n👥 Sample Users:");
        users.forEach(user -> System.out.println("  " + user));
        
        System.out.println("\n📱 Sample Posts:");
        posts.forEach(post -> System.out.println("  " + post));
        
        System.out.println("\n💰 Sample Transactions:");
        transactions.forEach(tx -> System.out.println("  " + tx));
        
        System.out.println("\n🎯 ProChat Backend simulation complete!");
        System.out.println("📝 To run real backend, fix Maven/Gradle issues and run:");
        System.out.println("   mvn spring-boot:run");
        System.out.println("   or");
        System.out.println("   ./gradlew bootRun");
    }
    
    // Data classes
    static class User {
        Long id;
        String username;
        String email;
        Double balance;
        
        User(Long id, String username, String email, Double balance) {
            this.id = id;
            this.username = username;
            this.email = email;
            this.balance = balance;
        }
        
        @Override
        public String toString() {
            return String.format("User{id=%d, username='%s', email='%s', balance=TZS %.0f}", 
                id, username, email, balance);
        }
    }
    
    static class Post {
        Long id;
        Long userId;
        String content;
        Integer likes;
        Integer comments;
        
        Post(Long id, Long userId, String content, Integer likes, Integer comments) {
            this.id = id;
            this.userId = userId;
            this.content = content;
            this.likes = likes;
            this.comments = comments;
        }
        
        @Override
        public String toString() {
            return String.format("Post{id=%d, userId=%d, content='%s', likes=%d, comments=%d}", 
                id, userId, content.substring(0, Math.min(30, content.length())) + "...", likes, comments);
        }
    }
    
    static class Transaction {
        Long id;
        Long userId;
        String type;
        Double amount;
        String status;
        String description;
        
        Transaction(Long id, Long userId, String type, Double amount, String status, String description) {
            this.id = id;
            this.userId = userId;
            this.type = type;
            this.amount = amount;
            this.status = status;
            this.description = description;
        }
        
        @Override
        public String toString() {
            return String.format("Transaction{id=%d, userId=%d, type='%s', amount=TZS %.0f, status='%s', desc='%s'}", 
                id, userId, type, amount, status, description);
        }
    }
}
