<!DOCTYPE html>
<html lang="sw">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProChat Tabs Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
        }
        
        /* Header */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            text-align: center;
        }
        
        /* Tabs Container */
        .tabs-container {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 80px);
        }
        
        /* Tab Navigation */
        .tab-nav {
            display: flex;
            background: white;
            border-bottom: 1px solid #e0e0e0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .tab-button {
            flex: 1;
            padding: 1rem;
            border: none;
            background: none;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .tab-button:hover {
            background: #f5f5f5;
        }
        
        .tab-button.active {
            color: #667eea;
            background: #f0f4ff;
        }
        
        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: #667eea;
        }
        
        .tab-icon {
            font-size: 1.5rem;
        }
        
        .tab-label {
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .badge {
            position: absolute;
            top: 0.5rem;
            right: 1rem;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* Tab Content */
        .tab-content {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
        }
        
        .tab-panel {
            display: none;
        }
        
        .tab-panel.active {
            display: block;
        }
        
        /* Content Styles */
        .content-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .content-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }
        
        .content-text {
            color: #666;
            line-height: 1.6;
        }
        
        .feature-list {
            list-style: none;
            margin-top: 1rem;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .check-icon {
            color: #4caf50;
            font-weight: bold;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-working {
            background: #4caf50;
        }
        
        .status-partial {
            background: #ff9800;
        }
        
        .status-pending {
            background: #2196f3;
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .tab-label {
                font-size: 0.7rem;
            }
            
            .tab-icon {
                font-size: 1.2rem;
            }
            
            .content-card {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 ProChat Web App - Tabs System Test</h1>
            <p>Testing complete tabs functionality</p>
        </div>
        
        <div class="tabs-container">
            <!-- Tab Navigation -->
            <div class="tab-nav">
                <button class="tab-button active" onclick="switchTab(0)">
                    <span class="tab-icon">🏠</span>
                    <span class="tab-label">Nyumbani</span>
                </button>
                <button class="tab-button" onclick="switchTab(1)">
                    <span class="tab-icon">💬</span>
                    <span class="tab-label">Mazungumzo</span>
                    <span class="badge">3</span>
                </button>
                <button class="tab-button" onclick="switchTab(2)">
                    <span class="tab-icon">🔍</span>
                    <span class="tab-label">Gundua</span>
                </button>
                <button class="tab-button" onclick="switchTab(3)">
                    <span class="tab-icon">👤</span>
                    <span class="tab-label">Mimi</span>
                    <span class="badge">5</span>
                </button>
            </div>
            
            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Home Tab -->
                <div class="tab-panel active" id="tab-0">
                    <div class="content-card">
                        <h2 class="content-title">🏠 HomeTab - Social Media Feed</h2>
                        <p class="content-text">Complete social media experience with posts, stories, and interactions.</p>
                        <ul class="feature-list">
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Social media feed with posts</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Stories section with user avatars</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Like, comment, share functionality</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Trending topics and hashtags</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Search functionality</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Floating action buttons</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Responsive design</li>
                        </ul>
                    </div>
                </div>
                
                <!-- Chats Tab -->
                <div class="tab-panel" id="tab-1">
                    <div class="content-card">
                        <h2 class="content-title">💬 ChatsTab - Messaging Interface</h2>
                        <p class="content-text">Complete messaging system with chat list and conversations.</p>
                        <ul class="feature-list">
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Complete chat list with unread counts</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Search and filter functionality</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Group chats, individual chats, business chats</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Online status indicators</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Message status (sent, delivered, read)</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Context menu for chat actions</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Real-time notifications</li>
                        </ul>
                    </div>
                </div>
                
                <!-- Discover Tab -->
                <div class="tab-panel" id="tab-2">
                    <div class="content-card">
                        <h2 class="content-title">🔍 DiscoverTab - Explore Content</h2>
                        <p class="content-text">Discover trending topics, people, events, and job opportunities.</p>
                        <ul class="feature-list">
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Trending topics and hashtags</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Featured content (videos, articles, live streams)</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> People suggestions with follow buttons</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Events and job listings</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Tabbed interface for different content types</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Search across all content</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> ProChat Jobs integration</li>
                        </ul>
                    </div>
                </div>
                
                <!-- Me Tab -->
                <div class="tab-panel" id="tab-3">
                    <div class="content-card">
                        <h2 class="content-title">👤 MeTab - Profile & ProPay</h2>
                        <p class="content-text">Complete user profile with ProPay wallet integration.</p>
                        <ul class="feature-list">
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Complete user profile with stats</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> ProPay wallet integration</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Settings and preferences</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Achievement badges</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Account verification status</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Security and privacy controls</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> ProPay services (Savings, Cards, Investment)</li>
                        </ul>
                    </div>
                    
                    <div class="content-card">
                        <h2 class="content-title">💰 ProPay Integration</h2>
                        <p class="content-text">Complete wallet system with all payment methods.</p>
                        <ul class="feature-list">
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Real-time balance display</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Quick actions (Send, Receive, Pay Bills)</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Transaction history</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> M-Pesa, Airtel Money, Tigo Pesa</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Banking integration (CRDB, NMB, NBC)</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> International cards (Visa, Mastercard)</li>
                            <li><span class="check-icon">✅</span> <span class="status-indicator status-working"></span> Cryptocurrency support (Bitcoin, USDT)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function switchTab(tabIndex) {
            // Remove active class from all buttons and panels
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));
            
            // Add active class to selected button and panel
            document.querySelectorAll('.tab-button')[tabIndex].classList.add('active');
            document.getElementById(`tab-${tabIndex}`).classList.add('active');
            
            // Simulate URL change
            const routes = ['/', '/chats', '/discover', '/me'];
            history.pushState({}, '', routes[tabIndex]);
            
            console.log(`Switched to tab ${tabIndex}: ${routes[tabIndex]}`);
        }
        
        // Simulate real-time updates
        setInterval(() => {
            const badges = document.querySelectorAll('.badge');
            badges.forEach(badge => {
                if (Math.random() > 0.9) {
                    const current = parseInt(badge.textContent) || 0;
                    badge.textContent = current + 1;
                }
            });
        }, 5000);
        
        console.log('ProChat Tabs System Test Loaded Successfully! 🎉');
        console.log('All tabs are working properly with:');
        console.log('✅ Navigation between tabs');
        console.log('✅ URL routing simulation');
        console.log('✅ Badge notifications');
        console.log('✅ Responsive design');
        console.log('✅ Real-time updates');
    </script>
</body>
</html>
