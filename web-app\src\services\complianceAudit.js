// ProChat Advanced Compliance & Audit System
// Regulatory compliance and comprehensive audit trails

import { SecurityLogger } from '../config/security';
import { rbacManager } from '../config/bankingSecurity';

class ComplianceAuditService {
  constructor() {
    this.auditLogs = new Map();
    this.complianceRules = new Map();
    this.auditTrails = new Map();
    this.complianceReports = new Map();
    this.retentionPolicies = new Map();
    this.dataClassifications = new Map();
    
    this.initializeComplianceFramework();
    this.startAuditMonitoring();
  }

  initializeComplianceFramework() {
    // Initialize compliance rules for different regulations
    this.setupGDPRCompliance();
    this.setupPCIDSSCompliance();
    this.setupSOXCompliance();
    this.setupTanzaniaDataProtection();
    this.setupAMLCompliance();
    this.setupKYCCompliance();
    
    // Initialize data retention policies
    this.setupRetentionPolicies();
    
    // Initialize data classification
    this.setupDataClassification();
  }

  setupGDPRCompliance() {
    this.complianceRules.set('GDPR', {
      name: 'General Data Protection Regulation',
      region: 'EU',
      requirements: {
        'data_minimization': {
          description: 'Collect only necessary personal data',
          checks: ['purpose_limitation', 'data_adequacy'],
          severity: 'HIGH',
        },
        'consent_management': {
          description: 'Explicit consent for data processing',
          checks: ['consent_recorded', 'consent_withdrawable'],
          severity: 'CRITICAL',
        },
        'right_to_erasure': {
          description: 'Right to be forgotten',
          checks: ['deletion_capability', 'data_portability'],
          severity: 'HIGH',
        },
        'data_breach_notification': {
          description: '72-hour breach notification',
          checks: ['breach_detection', 'notification_timeline'],
          severity: 'CRITICAL',
        },
        'privacy_by_design': {
          description: 'Privacy built into systems',
          checks: ['encryption_at_rest', 'encryption_in_transit'],
          severity: 'HIGH',
        },
      },
    });
  }

  setupPCIDSSCompliance() {
    this.complianceRules.set('PCI_DSS', {
      name: 'Payment Card Industry Data Security Standard',
      region: 'Global',
      requirements: {
        'secure_network': {
          description: 'Build and maintain secure network',
          checks: ['firewall_config', 'default_passwords'],
          severity: 'CRITICAL',
        },
        'protect_cardholder_data': {
          description: 'Protect stored cardholder data',
          checks: ['data_encryption', 'secure_storage'],
          severity: 'CRITICAL',
        },
        'vulnerability_management': {
          description: 'Maintain vulnerability management program',
          checks: ['antivirus_software', 'secure_systems'],
          severity: 'HIGH',
        },
        'access_control': {
          description: 'Restrict access to cardholder data',
          checks: ['unique_ids', 'access_restrictions'],
          severity: 'HIGH',
        },
        'monitor_networks': {
          description: 'Monitor and test networks',
          checks: ['logging_enabled', 'security_testing'],
          severity: 'MEDIUM',
        },
        'information_security': {
          description: 'Maintain information security policy',
          checks: ['security_policy', 'risk_assessment'],
          severity: 'MEDIUM',
        },
      },
    });
  }

  setupSOXCompliance() {
    this.complianceRules.set('SOX', {
      name: 'Sarbanes-Oxley Act',
      region: 'US',
      requirements: {
        'financial_reporting': {
          description: 'Accurate financial reporting',
          checks: ['data_integrity', 'audit_trails'],
          severity: 'CRITICAL',
        },
        'internal_controls': {
          description: 'Internal control over financial reporting',
          checks: ['segregation_of_duties', 'authorization_controls'],
          severity: 'HIGH',
        },
        'audit_trail': {
          description: 'Complete audit trail',
          checks: ['transaction_logging', 'change_management'],
          severity: 'HIGH',
        },
      },
    });
  }

  setupTanzaniaDataProtection() {
    this.complianceRules.set('TANZANIA_DPA', {
      name: 'Tanzania Data Protection Act',
      region: 'Tanzania',
      requirements: {
        'data_controller_registration': {
          description: 'Register as data controller',
          checks: ['registration_status', 'annual_renewal'],
          severity: 'HIGH',
        },
        'data_subject_rights': {
          description: 'Respect data subject rights',
          checks: ['access_rights', 'correction_rights'],
          severity: 'HIGH',
        },
        'cross_border_transfer': {
          description: 'Secure cross-border data transfer',
          checks: ['adequacy_decision', 'safeguards'],
          severity: 'MEDIUM',
        },
      },
    });
  }

  setupAMLCompliance() {
    this.complianceRules.set('AML', {
      name: 'Anti-Money Laundering',
      region: 'Global',
      requirements: {
        'customer_due_diligence': {
          description: 'Know your customer procedures',
          checks: ['identity_verification', 'risk_assessment'],
          severity: 'CRITICAL',
        },
        'transaction_monitoring': {
          description: 'Monitor suspicious transactions',
          checks: ['automated_monitoring', 'threshold_alerts'],
          severity: 'HIGH',
        },
        'suspicious_activity_reporting': {
          description: 'Report suspicious activities',
          checks: ['sar_filing', 'timely_reporting'],
          severity: 'CRITICAL',
        },
        'record_keeping': {
          description: 'Maintain transaction records',
          checks: ['record_retention', 'audit_trail'],
          severity: 'HIGH',
        },
      },
    });
  }

  setupKYCCompliance() {
    this.complianceRules.set('KYC', {
      name: 'Know Your Customer',
      region: 'Global',
      requirements: {
        'identity_verification': {
          description: 'Verify customer identity',
          checks: ['document_verification', 'biometric_check'],
          severity: 'CRITICAL',
        },
        'risk_profiling': {
          description: 'Assess customer risk',
          checks: ['risk_scoring', 'enhanced_due_diligence'],
          severity: 'HIGH',
        },
        'ongoing_monitoring': {
          description: 'Continuous customer monitoring',
          checks: ['periodic_review', 'behavior_analysis'],
          severity: 'MEDIUM',
        },
      },
    });
  }

  setupRetentionPolicies() {
    // Data retention policies by type
    this.retentionPolicies.set('financial_records', {
      retention_period: 7 * 365 * 24 * 60 * 60 * 1000, // 7 years
      legal_basis: 'Financial regulations',
      auto_delete: true,
    });

    this.retentionPolicies.set('audit_logs', {
      retention_period: 5 * 365 * 24 * 60 * 60 * 1000, // 5 years
      legal_basis: 'Audit requirements',
      auto_delete: false, // Manual review required
    });

    this.retentionPolicies.set('user_data', {
      retention_period: 2 * 365 * 24 * 60 * 60 * 1000, // 2 years after account closure
      legal_basis: 'GDPR Article 5',
      auto_delete: true,
    });

    this.retentionPolicies.set('security_logs', {
      retention_period: 1 * 365 * 24 * 60 * 60 * 1000, // 1 year
      legal_basis: 'Security monitoring',
      auto_delete: true,
    });
  }

  setupDataClassification() {
    // Classify data by sensitivity
    this.dataClassifications.set('PUBLIC', {
      level: 0,
      description: 'Public information',
      encryption_required: false,
      access_controls: 'none',
    });

    this.dataClassifications.set('INTERNAL', {
      level: 1,
      description: 'Internal use only',
      encryption_required: false,
      access_controls: 'authentication',
    });

    this.dataClassifications.set('CONFIDENTIAL', {
      level: 2,
      description: 'Confidential information',
      encryption_required: true,
      access_controls: 'authorization',
    });

    this.dataClassifications.set('RESTRICTED', {
      level: 3,
      description: 'Highly sensitive data',
      encryption_required: true,
      access_controls: 'multi_factor',
    });

    this.dataClassifications.set('TOP_SECRET', {
      level: 4,
      description: 'Top secret information',
      encryption_required: true,
      access_controls: 'privileged_access',
    });
  }

  startAuditMonitoring() {
    // Start continuous audit monitoring
    setInterval(() => {
      this.performComplianceCheck();
      this.generateAuditReports();
      this.enforceRetentionPolicies();
    }, 3600000); // Every hour

    console.log('Compliance Audit Service: Monitoring started');
  }

  // Audit logging methods
  logAuditEvent(eventType, details) {
    const auditId = this.generateAuditId();
    const timestamp = new Date().toISOString();
    
    const auditEntry = {
      id: auditId,
      timestamp,
      eventType,
      details,
      userId: details.userId,
      sessionId: details.sessionId,
      ipAddress: details.ipAddress,
      userAgent: details.userAgent,
      classification: this.classifyAuditEvent(eventType),
      compliance_tags: this.getComplianceTags(eventType),
    };

    // Store audit entry
    this.auditLogs.set(auditId, auditEntry);

    // Add to user's audit trail
    if (details.userId) {
      this.addToUserAuditTrail(details.userId, auditEntry);
    }

    // Check for compliance violations
    this.checkComplianceViolation(auditEntry);

    return auditId;
  }

  classifyAuditEvent(eventType) {
    const classifications = {
      'user_login': 'INTERNAL',
      'user_logout': 'INTERNAL',
      'password_change': 'CONFIDENTIAL',
      'financial_transaction': 'RESTRICTED',
      'data_export': 'CONFIDENTIAL',
      'admin_access': 'RESTRICTED',
      'system_config_change': 'RESTRICTED',
      'security_incident': 'TOP_SECRET',
      'data_breach': 'TOP_SECRET',
    };

    return classifications[eventType] || 'INTERNAL';
  }

  getComplianceTags(eventType) {
    const tags = [];

    // GDPR tags
    if (['user_registration', 'data_export', 'data_deletion'].includes(eventType)) {
      tags.push('GDPR');
    }

    // PCI DSS tags
    if (['payment_processing', 'card_data_access'].includes(eventType)) {
      tags.push('PCI_DSS');
    }

    // SOX tags
    if (['financial_transaction', 'financial_report'].includes(eventType)) {
      tags.push('SOX');
    }

    // AML tags
    if (['large_transaction', 'suspicious_activity'].includes(eventType)) {
      tags.push('AML');
    }

    // KYC tags
    if (['identity_verification', 'customer_onboarding'].includes(eventType)) {
      tags.push('KYC');
    }

    return tags;
  }

  addToUserAuditTrail(userId, auditEntry) {
    if (!this.auditTrails.has(userId)) {
      this.auditTrails.set(userId, []);
    }

    const userTrail = this.auditTrails.get(userId);
    userTrail.push(auditEntry);

    // Keep only recent entries (last 1000)
    if (userTrail.length > 1000) {
      userTrail.splice(0, userTrail.length - 1000);
    }
  }

  checkComplianceViolation(auditEntry) {
    const { eventType, details, userId } = auditEntry;

    // Check for potential violations
    const violations = [];

    // Check access control violations
    if (eventType === 'admin_access' && !this.isAuthorizedAdmin(userId)) {
      violations.push({
        rule: 'unauthorized_admin_access',
        severity: 'CRITICAL',
        description: 'Unauthorized administrative access attempt',
      });
    }

    // Check data access violations
    if (eventType === 'data_export' && !this.hasDataExportPermission(userId)) {
      violations.push({
        rule: 'unauthorized_data_export',
        severity: 'HIGH',
        description: 'Unauthorized data export attempt',
      });
    }

    // Check transaction violations
    if (eventType === 'financial_transaction' && this.exceedsTransactionLimits(details)) {
      violations.push({
        rule: 'transaction_limit_exceeded',
        severity: 'HIGH',
        description: 'Transaction exceeds regulatory limits',
      });
    }

    // Process violations
    if (violations.length > 0) {
      this.processComplianceViolations(auditEntry, violations);
    }
  }

  processComplianceViolations(auditEntry, violations) {
    violations.forEach(violation => {
      SecurityLogger.logSecurityEvent('COMPLIANCE_VIOLATION', {
        auditId: auditEntry.id,
        violation,
        userId: auditEntry.userId,
        timestamp: auditEntry.timestamp,
      });

      // Take corrective action based on severity
      if (violation.severity === 'CRITICAL') {
        this.handleCriticalViolation(auditEntry, violation);
      } else if (violation.severity === 'HIGH') {
        this.handleHighViolation(auditEntry, violation);
      }
    });
  }

  handleCriticalViolation(auditEntry, violation) {
    // Immediate action for critical violations
    console.log(`🚨 CRITICAL COMPLIANCE VIOLATION: ${violation.description}`);
    
    // Notify compliance team
    this.notifyComplianceTeam('CRITICAL', violation, auditEntry);
    
    // Consider emergency measures
    if (violation.rule === 'unauthorized_admin_access') {
      // Lock the account immediately
      this.lockUserAccount(auditEntry.userId, 'Critical compliance violation');
    }
  }

  handleHighViolation(auditEntry, violation) {
    // Action for high-severity violations
    console.log(`⚠️ HIGH COMPLIANCE VIOLATION: ${violation.description}`);
    
    // Notify compliance team
    this.notifyComplianceTeam('HIGH', violation, auditEntry);
    
    // Flag for review
    this.flagForComplianceReview(auditEntry, violation);
  }

  // Compliance checking methods
  performComplianceCheck() {
    const results = new Map();

    // Check each compliance framework
    this.complianceRules.forEach((framework, frameworkId) => {
      const frameworkResults = this.checkFrameworkCompliance(frameworkId, framework);
      results.set(frameworkId, frameworkResults);
    });

    // Generate compliance report
    this.generateComplianceReport(results);

    return results;
  }

  checkFrameworkCompliance(frameworkId, framework) {
    const results = {
      framework: frameworkId,
      overall_score: 0,
      requirements: new Map(),
      violations: [],
      recommendations: [],
    };

    let totalScore = 0;
    let requirementCount = 0;

    // Check each requirement
    Object.entries(framework.requirements).forEach(([reqId, requirement]) => {
      const reqResult = this.checkRequirementCompliance(reqId, requirement);
      results.requirements.set(reqId, reqResult);
      
      totalScore += reqResult.score;
      requirementCount++;

      if (reqResult.violations.length > 0) {
        results.violations.push(...reqResult.violations);
      }

      if (reqResult.recommendations.length > 0) {
        results.recommendations.push(...reqResult.recommendations);
      }
    });

    results.overall_score = requirementCount > 0 ? totalScore / requirementCount : 0;

    return results;
  }

  checkRequirementCompliance(reqId, requirement) {
    const result = {
      requirement: reqId,
      score: 0,
      status: 'non_compliant',
      violations: [],
      recommendations: [],
      evidence: [],
    };

    // Check each compliance check
    let passedChecks = 0;
    const totalChecks = requirement.checks.length;

    requirement.checks.forEach(check => {
      const checkResult = this.performComplianceCheck(check);
      
      if (checkResult.passed) {
        passedChecks++;
        result.evidence.push(checkResult.evidence);
      } else {
        result.violations.push({
          check,
          reason: checkResult.reason,
          severity: requirement.severity,
        });
        result.recommendations.push(checkResult.recommendation);
      }
    });

    // Calculate score
    result.score = totalChecks > 0 ? (passedChecks / totalChecks) * 100 : 0;
    
    // Determine status
    if (result.score >= 100) {
      result.status = 'compliant';
    } else if (result.score >= 80) {
      result.status = 'mostly_compliant';
    } else if (result.score >= 60) {
      result.status = 'partially_compliant';
    } else {
      result.status = 'non_compliant';
    }

    return result;
  }

  performComplianceCheck(checkType) {
    // Implement specific compliance checks
    switch (checkType) {
      case 'encryption_at_rest':
        return this.checkEncryptionAtRest();
      case 'encryption_in_transit':
        return this.checkEncryptionInTransit();
      case 'access_controls':
        return this.checkAccessControls();
      case 'audit_trails':
        return this.checkAuditTrails();
      case 'data_retention':
        return this.checkDataRetention();
      default:
        return { passed: false, reason: 'Check not implemented', recommendation: 'Implement compliance check' };
    }
  }

  checkEncryptionAtRest() {
    // Check if data is encrypted at rest
    return {
      passed: true, // Simplified for demo
      evidence: 'AES-256 encryption enabled for database',
      reason: null,
      recommendation: null,
    };
  }

  checkEncryptionInTransit() {
    // Check if data is encrypted in transit
    return {
      passed: true, // Simplified for demo
      evidence: 'TLS 1.3 enabled for all communications',
      reason: null,
      recommendation: null,
    };
  }

  checkAccessControls() {
    // Check access control implementation
    return {
      passed: true, // Simplified for demo
      evidence: 'RBAC system implemented with proper permissions',
      reason: null,
      recommendation: null,
    };
  }

  checkAuditTrails() {
    // Check audit trail completeness
    const recentLogs = Array.from(this.auditLogs.values())
      .filter(log => Date.now() - new Date(log.timestamp).getTime() < 24 * 60 * 60 * 1000);

    return {
      passed: recentLogs.length > 0,
      evidence: `${recentLogs.length} audit entries in last 24 hours`,
      reason: recentLogs.length === 0 ? 'No recent audit logs found' : null,
      recommendation: recentLogs.length === 0 ? 'Ensure audit logging is enabled' : null,
    };
  }

  checkDataRetention() {
    // Check data retention policy compliance
    return {
      passed: true, // Simplified for demo
      evidence: 'Data retention policies defined and enforced',
      reason: null,
      recommendation: null,
    };
  }

  // Report generation
  generateComplianceReport(results) {
    const reportId = this.generateReportId();
    const timestamp = new Date().toISOString();

    const report = {
      id: reportId,
      timestamp,
      type: 'compliance_assessment',
      results,
      summary: this.generateComplianceSummary(results),
      recommendations: this.generateRecommendations(results),
    };

    this.complianceReports.set(reportId, report);

    SecurityLogger.logSecurityEvent('COMPLIANCE_REPORT_GENERATED', {
      reportId,
      timestamp,
      frameworks: Array.from(results.keys()),
    });

    return report;
  }

  generateComplianceSummary(results) {
    const summary = {
      total_frameworks: results.size,
      compliant_frameworks: 0,
      overall_score: 0,
      critical_violations: 0,
      high_violations: 0,
    };

    let totalScore = 0;

    results.forEach(result => {
      totalScore += result.overall_score;
      
      if (result.overall_score >= 80) {
        summary.compliant_frameworks++;
      }

      result.violations.forEach(violation => {
        if (violation.severity === 'CRITICAL') {
          summary.critical_violations++;
        } else if (violation.severity === 'HIGH') {
          summary.high_violations++;
        }
      });
    });

    summary.overall_score = results.size > 0 ? totalScore / results.size : 0;

    return summary;
  }

  generateRecommendations(results) {
    const recommendations = [];

    results.forEach(result => {
      recommendations.push(...result.recommendations);
    });

    // Remove duplicates and prioritize
    const uniqueRecommendations = [...new Set(recommendations)];
    
    return uniqueRecommendations.slice(0, 10); // Top 10 recommendations
  }

  // Data retention enforcement
  enforceRetentionPolicies() {
    this.retentionPolicies.forEach((policy, dataType) => {
      this.enforceRetentionPolicy(dataType, policy);
    });
  }

  enforceRetentionPolicy(dataType, policy) {
    const cutoffTime = Date.now() - policy.retention_period;

    switch (dataType) {
      case 'audit_logs':
        this.cleanupAuditLogs(cutoffTime, policy.auto_delete);
        break;
      case 'security_logs':
        this.cleanupSecurityLogs(cutoffTime, policy.auto_delete);
        break;
      case 'user_data':
        this.cleanupUserData(cutoffTime, policy.auto_delete);
        break;
    }
  }

  cleanupAuditLogs(cutoffTime, autoDelete) {
    const expiredLogs = [];

    this.auditLogs.forEach((log, logId) => {
      if (new Date(log.timestamp).getTime() < cutoffTime) {
        expiredLogs.push(logId);
      }
    });

    if (autoDelete) {
      expiredLogs.forEach(logId => {
        this.auditLogs.delete(logId);
      });
      
      console.log(`🗑️ Auto-deleted ${expiredLogs.length} expired audit logs`);
    } else {
      console.log(`📋 Found ${expiredLogs.length} audit logs requiring manual review for deletion`);
    }
  }

  cleanupSecurityLogs(cutoffTime, autoDelete) {
    // Implementation for security log cleanup
    console.log(`🔍 Checking security logs for retention policy enforcement`);
  }

  cleanupUserData(cutoffTime, autoDelete) {
    // Implementation for user data cleanup
    console.log(`👤 Checking user data for retention policy enforcement`);
  }

  // Utility methods
  generateAuditId() {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  generateReportId() {
    return `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  isAuthorizedAdmin(userId) {
    const userRole = rbacManager.getUserRole(userId);
    return userRole && ['admin', 'super_admin'].includes(userRole.name);
  }

  hasDataExportPermission(userId) {
    return rbacManager.hasPermission(userId, 'admin.data.export');
  }

  exceedsTransactionLimits(details) {
    // Check if transaction exceeds regulatory limits
    return details.amount > 1000000; // TZS 1M limit for demo
  }

  lockUserAccount(userId, reason) {
    console.log(`🔒 Locking user account ${userId}: ${reason}`);
    // Implementation for account locking
  }

  notifyComplianceTeam(severity, violation, auditEntry) {
    console.log(`📧 Notifying compliance team: ${severity} violation`);
    // Implementation for compliance team notification
  }

  flagForComplianceReview(auditEntry, violation) {
    console.log(`🏷️ Flagging for compliance review: ${violation.rule}`);
    // Implementation for compliance review flagging
  }

  // Public API methods
  getComplianceStatus() {
    const latestResults = this.performComplianceCheck();
    return this.generateComplianceSummary(latestResults);
  }

  getAuditTrail(userId, limit = 100) {
    const userTrail = this.auditTrails.get(userId) || [];
    return userTrail.slice(-limit).reverse();
  }

  getComplianceReports(limit = 10) {
    return Array.from(this.complianceReports.values())
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, limit);
  }

  exportAuditData(startDate, endDate, format = 'json') {
    const start = new Date(startDate).getTime();
    const end = new Date(endDate).getTime();

    const auditData = Array.from(this.auditLogs.values())
      .filter(log => {
        const logTime = new Date(log.timestamp).getTime();
        return logTime >= start && logTime <= end;
      });

    if (format === 'csv') {
      return this.convertToCSV(auditData);
    }

    return auditData;
  }

  convertToCSV(data) {
    if (data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => JSON.stringify(row[header])).join(','))
    ].join('\n');

    return csvContent;
  }
}

// Create singleton instance
const complianceAuditService = new ComplianceAuditService();

export default complianceAuditService;
