// ProChat Notification Settings Component
// Allow users to control which notifications they want to receive

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Switch,
  FormControlLabel,
  FormGroup,
  Divider,
  Alert,
  Button,
  Slider,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TimePicker,
} from '@mui/material';
import {
  Notifications,
  NotificationsOff,
  MonetizationOn,
  EmojiEvents,
  People,
  Store,
  Security,
  Settings,
  Schedule,
  VolumeUp,
  ExpandMore,
  Save,
  Refresh,
} from '@mui/icons-material';

import { useAuth } from '../../contexts/AuthContext';
import smartNotificationService from '../../services/smartNotifications';

const NotificationSettings = () => {
  const { user } = useAuth();
  const [preferences, setPreferences] = useState({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testNotification, setTestNotification] = useState(false);

  useEffect(() => {
    loadNotificationPreferences();
  }, [user]);

  const loadNotificationPreferences = async () => {
    try {
      setLoading(true);
      const userPrefs = smartNotificationService.getUserPreferences(user.id);
      setPreferences(userPrefs);
    } catch (error) {
      console.error('Failed to load notification preferences:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePreferenceChange = (key, value) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleQuietHoursChange = (type, value) => {
    setPreferences(prev => ({
      ...prev,
      quiet_hours: {
        ...prev.quiet_hours,
        [type]: value,
      },
    }));
  };

  const savePreferences = async () => {
    try {
      setSaving(true);
      await smartNotificationService.updateUserPreferences(user.id, preferences);
      alert('Mipangilio ya notification imehifadhiwa!');
    } catch (error) {
      alert('Kosa: ' + error.message);
    } finally {
      setSaving(false);
    }
  };

  const sendTestNotification = async () => {
    try {
      setTestNotification(true);
      await smartNotificationService.testNotification(user.id, 'money_earned');
      alert('Test notification imetumwa!');
    } catch (error) {
      alert('Kosa: ' + error.message);
    } finally {
      setTestNotification(false);
    }
  };

  const NotificationGroup = ({ title, icon, children, description }) => (
    <Accordion defaultExpanded>
      <AccordionSummary expandIcon={<ExpandMore />}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {icon}
          <Box sx={{ ml: 2 }}>
            <Typography variant="h6">{title}</Typography>
            {description && (
              <Typography variant="body2" color="text.secondary">
                {description}
              </Typography>
            )}
          </Box>
        </Box>
      </AccordionSummary>
      <AccordionDetails>
        <FormGroup>
          {children}
        </FormGroup>
      </AccordionDetails>
    </Accordion>
  );

  const NotificationSwitch = ({ label, description, prefKey, disabled = false, automatic = false }) => (
    <Box sx={{ mb: 2 }}>
      <FormControlLabel
        control={
          <Switch
            checked={preferences[prefKey] || false}
            onChange={(e) => handlePreferenceChange(prefKey, e.target.checked)}
            disabled={disabled || automatic}
          />
        }
        label={
          <Box>
            <Typography variant="body1">
              {label}
              {automatic && <Chip label="Auto" color="primary" size="small" sx={{ ml: 1 }} />}
            </Typography>
            {description && (
              <Typography variant="caption" color="text.secondary">
                {description}
              </Typography>
            )}
          </Box>
        }
      />
    </Box>
  );

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Inapakia mipangilio...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Notifications sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          Mipangilio ya Notification
        </Typography>
        <Button
          variant="outlined"
          startIcon={<VolumeUp />}
          onClick={sendTestNotification}
          disabled={testNotification}
          sx={{ mr: 2 }}
        >
          Test Notification
        </Button>
        <Button
          variant="contained"
          startIcon={<Save />}
          onClick={savePreferences}
          disabled={saving}
        >
          Hifadhi
        </Button>
      </Box>

      {/* Info Alert */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          💡 <strong>Automatic notifications</strong> (zenye alama ya "Auto") haziwezi kuzimwa kwa usalama wako.
          Hizi ni pamoja na notification za pesa, login, na usalama.
        </Typography>
      </Alert>

      {/* Financial Notifications */}
      <NotificationGroup
        title="💰 Notification za Kifedha"
        icon={<MonetizationOn color="success" />}
        description="Notification muhimu za pesa - haziwezi kuzimwa"
      >
        <NotificationSwitch
          label="Umepata Pesa"
          description="Notification wakati umepata pesa kutoka posts, missions, n.k."
          prefKey="money_earned"
          automatic={true}
        />
        
        <NotificationSwitch
          label="Umetuma Pesa"
          description="Notification wakati umetuma pesa kwa mtu"
          prefKey="money_sent"
          automatic={true}
        />
        
        <NotificationSwitch
          label="Umepokea Pesa"
          description="Notification wakati umepokea pesa kutoka mtu"
          prefKey="money_received"
          automatic={true}
        />
        
        <NotificationSwitch
          label="Balance ya Chini"
          description="Notification wakati balance yako ni chini ya TZS 1,000"
          prefKey="wallet_low_balance"
          automatic={true}
        />
        
        <NotificationSwitch
          label="Cashout Imekamilika"
          description="Notification wakati cashout yako imekamilika"
          prefKey="cashout_processed"
          automatic={true}
        />
      </NotificationGroup>

      {/* Engagement Notifications */}
      <NotificationGroup
        title="🎯 Notification za Ushiriki"
        icon={<EmojiEvents color="warning" />}
        description="Notification za missions, levels, na achievements"
      >
        <NotificationSwitch
          label="Mission Reminders"
          description="Kukumbusha missions za leo"
          prefKey="mission_reminders"
        />
        
        <NotificationSwitch
          label="Streak Alerts"
          description="Notification za login streaks na bonuses"
          prefKey="streak_alerts"
        />
        
        <NotificationSwitch
          label="Level Up Alerts"
          description="Notification wakati umepanda level"
          prefKey="level_up_alerts"
        />
        
        <NotificationSwitch
          label="Badge Earned"
          description="Notification wakati umepata badge mpya"
          prefKey="badge_earned"
        />
      </NotificationGroup>

      {/* Social Notifications */}
      <NotificationGroup
        title="👥 Notification za Kijamii"
        icon={<People color="info" />}
        description="Notification za likes, comments, na followers"
      >
        <NotificationSwitch
          label="Post Liked"
          description="Notification wakati mtu amependa post yako"
          prefKey="post_liked"
        />
        
        <NotificationSwitch
          label="Post Commented"
          description="Notification wakati mtu amecomment post yako"
          prefKey="post_commented"
        />
        
        <NotificationSwitch
          label="Post Shared"
          description="Notification wakati mtu ameshare post yako"
          prefKey="post_shared"
        />
        
        <NotificationSwitch
          label="New Follower"
          description="Notification wakati umepata follower mpya"
          prefKey="new_follower"
        />
        
        <NotificationSwitch
          label="Friend Activity"
          description="Notification za shughuli za marafiki"
          prefKey="friend_activity"
        />
      </NotificationGroup>

      {/* Marketplace Notifications */}
      <NotificationGroup
        title="🛒 Notification za Marketplace"
        icon={<Store color="secondary" />}
        description="Notification za biashara na marketplace"
      >
        <NotificationSwitch
          label="Product Sold"
          description="Notification wakati bidhaa yako imeuzwa"
          prefKey="product_sold"
        />
        
        <NotificationSwitch
          label="Product Liked"
          description="Notification wakati mtu amependa bidhaa yako"
          prefKey="product_liked"
        />
        
        <NotificationSwitch
          label="New Order"
          description="Notification za order mpya"
          prefKey="new_order"
        />
        
        <NotificationSwitch
          label="Price Drop"
          description="Notification za bei zilizoshuka"
          prefKey="price_drop"
        />
      </NotificationGroup>

      {/* Promotional Notifications */}
      <NotificationGroup
        title="🎁 Notification za Matangazo"
        icon={<Settings color="primary" />}
        description="Notification za offers na events"
      >
        <NotificationSwitch
          label="Surprise Rewards"
          description="Notification za zawadi za ghafla"
          prefKey="surprise_rewards"
        />
        
        <NotificationSwitch
          label="Special Offers"
          description="Notification za offers maalum"
          prefKey="special_offers"
        />
        
        <NotificationSwitch
          label="Event Invitations"
          description="Notification za mialiko ya events"
          prefKey="event_invitations"
        />
        
        <NotificationSwitch
          label="App Updates"
          description="Notification za updates za app"
          prefKey="app_updates"
        />
      </NotificationGroup>

      {/* System Notifications */}
      <NotificationGroup
        title="🔒 Notification za Mfumo"
        icon={<Security color="error" />}
        description="Notification muhimu za usalama na mfumo"
      >
        <NotificationSwitch
          label="Security Alerts"
          description="Notification za usalama - haziwezi kuzimwa"
          prefKey="security_alerts"
          automatic={true}
        />
        
        <NotificationSwitch
          label="System Maintenance"
          description="Notification za matengenezo ya mfumo"
          prefKey="system_maintenance"
          automatic={true}
        />
      </NotificationGroup>

      {/* Timing Settings */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            ⏰ Mipangilio ya Muda
          </Typography>
          
          <Box sx={{ mb: 3 }}>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Masaa ya Kimya (Quiet Hours)
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Hautapokea notification katika masaa haya isipokuwa za dharura
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <FormControl size="small">
                <InputLabel>Kuanzia</InputLabel>
                <Select
                  value={preferences.quiet_hours?.start || 22}
                  onChange={(e) => handleQuietHoursChange('start', e.target.value)}
                  label="Kuanzia"
                >
                  {Array.from({ length: 24 }, (_, i) => (
                    <MenuItem key={i} value={i}>
                      {i.toString().padStart(2, '0')}:00
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              
              <Typography>hadi</Typography>
              
              <FormControl size="small">
                <InputLabel>Kuishia</InputLabel>
                <Select
                  value={preferences.quiet_hours?.end || 7}
                  onChange={(e) => handleQuietHoursChange('end', e.target.value)}
                  label="Kuishia"
                >
                  {Array.from({ length: 24 }, (_, i) => (
                    <MenuItem key={i} value={i}>
                      {i.toString().padStart(2, '0')}:00
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </Box>

          <Divider sx={{ my: 2 }} />

          <Box sx={{ mb: 3 }}>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Frequency ya Notification
            </Typography>
            <FormControl fullWidth size="small">
              <InputLabel>Frequency</InputLabel>
              <Select
                value={preferences.frequency || 'optimal'}
                onChange={(e) => handlePreferenceChange('frequency', e.target.value)}
                label="Frequency"
              >
                <MenuItem value="low">Chache (Low) - Notification muhimu tu</MenuItem>
                <MenuItem value="optimal">Wastani (Optimal) - Balanced</MenuItem>
                <MenuItem value="high">Nyingi (High) - Notification zote</MenuItem>
              </Select>
            </FormControl>
          </Box>

          <Box>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Mtindo wa Motivation
            </Typography>
            <FormControl fullWidth size="small">
              <InputLabel>Mtindo</InputLabel>
              <Select
                value={preferences.motivation_style || 'balanced'}
                onChange={(e) => handlePreferenceChange('motivation_style', e.target.value)}
                label="Mtindo"
              >
                <MenuItem value="gentle">Pole Pole (Gentle) - Notification za upole</MenuItem>
                <MenuItem value="balanced">Wastani (Balanced) - Kati kati</MenuItem>
                <MenuItem value="aggressive">Kali (Aggressive) - Notification za haraka</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </CardContent>
      </Card>

      {/* Delivery Preferences */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            📱 Njia za Kupokea Notification
          </Typography>
          
          <FormGroup>
            <NotificationSwitch
              label="Push Notifications"
              description="Notification kwenye simu yako"
              prefKey="push_notifications"
            />
            
            <NotificationSwitch
              label="In-App Notifications"
              description="Notification ndani ya app"
              prefKey="in_app_notifications"
            />
            
            <NotificationSwitch
              label="Email Notifications"
              description="Notification kwenye email"
              prefKey="email_notifications"
            />
            
            <NotificationSwitch
              label="SMS Notifications"
              description="Notification kwenye SMS (ada inaweza kutoza)"
              prefKey="sms_notifications"
            />
          </FormGroup>
        </CardContent>
      </Card>

      {/* Save Button */}
      <Box sx={{ mt: 3, textAlign: 'center' }}>
        <Button
          variant="contained"
          size="large"
          startIcon={<Save />}
          onClick={savePreferences}
          disabled={saving}
          sx={{ minWidth: 200 }}
        >
          {saving ? 'Inahifadhi...' : 'Hifadhi Mipangilio'}
        </Button>
      </Box>
    </Box>
  );
};

export default NotificationSettings;
