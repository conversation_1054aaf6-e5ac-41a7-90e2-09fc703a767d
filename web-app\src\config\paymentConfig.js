// ProChat Payment Configuration
// Complete payment gateway configuration for all supported providers

export const PAYMENT_CONFIG = {
  // 🌍 GENERAL SETTINGS
  defaultCurrency: 'TZS',
  supportedCurrencies: ['TZS', 'USD', 'EUR', 'KES', 'UGX', 'RWF', 'BTC', 'USDT'],
  
  // 📱 MOBILE MONEY CONFIGURATION
  MOBILE_MONEY: {
    // M-Pesa Configuration
    MPESA: {
      enabled: true,
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
      baseUrl: {
        sandbox: 'https://openapi.m-pesa.com/sandbox/ipg/v2',
        production: 'https://openapi.m-pesa.com/ipg/v2',
      },
      credentials: {
        apiKey: process.env.MPESA_API_KEY,
        publicKey: process.env.MPESA_PUBLIC_KEY,
        serviceProviderCode: process.env.MPESA_SERVICE_PROVIDER_CODE,
        initiatorIdentifier: process.env.MPESA_INITIATOR_IDENTIFIER,
        securityCredential: process.env.MPESA_SECURITY_CREDENTIAL,
      },
      endpoints: {
        c2bPayment: '/ipg/v2/vodacomTZN/c2bPayment/singleStage/',
        b2cPayment: '/ipg/v2/vodacomTZN/b2cPayment/',
        queryTransactionStatus: '/ipg/v2/vodacomTZN/queryTransactionStatus/',
        reversal: '/ipg/v2/vodacomTZN/reversal/',
      },
      limits: {
        minimum: 100, // TZS 100
        maximum: ********, // TZS 10M
        daily: 3000000, // TZS 3M per day
        monthly: ********, // TZS 30M per month
      },
      fees: {
        percentage: 0.02, // 2%
        fixed: 0,
        minimum: 100,
        maximum: 10000,
      },
      timeout: 30000, // 30 seconds
    },

    // Airtel Money Configuration
    AIRTEL_MONEY: {
      enabled: true,
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'staging',
      baseUrl: {
        staging: 'https://openapiuat.airtel.africa',
        production: 'https://openapi.airtel.africa',
      },
      credentials: {
        clientId: process.env.AIRTEL_CLIENT_ID,
        clientSecret: process.env.AIRTEL_CLIENT_SECRET,
        apiKey: process.env.AIRTEL_API_KEY,
        xCountry: 'TZ',
        xCurrency: 'TZS',
      },
      endpoints: {
        auth: '/auth/oauth2/token',
        collection: '/merchant/v1/payments/',
        disbursement: '/standard/v1/disbursements/',
        transactionStatus: '/standard/v1/payments/',
        balance: '/standard/v1/users/balance',
      },
      limits: {
        minimum: 100,
        maximum: 5000000,
        daily: 2000000,
        monthly: 20000000,
      },
      fees: {
        percentage: 0.018, // 1.8%
        fixed: 0,
        minimum: 100,
        maximum: 8000,
      },
      timeout: 30000,
    },

    // Tigo Pesa (MIXX BY YAS) Configuration
    TIGO_PESA: {
      enabled: true,
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
      baseUrl: {
        sandbox: 'https://sandbox.tigo.co.tz/v1',
        production: 'https://api.tigo.co.tz/v1',
      },
      credentials: {
        username: process.env.TIGO_USERNAME,
        password: process.env.TIGO_PASSWORD,
        apiKey: process.env.TIGO_API_KEY,
        brandId: process.env.TIGO_BRAND_ID,
        accountMsisdn: process.env.TIGO_ACCOUNT_MSISDN,
      },
      endpoints: {
        payment: '/payment/request',
        disbursement: '/disbursement/request',
        status: '/transaction/status',
        balance: '/account/balance',
      },
      limits: {
        minimum: 100,
        maximum: 3000000,
        daily: 1500000,
        monthly: ********,
      },
      fees: {
        percentage: 0.019, // 1.9%
        fixed: 0,
        minimum: 100,
        maximum: 6000,
      },
      timeout: 30000,
    },

    // HaloPesa Configuration
    HALOPESA: {
      enabled: true,
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
      baseUrl: {
        sandbox: 'https://sandbox.halopesa.co.tz/api/v1',
        production: 'https://api.halopesa.co.tz/api/v1',
      },
      credentials: {
        merchantId: process.env.HALOPESA_MERCHANT_ID,
        apiKey: process.env.HALOPESA_API_KEY,
        secretKey: process.env.HALOPESA_SECRET_KEY,
        callbackUrl: process.env.HALOPESA_CALLBACK_URL,
      },
      endpoints: {
        payment: '/payment/request',
        disbursement: '/disbursement/request',
        status: '/transaction/query',
        balance: '/merchant/balance',
      },
      limits: {
        minimum: 100,
        maximum: 2000000,
        daily: 1000000,
        monthly: ********,
      },
      fees: {
        percentage: 0.02, // 2%
        fixed: 0,
        minimum: 100,
        maximum: 5000,
      },
      timeout: 30000,
    },

    // AzamPesa Configuration
    AZAMPESA: {
      enabled: true,
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
      baseUrl: {
        sandbox: 'https://sandbox.azampesa.co.tz/api/v1',
        production: 'https://api.azampesa.co.tz/api/v1',
      },
      credentials: {
        merchantCode: process.env.AZAMPESA_MERCHANT_CODE,
        apiKey: process.env.AZAMPESA_API_KEY,
        secretKey: process.env.AZAMPESA_SECRET_KEY,
        callbackUrl: process.env.AZAMPESA_CALLBACK_URL,
      },
      endpoints: {
        payment: '/payment/request',
        disbursement: '/disbursement/request',
        status: '/transaction/status',
        balance: '/merchant/balance',
      },
      limits: {
        minimum: 100,
        maximum: 1500000,
        daily: 800000,
        monthly: 8000000,
      },
      fees: {
        percentage: 0.021, // 2.1%
        fixed: 0,
        minimum: 100,
        maximum: 4000,
      },
      timeout: 30000,
    },

    // NARA Configuration
    NARA: {
      enabled: true,
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
      baseUrl: {
        sandbox: 'https://sandbox.nara.co.tz/api/v1',
        production: 'https://api.nara.co.tz/api/v1',
      },
      credentials: {
        clientId: process.env.NARA_CLIENT_ID,
        clientSecret: process.env.NARA_CLIENT_SECRET,
        apiKey: process.env.NARA_API_KEY,
        merchantId: process.env.NARA_MERCHANT_ID,
      },
      endpoints: {
        auth: '/auth/token',
        payment: '/payment/request',
        disbursement: '/disbursement/request',
        status: '/transaction/status',
        balance: '/account/balance',
      },
      limits: {
        minimum: 100,
        maximum: 2500000,
        daily: 1200000,
        monthly: ********,
      },
      fees: {
        percentage: 0.017, // 1.7%
        fixed: 0,
        minimum: 100,
        maximum: 5500,
      },
      timeout: 30000,
    },
  },

  // 🏦 BANKING CONFIGURATION
  BANKING: {
    // CRDB Bank Configuration
    CRDB_BANK: {
      enabled: true,
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
      baseUrl: {
        sandbox: 'https://sandbox.crdbbank.co.tz/api/v1',
        production: 'https://api.crdbbank.co.tz/api/v1',
      },
      credentials: {
        bankCode: process.env.CRDB_BANK_CODE,
        apiKey: process.env.CRDB_API_KEY,
        secretKey: process.env.CRDB_SECRET_KEY,
        merchantId: process.env.CRDB_MERCHANT_ID,
        accountNumber: process.env.CRDB_ACCOUNT_NUMBER,
      },
      endpoints: {
        payment: '/payments/request',
        transfer: '/transfers/request',
        status: '/transactions/status',
        balance: '/accounts/balance',
        verification: '/accounts/verify',
      },
      limits: {
        minimum: 500,
        maximum: ********,
        daily: ********,
        monthly: ********0,
      },
      fees: {
        percentage: 0.015, // 1.5%
        fixed: 500,
        minimum: 500,
        maximum: 15000,
      },
      timeout: 45000,
    },

    // NMB Bank Configuration
    NMB_BANK: {
      enabled: true,
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
      baseUrl: {
        sandbox: 'https://sandbox.nmbbank.co.tz/api/v1',
        production: 'https://api.nmbbank.co.tz/api/v1',
      },
      credentials: {
        bankCode: process.env.NMB_BANK_CODE,
        apiKey: process.env.NMB_API_KEY,
        secretKey: process.env.NMB_SECRET_KEY,
        merchantId: process.env.NMB_MERCHANT_ID,
        accountNumber: process.env.NMB_ACCOUNT_NUMBER,
      },
      endpoints: {
        payment: '/payments/request',
        transfer: '/transfers/request',
        status: '/transactions/status',
        balance: '/accounts/balance',
        verification: '/accounts/verify',
      },
      limits: {
        minimum: 500,
        maximum: ********,
        daily: 8000000,
        monthly: ********,
      },
      fees: {
        percentage: 0.016, // 1.6%
        fixed: 500,
        minimum: 500,
        maximum: 12000,
      },
      timeout: 45000,
    },

    // NBC Bank Configuration
    NBC_BANK: {
      enabled: true,
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
      baseUrl: {
        sandbox: 'https://sandbox.nbcbank.co.tz/api/v1',
        production: 'https://api.nbcbank.co.tz/api/v1',
      },
      credentials: {
        bankCode: process.env.NBC_BANK_CODE,
        apiKey: process.env.NBC_API_KEY,
        secretKey: process.env.NBC_SECRET_KEY,
        merchantId: process.env.NBC_MERCHANT_ID,
        accountNumber: process.env.NBC_ACCOUNT_NUMBER,
      },
      endpoints: {
        payment: '/payments/request',
        transfer: '/transfers/request',
        status: '/transactions/status',
        balance: '/accounts/balance',
        verification: '/accounts/verify',
      },
      limits: {
        minimum: 500,
        maximum: ********,
        daily: ********,
        monthly: *********,
      },
      fees: {
        percentage: 0.014, // 1.4%
        fixed: 500,
        minimum: 500,
        maximum: 10000,
      },
      timeout: 45000,
    },
  },

  // 💳 INTERNATIONAL CARDS CONFIGURATION
  CARDS: {
    // Visa Configuration
    VISA: {
      enabled: true,
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
      baseUrl: {
        sandbox: 'https://sandbox.api.visa.com',
        production: 'https://api.visa.com',
      },
      credentials: {
        userId: process.env.VISA_USER_ID,
        password: process.env.VISA_PASSWORD,
        keyId: process.env.VISA_KEY_ID,
        sharedSecret: process.env.VISA_SHARED_SECRET,
        certificatePath: process.env.VISA_CERTIFICATE_PATH,
        privateKeyPath: process.env.VISA_PRIVATE_KEY_PATH,
      },
      endpoints: {
        payment: '/visadirect/fundstransfer/v1/pushfundstransactions',
        pullFunds: '/visadirect/fundstransfer/v1/pullfundstransactions',
        reversal: '/visadirect/fundstransfer/v1/reversefundstransactions',
        status: '/visadirect/reports/v1/fundstransferattributes',
      },
      limits: {
        minimum: 100,
        maximum: ********0,
        daily: ********,
        monthly: ********0,
      },
      fees: {
        percentage: 0.029, // 2.9%
        international: 0.035, // 3.5%
        fixed: 0,
        minimum: 100,
        maximum: 50000,
      },
      timeout: 60000,
    },

    // Mastercard Configuration
    MASTERCARD: {
      enabled: true,
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
      baseUrl: {
        sandbox: 'https://sandbox.api.mastercard.com',
        production: 'https://api.mastercard.com',
      },
      credentials: {
        consumerKey: process.env.MASTERCARD_CONSUMER_KEY,
        privateKey: process.env.MASTERCARD_PRIVATE_KEY,
        keyAlias: process.env.MASTERCARD_KEY_ALIAS,
        keyPassword: process.env.MASTERCARD_KEY_PASSWORD,
        certificatePath: process.env.MASTERCARD_CERTIFICATE_PATH,
      },
      endpoints: {
        payment: '/send/v1/partners/{partnerId}/crossborder/transfer',
        status: '/send/v1/partners/{partnerId}/crossborder/transfer/{transferId}',
        quotes: '/send/v1/partners/{partnerId}/crossborder/transfer_quotes',
        rates: '/send/v1/partners/{partnerId}/crossborder/exchange_rates',
      },
      limits: {
        minimum: 100,
        maximum: ********0,
        daily: ********,
        monthly: ********0,
      },
      fees: {
        percentage: 0.028, // 2.8%
        international: 0.034, // 3.4%
        fixed: 0,
        minimum: 100,
        maximum: 50000,
      },
      timeout: 60000,
    },

    // Stripe Configuration
    STRIPE: {
      enabled: true,
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'test',
      baseUrl: 'https://api.stripe.com/v1',
      credentials: {
        publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
        secretKey: process.env.STRIPE_SECRET_KEY,
        webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
        connectClientId: process.env.STRIPE_CONNECT_CLIENT_ID,
      },
      endpoints: {
        paymentIntents: '/payment_intents',
        paymentMethods: '/payment_methods',
        customers: '/customers',
        transfers: '/transfers',
        refunds: '/refunds',
        webhooks: '/webhook_endpoints',
      },
      limits: {
        minimum: 50,
        maximum: 999999999,
        daily: ********0,
        monthly: ********00,
      },
      fees: {
        percentage: 0.029, // 2.9%
        international: 0.039, // 3.9%
        fixed: 30, // 30 cents
        minimum: 50,
        maximum: 100000,
      },
      timeout: 30000,
    },
  },

  // ₿ CRYPTOCURRENCY CONFIGURATION
  CRYPTO: {
    // Bitcoin Configuration
    BITCOIN: {
      enabled: true,
      network: process.env.NODE_ENV === 'production' ? 'mainnet' : 'testnet',
      baseUrl: {
        mainnet: 'https://api.blockchain.info/v1',
        testnet: 'https://testnet-api.blockchain.info/v1',
      },
      credentials: {
        apiKey: process.env.BLOCKCHAIN_API_KEY,
        walletId: process.env.BITCOIN_WALLET_ID,
        walletPassword: process.env.BITCOIN_WALLET_PASSWORD,
      },
      endpoints: {
        balance: '/balance',
        send: '/payment',
        receive: '/receive',
        transactions: '/transactions',
      },
      fees: {
        percentage: 0.001, // 0.1%
        networkFee: 'dynamic',
        minimum: 0.00001, // 1000 satoshis
      },
      confirmations: 3,
    },

    // USDT Configuration
    USDT: {
      enabled: true,
      network: 'ethereum',
      baseUrl: 'https://api.etherscan.io/api',
      credentials: {
        apiKey: process.env.ETHERSCAN_API_KEY,
        contractAddress: '******************************************', // USDT contract
        walletAddress: process.env.USDT_WALLET_ADDRESS,
        privateKey: process.env.USDT_PRIVATE_KEY,
      },
      endpoints: {
        balance: '?module=account&action=tokenbalance',
        transactions: '?module=account&action=tokentx',
        gasPrice: '?module=proxy&action=eth_gasPrice',
      },
      fees: {
        percentage: 0.001, // 0.1%
        gasLimit: 21000,
        gasPriceMultiplier: 1.2,
      },
      confirmations: 12,
    },
  },

  // 🔗 WEBHOOK CONFIGURATION
  WEBHOOKS: {
    baseUrl: process.env.WEBHOOK_BASE_URL || 'https://api.prochat.co.tz/webhooks',
    endpoints: {
      mpesa: '/mpesa',
      airtel: '/airtel',
      tigo: '/tigo',
      halopesa: '/halopesa',
      azampesa: '/azampesa',
      nara: '/nara',
      crdb: '/crdb',
      nmb: '/nmb',
      nbc: '/nbc',
      visa: '/visa',
      mastercard: '/mastercard',
      stripe: '/stripe',
      bitcoin: '/bitcoin',
      usdt: '/usdt',
    },
    security: {
      verifySignature: true,
      allowedIPs: [
        '*************/24', // M-Pesa IPs
        '*********/24',     // Airtel IPs
        '***********/16',   // Tigo IPs
        // Add other provider IPs
      ],
      timeout: 30000,
      retryAttempts: 3,
    },
  },

  // 🔒 SECURITY CONFIGURATION
  SECURITY: {
    encryption: {
      algorithm: 'aes-256-gcm',
      keyLength: 32,
      ivLength: 16,
      tagLength: 16,
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 100, // limit each IP to 100 requests per windowMs
    },
    fraudDetection: {
      enabled: true,
      maxAmountPerTransaction: ********, // TZS 10M
      maxTransactionsPerDay: 50,
      maxAmountPerDay: ********, // TZS 50M
      suspiciousPatterns: [
        'rapid_succession',
        'round_numbers',
        'unusual_hours',
        'new_device',
      ],
    },
    compliance: {
      kycRequired: true,
      amlChecks: true,
      sanctionScreening: true,
      reportingThreshold: 1000000, // TZS 1M
    },
  },

  // 📊 MONITORING CONFIGURATION
  MONITORING: {
    logging: {
      level: process.env.LOG_LEVEL || 'info',
      destinations: ['console', 'file', 'database'],
      retention: 90, // days
    },
    metrics: {
      enabled: true,
      interval: 60000, // 1 minute
      endpoints: [
        'transaction_count',
        'success_rate',
        'average_amount',
        'provider_performance',
        'error_rate',
      ],
    },
    alerts: {
      enabled: true,
      channels: ['email', 'sms', 'slack'],
      thresholds: {
        errorRate: 0.05, // 5%
        responseTime: 30000, // 30 seconds
        failureRate: 0.1, // 10%
      },
    },
  },
};

// Export individual configurations for easy access
export const MOBILE_MONEY_CONFIG = PAYMENT_CONFIG.MOBILE_MONEY;
export const BANKING_CONFIG = PAYMENT_CONFIG.BANKING;
export const CARDS_CONFIG = PAYMENT_CONFIG.CARDS;
export const CRYPTO_CONFIG = PAYMENT_CONFIG.CRYPTO;
export const WEBHOOK_CONFIG = PAYMENT_CONFIG.WEBHOOKS;
export const SECURITY_CONFIG = PAYMENT_CONFIG.SECURITY;
export const MONITORING_CONFIG = PAYMENT_CONFIG.MONITORING;

export default PAYMENT_CONFIG;
