{"version": 3, "sources": ["../../../../../src/run/ios/options/resolveNativeScheme.ts"], "sourcesContent": ["import { IOSConfig } from '@expo/config-plugins';\nimport chalk from 'chalk';\nimport path from 'path';\n\nimport * as Log from '../../../log';\nimport { CommandError } from '../../../utils/errors';\nimport { profile } from '../../../utils/profile';\nimport { selectAsync } from '../../../utils/prompts';\nimport { Options, ProjectInfo, XcodeConfiguration } from '../XcodeBuild.types';\n\nconst debug = require('debug')('expo:run:ios:options:resolveNativeScheme') as typeof console.log;\n\ntype NativeSchemeProps = {\n  name: string;\n  osType?: string;\n};\n\nexport async function resolveNativeSchemePropsAsync(\n  projectRoot: string,\n  options: Pick<Options, 'scheme' | 'configuration'>,\n  xcodeProject: ProjectInfo\n): Promise<NativeSchemeProps> {\n  return (\n    (await promptOrQueryNativeSchemeAsync(projectRoot, options)) ??\n    getDefaultNativeScheme(projectRoot, options, xcodeProject)\n  );\n}\n\n/** Resolve the native iOS build `scheme` for a given `configuration`. If the `scheme` isn't provided then the user will be prompted to select one. */\nexport async function promptOrQueryNativeSchemeAsync(\n  projectRoot: string,\n  { scheme, configuration }: { scheme?: string | boolean; configuration?: XcodeConfiguration }\n): Promise<NativeSchemeProps | null> {\n  const schemes = IOSConfig.BuildScheme.getRunnableSchemesFromXcodeproj(projectRoot, {\n    configuration,\n  });\n\n  if (!schemes.length) {\n    throw new CommandError('IOS_MALFORMED', 'No native iOS build schemes found');\n  }\n\n  if (scheme === true) {\n    if (schemes.length === 1) {\n      Log.log(`Auto selecting only available scheme: ${schemes[0].name}`);\n      return schemes[0];\n    }\n    const resolvedSchemeName = await selectAsync(\n      'Select a scheme',\n      schemes.map((value) => {\n        const isApp =\n          value.type === IOSConfig.Target.TargetType.APPLICATION && value.osType === 'iOS';\n        return {\n          value: value.name,\n          title: isApp ? chalk.bold(value.name) + chalk.gray(' (app)') : value.name,\n        };\n      }),\n      {\n        nonInteractiveHelp: `--scheme: argument must be provided with a string in non-interactive mode. Valid choices are: ${schemes.join(\n          ', '\n        )}`,\n      }\n    );\n    return schemes.find(({ name }) => resolvedSchemeName === name) ?? null;\n  }\n  // Attempt to match the schemes up so we can open the correct simulator\n  return scheme ? schemes.find(({ name }) => name === scheme) || { name: scheme } : null;\n}\n\nexport function getDefaultNativeScheme(\n  projectRoot: string,\n  options: Pick<Options, 'configuration'>,\n  xcodeProject: Pick<ProjectInfo, 'name'>\n): NativeSchemeProps {\n  // If the resolution failed then we should just use the first runnable scheme that\n  // matches the provided configuration.\n  const resolvedSchemes = profile(IOSConfig.BuildScheme.getRunnableSchemesFromXcodeproj)(\n    projectRoot,\n    {\n      configuration: options.configuration,\n    }\n  );\n\n  // If there are multiple schemes, then the default should be the application.\n  if (resolvedSchemes.length > 1) {\n    const scheme =\n      resolvedSchemes.find(({ type }) => type === IOSConfig.Target.TargetType.APPLICATION) ??\n      resolvedSchemes[0];\n    debug(`Using default scheme: ${scheme.name}`);\n    return scheme;\n  }\n\n  // If we couldn't find the scheme, then we'll guess at it,\n  // this is needed for cases where the native code hasn't been generated yet.\n  if (resolvedSchemes[0]) {\n    return resolvedSchemes[0];\n  }\n  return {\n    name: path.basename(xcodeProject.name, path.extname(xcodeProject.name)),\n  };\n}\n"], "names": ["getDefaultNativeScheme", "promptOrQueryNativeSchemeAsync", "resolveNativeSchemePropsAsync", "debug", "require", "projectRoot", "options", "xcodeProject", "scheme", "configuration", "schemes", "IOSConfig", "BuildScheme", "getRunnableSchemesFromXcodeproj", "length", "CommandError", "Log", "log", "name", "resolvedSchemeName", "selectAsync", "map", "value", "isApp", "type", "Target", "TargetType", "APPLICATION", "osType", "title", "chalk", "bold", "gray", "nonInteractiveHelp", "join", "find", "resolvedSchemes", "profile", "path", "basename", "extname"], "mappings": ";;;;;;;;;;;IAoEgBA,sBAAsB;eAAtBA;;IAvCMC,8BAA8B;eAA9BA;;IAZAC,6BAA6B;eAA7BA;;;;yBAjBI;;;;;;;gEACR;;;;;;;gEACD;;;;;;6DAEI;wBACQ;yBACL;yBACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG5B,MAAMC,QAAQC,QAAQ,SAAS;AAOxB,eAAeF,8BACpBG,WAAmB,EACnBC,OAAkD,EAClDC,YAAyB;IAEzB,OACE,AAAC,MAAMN,+BAA+BI,aAAaC,YACnDN,uBAAuBK,aAAaC,SAASC;AAEjD;AAGO,eAAeN,+BACpBI,WAAmB,EACnB,EAAEG,MAAM,EAAEC,aAAa,EAAqE;IAE5F,MAAMC,UAAUC,0BAAS,CAACC,WAAW,CAACC,+BAA+B,CAACR,aAAa;QACjFI;IACF;IAEA,IAAI,CAACC,QAAQI,MAAM,EAAE;QACnB,MAAM,IAAIC,oBAAY,CAAC,iBAAiB;IAC1C;IAEA,IAAIP,WAAW,MAAM;QACnB,IAAIE,QAAQI,MAAM,KAAK,GAAG;YACxBE,KAAIC,GAAG,CAAC,CAAC,sCAAsC,EAAEP,OAAO,CAAC,EAAE,CAACQ,IAAI,EAAE;YAClE,OAAOR,OAAO,CAAC,EAAE;QACnB;QACA,MAAMS,qBAAqB,MAAMC,IAAAA,oBAAW,EAC1C,mBACAV,QAAQW,GAAG,CAAC,CAACC;YACX,MAAMC,QACJD,MAAME,IAAI,KAAKb,0BAAS,CAACc,MAAM,CAACC,UAAU,CAACC,WAAW,IAAIL,MAAMM,MAAM,KAAK;YAC7E,OAAO;gBACLN,OAAOA,MAAMJ,IAAI;gBACjBW,OAAON,QAAQO,gBAAK,CAACC,IAAI,CAACT,MAAMJ,IAAI,IAAIY,gBAAK,CAACE,IAAI,CAAC,YAAYV,MAAMJ,IAAI;YAC3E;QACF,IACA;YACEe,oBAAoB,CAAC,8FAA8F,EAAEvB,QAAQwB,IAAI,CAC/H,OACC;QACL;QAEF,OAAOxB,QAAQyB,IAAI,CAAC,CAAC,EAAEjB,IAAI,EAAE,GAAKC,uBAAuBD,SAAS;IACpE;IACA,uEAAuE;IACvE,OAAOV,SAASE,QAAQyB,IAAI,CAAC,CAAC,EAAEjB,IAAI,EAAE,GAAKA,SAASV,WAAW;QAAEU,MAAMV;IAAO,IAAI;AACpF;AAEO,SAASR,uBACdK,WAAmB,EACnBC,OAAuC,EACvCC,YAAuC;IAEvC,kFAAkF;IAClF,sCAAsC;IACtC,MAAM6B,kBAAkBC,IAAAA,gBAAO,EAAC1B,0BAAS,CAACC,WAAW,CAACC,+BAA+B,EACnFR,aACA;QACEI,eAAeH,QAAQG,aAAa;IACtC;IAGF,6EAA6E;IAC7E,IAAI2B,gBAAgBtB,MAAM,GAAG,GAAG;QAC9B,MAAMN,SACJ4B,gBAAgBD,IAAI,CAAC,CAAC,EAAEX,IAAI,EAAE,GAAKA,SAASb,0BAAS,CAACc,MAAM,CAACC,UAAU,CAACC,WAAW,KACnFS,eAAe,CAAC,EAAE;QACpBjC,MAAM,CAAC,sBAAsB,EAAEK,OAAOU,IAAI,EAAE;QAC5C,OAAOV;IACT;IAEA,0DAA0D;IAC1D,4EAA4E;IAC5E,IAAI4B,eAAe,CAAC,EAAE,EAAE;QACtB,OAAOA,eAAe,CAAC,EAAE;IAC3B;IACA,OAAO;QACLlB,MAAMoB,eAAI,CAACC,QAAQ,CAAChC,aAAaW,IAAI,EAAEoB,eAAI,CAACE,OAAO,CAACjC,aAAaW,IAAI;IACvE;AACF"}