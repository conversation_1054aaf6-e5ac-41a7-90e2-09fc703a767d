// ProChat Engagement Dashboard Component
// Show users their earnings, missions, streaks, and rewards!

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Alert,
  IconButton,
  Tooltip,
  Badge,
  CircularProgress,
} from '@mui/material';
import {
  AccountBalance,
  TrendingUp,
  EmojiEvents,
  LocalFireDepartment,
  Assignment,
  Star,
  MonetizationOn,
  Notifications,
  Share,
  ThumbUp,
  Comment,
  Store,
  Gift,
  Timeline,
  Refresh,
} from '@mui/icons-material';

import { useAuth } from '../../contexts/AuthContext';
import { useDeviceDetection } from '../../hooks/useDeviceDetection';
import engagementRewardsService from '../../services/engagementRewards';
import smartNotificationService from '../../services/smartNotifications';

const EngagementDashboard = () => {
  const { user } = useAuth();
  const { isMobile } = useDeviceDetection();
  const [dashboardData, setDashboardData] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadDashboardData, 30000);
    return () => clearInterval(interval);
  }, [user]);

  const loadDashboardData = async () => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      
      const [
        userEngagement,
        userCoins,
        dailyMissions,
        engagementStats,
      ] = await Promise.all([
        engagementRewardsService.getUserEngagement(user.id),
        engagementRewardsService.getUserCoins(user.id),
        engagementRewardsService.getDailyMissions(user.id),
        engagementRewardsService.getEngagementStats(),
      ]);

      setDashboardData({
        engagement: userEngagement,
        coins: userCoins,
        missions: dailyMissions,
        stats: engagementStats,
      });

    } catch (error) {
      console.error('Failed to load engagement dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const handleMissionAction = async (mission) => {
    // Navigate to appropriate section based on mission type
    switch (mission.requirement.type) {
      case 'post_created':
        window.location.hash = '#/create-post';
        break;
      case 'post_liked':
        window.location.hash = '#/feed';
        break;
      case 'invite_friend':
        window.location.hash = '#/invite';
        break;
      case 'marketplace_visit':
        window.location.hash = '#/marketplace';
        break;
      default:
        window.location.hash = '#/';
    }
  };

  const handleCoinExchange = async () => {
    try {
      const result = await engagementRewardsService.exchangeCoinsForCash(
        user.id,
        dashboardData.coins
      );
      
      alert(`Umebadilisha ${dashboardData.coins} coins kuwa TZS ${result.cashAmount}!`);
      loadDashboardData(); // Refresh data
    } catch (error) {
      alert('Kosa: ' + error.message);
    }
  };

  const StatCard = ({ title, value, subtitle, icon, color = 'primary', action }) => (
    <Card sx={{ height: '100%', position: 'relative' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {icon}
          <Typography variant="h6" sx={{ ml: 1, flexGrow: 1 }}>
            {title}
          </Typography>
        </Box>
        <Typography variant="h3" color={`${color}.main`} sx={{ fontWeight: 'bold', mb: 1 }}>
          {value}
        </Typography>
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
        {action && (
          <Box sx={{ mt: 2 }}>
            {action}
          </Box>
        )}
      </CardContent>
    </Card>
  );

  const MissionCard = ({ mission }) => {
    const progress = (mission.progress / mission.requirement.count) * 100;
    const isCompleted = mission.completed;
    
    return (
      <Card sx={{ mb: 2, opacity: isCompleted ? 0.7 : 1 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Assignment color={isCompleted ? 'success' : 'primary'} sx={{ mr: 2 }} />
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="h6">
                {mission.name}
                {isCompleted && <Chip label="✅ Completed" color="success" size="small" sx={{ ml: 1 }} />}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {mission.description}
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'right' }}>
              <Typography variant="h6" color="primary.main">
                TZS {mission.reward.cash}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                + {mission.reward.coins} coins
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">
                Progress: {mission.progress}/{mission.requirement.count}
              </Typography>
              <Typography variant="body2">
                {Math.round(progress)}%
              </Typography>
            </Box>
            <LinearProgress 
              variant="determinate" 
              value={progress} 
              color={isCompleted ? 'success' : 'primary'}
              sx={{ height: 8, borderRadius: 4 }}
            />
          </Box>
          
          {!isCompleted && (
            <Button
              variant="contained"
              size="small"
              onClick={() => handleMissionAction(mission)}
              sx={{ mt: 1 }}
            >
              {mission.difficulty === 'easy' ? '🟢 Easy' : 
               mission.difficulty === 'medium' ? '🟡 Medium' : '🔴 Hard'} - Start Mission
            </Button>
          )}
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  const { engagement, coins, missions, stats } = dashboardData;

  return (
    <Box sx={{ p: isMobile ? 2 : 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <EmojiEvents sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          💰 Pata Pesa na ProChat!
        </Typography>
        <IconButton onClick={handleRefresh} disabled={refreshing}>
          <Refresh />
        </IconButton>
      </Box>

      {/* Welcome Message */}
      <Alert severity="success" sx={{ mb: 3 }}>
        <Typography variant="h6">
          🎉 Karibu kwenye mfumo wa kupata pesa kwa kutumia ProChat!
        </Typography>
        <Typography variant="body2">
          Pata hela kwa kila post, like, comment, na shughuli nyingine. Maliza missions upate zawadi kubwa!
        </Typography>
      </Alert>

      {/* Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Earnings"
            value={`TZS ${engagement?.totalCash?.toLocaleString() || 0}`}
            subtitle="Jumla ya pesa ulizopata"
            icon={<MonetizationOn color="success" />}
            color="success"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="ProChat Coins"
            value={coins?.toLocaleString() || 0}
            subtitle="Badilisha kuwa pesa"
            icon={<Star color="warning" />}
            color="warning"
            action={
              <Button 
                variant="outlined" 
                size="small" 
                onClick={handleCoinExchange}
                disabled={coins < 100}
              >
                Exchange Coins
              </Button>
            }
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Current Level"
            value={engagement?.level || 1}
            subtitle={`${engagement?.xp || 0} XP`}
            icon={<TrendingUp color="info" />}
            color="info"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Login Streak"
            value={`${engagement?.streaks?.login || 0} days`}
            subtitle="Endelea kuingia kila siku!"
            icon={<LocalFireDepartment color="error" />}
            color="error"
          />
        </Grid>
      </Grid>

      {/* Daily Missions */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                📋 Kazi za Leo (Daily Missions)
              </Typography>
              
              {missions?.length > 0 ? (
                missions.map((mission) => (
                  <MissionCard key={mission.id} mission={mission} />
                ))
              ) : (
                <Typography variant="body2" color="text.secondary">
                  Hakuna missions za leo. Rudi kesho!
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          {/* Quick Actions */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                ⚡ Quick Actions
              </Typography>
              
              <List>
                <ListItem button data-action="post_created">
                  <ListItemIcon>
                    <Badge badgeContent="TZS 100" color="success">
                      <Assignment />
                    </Badge>
                  </ListItemIcon>
                  <ListItemText 
                    primary="Create Post" 
                    secondary="Pata TZS 100 kwa post"
                  />
                </ListItem>
                
                <ListItem button data-action="invite_friend">
                  <ListItemIcon>
                    <Badge badgeContent="TZS 1000" color="success">
                      <Share />
                    </Badge>
                  </ListItemIcon>
                  <ListItemText 
                    primary="Invite Friend" 
                    secondary="Pata TZS 1,000 kwa invite"
                  />
                </ListItem>
                
                <ListItem button data-action="marketplace_visit">
                  <ListItemIcon>
                    <Badge badgeContent="TZS 100" color="success">
                      <Store />
                    </Badge>
                  </ListItemIcon>
                  <ListItemText 
                    primary="Visit Marketplace" 
                    secondary="Pata TZS 100 kwa visit"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>

          {/* Earning Tips */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                💡 Tips za Kupata Pesa
              </Typography>
              
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <ThumbUp color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Like posts za watu"
                    secondary="TZS 10 kwa like"
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <Comment color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Comment posts"
                    secondary="TZS 25 kwa comment"
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <LocalFireDepartment color="error" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Post viral (100+ likes)"
                    secondary="TZS 5,000 bonus!"
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <Gift color="success" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Login kila siku"
                    secondary="Streak bonuses!"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>

          {/* Badges & Achievements */}
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                🏆 Badges & Achievements
              </Typography>
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {engagement?.badges?.map((badge, index) => (
                  <Tooltip key={index} title={`Badge: ${badge}`}>
                    <Chip 
                      label={badge} 
                      color="primary" 
                      variant="outlined"
                      size="small"
                    />
                  </Tooltip>
                ))}
                
                {(!engagement?.badges || engagement.badges.length === 0) && (
                  <Typography variant="body2" color="text.secondary">
                    Bado hakuna badges. Endelea kutumia app upate badges!
                  </Typography>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Floating Action Button for Test Notification */}
      {process.env.NODE_ENV === 'development' && (
        <Button
          variant="contained"
          color="secondary"
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            borderRadius: '50%',
            minWidth: 56,
            height: 56,
          }}
          onClick={() => smartNotificationService.testNotification(user.id)}
        >
          <Notifications />
        </Button>
      )}
    </Box>
  );
};

export default EngagementDashboard;
