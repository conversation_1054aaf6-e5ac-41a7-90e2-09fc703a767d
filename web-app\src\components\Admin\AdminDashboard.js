// ProChat Advanced Admin Dashboard
// Military-grade administrative interface

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  Badge,
  LinearProgress,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Dashboard,
  Security,
  People,
  AccountBalance,
  Warning,
  CheckCircle,
  Error,
  Block,
  Visibility,
  Edit,
  Delete,
  Download,
  Upload,
  Refresh,
  Settings,
  Emergency,
  Shield,
  Lock,
  Unlock,
  Gavel,
  Assessment,
  Timeline,
  Notifications,
} from '@mui/icons-material';

import { useAuth } from '../../contexts/AuthContext';
import { useDeviceDetection } from '../../hooks/useDeviceDetection';
import adminSecurityService from '../../services/adminSecurity';
import fraudDetectionService from '../../services/fraudDetection';
import threatIntelligenceService from '../../services/threatIntelligence';
import complianceAuditService from '../../services/complianceAudit';
import walletSecurityService from '../../services/walletSecurity';
import { emergencyModeManager } from '../../config/bankingSecurity';

const AdminDashboard = () => {
  const { user } = useAuth();
  const { isMobile } = useDeviceDetection();
  const [currentTab, setCurrentTab] = useState(0);
  const [dashboardData, setDashboardData] = useState({});
  const [loading, setLoading] = useState(true);
  const [privilegedOpDialog, setPrivilegedOpDialog] = useState(false);
  const [selectedOperation, setSelectedOperation] = useState('');
  const [operationParams, setOperationParams] = useState({});
  const [alerts, setAlerts] = useState([]);

  useEffect(() => {
    loadDashboardData();
    const interval = setInterval(loadDashboardData, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      const [
        adminStats,
        fraudStats,
        threatStats,
        complianceStatus,
        walletStats,
        emergencyStatus,
        activeSessions,
        recentAudit,
      ] = await Promise.all([
        adminSecurityService.getAdminSecurityStats(),
        fraudDetectionService.getSystemStats(),
        threatIntelligenceService.getThreatStatistics(),
        complianceAuditService.getComplianceStatus(),
        walletSecurityService.getSecurityStats(),
        emergencyModeManager.getEmergencyStatus(),
        adminSecurityService.getActiveAdminSessions(),
        adminSecurityService.getAdminAuditTrail(null, 50),
      ]);

      setDashboardData({
        admin: adminStats,
        fraud: fraudStats,
        threats: threatStats,
        compliance: complianceStatus,
        wallet: walletStats,
        emergency: emergencyStatus,
        sessions: activeSessions,
        audit: recentAudit,
      });

      // Generate alerts
      generateAlerts({
        fraud: fraudStats,
        threats: threatStats,
        compliance: complianceStatus,
        emergency: emergencyStatus,
      });

    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateAlerts = (data) => {
    const newAlerts = [];

    // Emergency mode alert
    if (data.emergency.active) {
      newAlerts.push({
        id: 'emergency',
        type: 'error',
        title: 'Emergency Mode Active',
        message: data.emergency.reason,
        action: 'Manage Emergency Mode',
      });
    }

    // High threat activity
    if (data.threats.criticalThreats > 0) {
      newAlerts.push({
        id: 'threats',
        type: 'error',
        title: 'Critical Threats Detected',
        message: `${data.threats.criticalThreats} critical security threats require immediate attention`,
        action: 'View Threats',
      });
    }

    // Fraud detection alerts
    if (data.fraud.blockedUsers > 10) {
      newAlerts.push({
        id: 'fraud',
        type: 'warning',
        title: 'High Fraud Activity',
        message: `${data.fraud.blockedUsers} users blocked due to suspicious activity`,
        action: 'Review Blocked Users',
      });
    }

    // Compliance violations
    if (data.compliance.critical_violations > 0) {
      newAlerts.push({
        id: 'compliance',
        type: 'error',
        title: 'Compliance Violations',
        message: `${data.compliance.critical_violations} critical compliance violations detected`,
        action: 'Review Violations',
      });
    }

    setAlerts(newAlerts);
  };

  const handlePrivilegedOperation = async (operationId, params) => {
    try {
      const sessionId = localStorage.getItem('admin_session_id');
      const result = await adminSecurityService.executePrivilegedOperation(
        sessionId,
        operationId,
        params
      );

      if (result.success) {
        alert('Operation completed successfully');
        loadDashboardData(); // Refresh data
      } else if (result.approvalRequired) {
        alert('Operation requires approval or dual authorization');
      } else {
        alert('Operation failed: ' + result.errors.join(', '));
      }
    } catch (error) {
      alert('Operation failed: ' + error.message);
    }
    
    setPrivilegedOpDialog(false);
    setSelectedOperation('');
    setOperationParams({});
  };

  const StatCard = ({ title, value, icon, color = 'primary', subtitle }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {icon}
          <Typography variant="h6" sx={{ ml: 1, flexGrow: 1 }}>
            {title}
          </Typography>
        </Box>
        <Typography variant="h3" color={`${color}.main`} sx={{ fontWeight: 'bold' }}>
          {value}
        </Typography>
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );

  const AlertCard = ({ alert }) => (
    <Alert
      severity={alert.type}
      sx={{ mb: 2 }}
      action={
        <Button color="inherit" size="small">
          {alert.action}
        </Button>
      }
    >
      <Typography variant="subtitle2">{alert.title}</Typography>
      <Typography variant="body2">{alert.message}</Typography>
    </Alert>
  );

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" sx={{ mb: 2 }}>Admin Dashboard</Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: isMobile ? 2 : 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Dashboard sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          ProChat Admin Dashboard
        </Typography>
        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={loadDashboardData}
          size="small"
        >
          Refresh
        </Button>
      </Box>

      {/* Alerts Section */}
      {alerts.length > 0 && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            🚨 Critical Alerts
          </Typography>
          {alerts.map(alert => (
            <AlertCard key={alert.id} alert={alert} />
          ))}
        </Box>
      )}

      {/* Navigation Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab label="Overview" icon={<Dashboard />} />
          <Tab label="Security" icon={<Security />} />
          <Tab label="Users" icon={<People />} />
          <Tab label="Wallet" icon={<AccountBalance />} />
          <Tab label="Compliance" icon={<Gavel />} />
          <Tab label="Operations" icon={<Settings />} />
        </Tabs>
      </Box>

      {/* Tab Content */}
      {currentTab === 0 && (
        <Grid container spacing={3}>
          {/* System Overview */}
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Active Users"
              value={dashboardData.fraud?.totalUsers || 0}
              icon={<People color="primary" />}
              subtitle="Currently online"
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Security Score"
              value="98%"
              icon={<Shield color="success" />}
              color="success"
              subtitle="System security level"
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Active Threats"
              value={dashboardData.threats?.activeThreats || 0}
              icon={<Warning color="error" />}
              color="error"
              subtitle="Requires attention"
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Compliance Score"
              value={`${Math.round(dashboardData.compliance?.overall_score || 0)}%`}
              icon={<Gavel color="info" />}
              color="info"
              subtitle="Regulatory compliance"
            />
          </Grid>

          {/* Emergency Mode Status */}
          {dashboardData.emergency?.active && (
            <Grid item xs={12}>
              <Card sx={{ bgcolor: 'error.light', color: 'error.contrastText' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Emergency sx={{ fontSize: 40, mr: 2 }} />
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="h6">
                        🚨 EMERGENCY MODE ACTIVE
                      </Typography>
                      <Typography variant="body1">
                        {dashboardData.emergency.reason}
                      </Typography>
                      <Typography variant="caption">
                        Active for {Math.round(dashboardData.emergency.duration / 1000 / 60)} minutes
                      </Typography>
                    </Box>
                    <Button
                      variant="contained"
                      color="inherit"
                      onClick={() => setPrivilegedOpDialog(true)}
                    >
                      Manage
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          )}

          {/* Recent Admin Activity */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Recent Admin Activity
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Time</TableCell>
                        <TableCell>Admin</TableCell>
                        <TableCell>Action</TableCell>
                        <TableCell>Status</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {dashboardData.audit?.slice(0, 10).map((entry) => (
                        <TableRow key={entry.id}>
                          <TableCell>
                            {new Date(entry.timestamp).toLocaleTimeString()}
                          </TableCell>
                          <TableCell>{entry.username}</TableCell>
                          <TableCell>{entry.type}</TableCell>
                          <TableCell>
                            <Chip
                              label="Success"
                              color="success"
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Active Admin Sessions */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Active Admin Sessions
                </Typography>
                <List>
                  {dashboardData.sessions?.map((session) => (
                    <ListItem key={session.sessionId}>
                      <ListItemIcon>
                        <Lock color="success" />
                      </ListItemIcon>
                      <ListItemText
                        primary={session.username}
                        secondary={`Since ${new Date(session.startTime).toLocaleTimeString()}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {currentTab === 1 && (
        <Grid container spacing={3}>
          {/* Security Metrics */}
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Blocked Users"
              value={dashboardData.fraud?.blockedUsers || 0}
              icon={<Block color="error" />}
              color="error"
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Suspicious Activities"
              value={dashboardData.fraud?.suspiciousActivities || 0}
              icon={<Warning color="warning" />}
              color="warning"
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Critical Threats"
              value={dashboardData.threats?.criticalThreats || 0}
              icon={<Error color="error" />}
              color="error"
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Security Rules"
              value={dashboardData.fraud?.activeRules || 0}
              icon={<Shield color="success" />}
              color="success"
            />
          </Grid>

          {/* Threat Intelligence */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Threat Intelligence
                </Typography>
                <Grid container spacing={2}>
                  {Object.entries(dashboardData.threats?.threatTypes || {}).map(([type, count]) => (
                    <Grid item xs={12} sm={6} md={4} key={type}>
                      <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                        <Typography variant="subtitle2">{type.replace('_', ' ').toUpperCase()}</Typography>
                        <Typography variant="h4" color="error.main">{count}</Typography>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Privileged Operations Dialog */}
      <Dialog
        open={privilegedOpDialog}
        onClose={() => setPrivilegedOpDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Privileged Operations</DialogTitle>
        <DialogContent>
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Operation</InputLabel>
            <Select
              value={selectedOperation}
              onChange={(e) => setSelectedOperation(e.target.value)}
            >
              <MenuItem value="system_shutdown">System Shutdown</MenuItem>
              <MenuItem value="user_data_export">User Data Export</MenuItem>
              <MenuItem value="financial_adjustment">Financial Adjustment</MenuItem>
              <MenuItem value="security_config">Security Configuration</MenuItem>
              <MenuItem value="user_suspension">User Suspension</MenuItem>
              <MenuItem value="data_deletion">Data Deletion</MenuItem>
            </Select>
          </FormControl>

          {selectedOperation === 'user_suspension' && (
            <>
              <TextField
                fullWidth
                label="User ID"
                value={operationParams.userId || ''}
                onChange={(e) => setOperationParams({
                  ...operationParams,
                  userId: e.target.value
                })}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="Reason"
                value={operationParams.reason || ''}
                onChange={(e) => setOperationParams({
                  ...operationParams,
                  reason: e.target.value
                })}
                sx={{ mb: 2 }}
              />
            </>
          )}

          {selectedOperation === 'financial_adjustment' && (
            <>
              <TextField
                fullWidth
                label="User ID"
                value={operationParams.userId || ''}
                onChange={(e) => setOperationParams({
                  ...operationParams,
                  userId: e.target.value
                })}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="Amount"
                type="number"
                value={operationParams.amount || ''}
                onChange={(e) => setOperationParams({
                  ...operationParams,
                  amount: parseFloat(e.target.value)
                })}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="Reason"
                value={operationParams.reason || ''}
                onChange={(e) => setOperationParams({
                  ...operationParams,
                  reason: e.target.value
                })}
                sx={{ mb: 2 }}
              />
            </>
          )}

          <Alert severity="warning" sx={{ mt: 2 }}>
            This operation requires elevated privileges and will be audited.
            {selectedOperation === 'system_shutdown' && ' This will activate emergency mode.'}
            {selectedOperation === 'data_deletion' && ' This action is irreversible.'}
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPrivilegedOpDialog(false)}>
            Cancel
          </Button>
          <Button
            onClick={() => handlePrivilegedOperation(selectedOperation, operationParams)}
            variant="contained"
            color="error"
            disabled={!selectedOperation}
          >
            Execute Operation
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminDashboard;
