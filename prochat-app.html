<!DOCTYPE html>
<html lang="sw">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProChat - Tanzania's Super App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .app-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header h1 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }
        
        .status {
            background: rgba(255,255,255,0.2);
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            display: inline-block;
            font-size: 0.8rem;
        }
        
        .tabs {
            display: flex;
            background: white;
            border-top: 1px solid #e0e0e0;
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 400px;
            z-index: 100;
        }
        
        .tab {
            flex: 1;
            padding: 0.8rem 0.5rem;
            text-align: center;
            cursor: pointer;
            border: none;
            background: none;
            transition: all 0.3s ease;
            font-size: 0.7rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.2rem;
        }
        
        .tab:hover {
            background: #f8f9fa;
        }
        
        .tab.active {
            color: #667eea;
            background: #f0f4ff;
        }
        
        .tab-icon {
            font-size: 1.2rem;
        }
        
        .tab-content {
            padding: 1rem;
            padding-bottom: 80px;
            min-height: calc(100vh - 140px);
        }
        
        .tab-panel {
            display: none;
        }
        
        .tab-panel.active {
            display: block;
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1rem;
        }
        
        .balance-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        
        .balance {
            font-size: 2rem;
            font-weight: bold;
            margin: 1rem 0;
        }
        
        .actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.8rem;
            margin-top: 1rem;
        }
        
        .action-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 0.8rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
            font-size: 0.8rem;
        }
        
        .action-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .post {
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 1rem;
            margin-bottom: 1rem;
        }
        
        .post:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .post-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 0.8rem;
            font-size: 0.9rem;
        }
        
        .post-content {
            margin-bottom: 0.8rem;
            line-height: 1.5;
            font-size: 0.9rem;
        }
        
        .post-actions {
            display: flex;
            gap: 1rem;
        }
        
        .post-action {
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            padding: 0.3rem;
            border-radius: 4px;
            transition: all 0.2s ease;
            font-size: 0.8rem;
        }
        
        .post-action:hover {
            background: #f8f9fa;
            color: #333;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        .list-item {
            padding: 0.8rem 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 0.9rem;
        }
        
        .list-item:last-child {
            border-bottom: none;
        }
        
        .list-item strong {
            display: block;
            margin-bottom: 0.3rem;
        }
        
        .list-item small {
            color: #666;
            font-size: 0.8rem;
        }
        
        .notification-badge {
            background: #ff4757;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            font-size: 0.7rem;
            margin-left: auto;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <h1>🚀 ProChat</h1>
            <div class="status" id="status">✅ Connected</div>
        </div>
        
        <div class="tab-content">
            <!-- Home Tab -->
            <div class="tab-panel active" id="tab-0">
                <div class="card">
                    <h3>📱 Machapisho ya Kijamii</h3>
                    <div id="posts-container">
                        <div class="post">
                            <div class="post-header">
                                <div class="avatar">J</div>
                                <div>
                                    <strong>john_doe</strong><br>
                                    <small>Sasa hivi</small>
                                </div>
                            </div>
                            <div class="post-content">Habari za asubuhi! Leo ni siku nzuri ya kufanya biashara.</div>
                            <div class="post-actions">
                                <button class="post-action">❤️ 15</button>
                                <button class="post-action">💬 3</button>
                                <button class="post-action">📤 Share</button>
                            </div>
                        </div>
                        
                        <div class="post">
                            <div class="post-header">
                                <div class="avatar">J</div>
                                <div>
                                    <strong>jane_smith</strong><br>
                                    <small>Dakika 5 zilizopita</small>
                                </div>
                            </div>
                            <div class="post-content">Nimepata kazi mpya kupitia ProChat Jobs! Asante sana.</div>
                            <div class="post-actions">
                                <button class="post-action">❤️ 28</button>
                                <button class="post-action">💬 7</button>
                                <button class="post-action">📤 Share</button>
                            </div>
                        </div>
                        
                        <div class="post">
                            <div class="post-header">
                                <div class="avatar">M</div>
                                <div>
                                    <strong>mike_wilson</strong><br>
                                    <small>Dakika 10 zilizopita</small>
                                </div>
                            </div>
                            <div class="post-content">Event ya muziki leo jioni Mlimani City. Tiketi zinapatikana ProChat.</div>
                            <div class="post-actions">
                                <button class="post-action">❤️ 42</button>
                                <button class="post-action">💬 12</button>
                                <button class="post-action">📤 Share</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Chats Tab -->
            <div class="tab-panel" id="tab-1">
                <div class="card">
                    <h3>💬 Mazungumzo</h3>
                    <div class="list-item">
                        <div style="display: flex; align-items: center;">
                            <div class="avatar">F</div>
                            <div style="flex: 1;">
                                <strong>Family Group</strong><br>
                                <small>Habari za leo?</small>
                            </div>
                            <div class="notification-badge">2</div>
                        </div>
                    </div>
                    
                    <div class="list-item">
                        <div style="display: flex; align-items: center;">
                            <div class="avatar">J</div>
                            <div style="flex: 1;">
                                <strong>John Doe</strong><br>
                                <small>Tutaonana kesho</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="list-item">
                        <div style="display: flex; align-items: center;">
                            <div class="avatar">W</div>
                            <div style="flex: 1;">
                                <strong>Work Team</strong><br>
                                <small>Meeting at 2 PM</small>
                            </div>
                            <div class="notification-badge">5</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Discover Tab -->
            <div class="tab-panel" id="tab-2">
                <div class="card">
                    <h3>🎫 Matukio</h3>
                    <div class="list-item">
                        <strong>Muziki wa Bongo Flava</strong><br>
                        <small>📅 2024-12-15 | 📍 Mlimani City | 💰 TZS 15,000</small>
                    </div>
                    <div class="list-item">
                        <strong>Tech Conference Dar</strong><br>
                        <small>📅 2024-12-20 | 📍 UDSM | 💰 TZS 25,000</small>
                    </div>
                    <div class="list-item">
                        <strong>Food Festival</strong><br>
                        <small>📅 2024-12-25 | 📍 Coco Beach | 💰 TZS 10,000</small>
                    </div>
                </div>
                
                <div class="card">
                    <h3>💼 Fursa za Kazi</h3>
                    <div class="list-item">
                        <strong>Software Developer</strong><br>
                        <small>🏢 TechCorp TZ | 💰 TZS 800,000 - 1,200,000 | 📍 Dar es Salaam</small>
                    </div>
                    <div class="list-item">
                        <strong>Marketing Manager</strong><br>
                        <small>🏢 BrandCo | 💰 TZS 600,000 - 900,000 | 📍 Arusha</small>
                    </div>
                    <div class="list-item">
                        <strong>Data Analyst</strong><br>
                        <small>🏢 DataTech | 💰 TZS 700,000 - 1,000,000 | 📍 Mwanza</small>
                    </div>
                </div>
            </div>
            
            <!-- Me Tab -->
            <div class="tab-panel" id="tab-3">
                <div class="card balance-card">
                    <h3>💰 ProPay Wallet</h3>
                    <div class="balance">TZS 250,000</div>
                    <div class="actions">
                        <button class="action-btn" onclick="sendMoney()">📤 Tuma Pesa</button>
                        <button class="action-btn" onclick="receiveMoney()">📥 Pokea Pesa</button>
                        <button class="action-btn" onclick="payBills()">🧾 Lipa Bill</button>
                        <button class="action-btn" onclick="buyAirtime()">📱 Airtime</button>
                    </div>
                </div>
                
                <div class="card">
                    <h3>📊 Historia ya Malipo</h3>
                    <div class="list-item">
                        <strong>Malipo ya bidhaa</strong><br>
                        <small>SEND_MONEY | TZS 50,000 | COMPLETED</small>
                    </div>
                    <div class="list-item">
                        <strong>Mshahara</strong><br>
                        <small>RECEIVE_MONEY | TZS 75,000 | COMPLETED</small>
                    </div>
                    <div class="list-item">
                        <strong>LUKU - Umeme</strong><br>
                        <small>BILL_PAYMENT | TZS 25,000 | COMPLETED</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="switchTab(0)">
                <div class="tab-icon">🏠</div>
                <div>Nyumbani</div>
            </button>
            <button class="tab" onclick="switchTab(1)">
                <div class="tab-icon">💬</div>
                <div>Mazungumzo</div>
            </button>
            <button class="tab" onclick="switchTab(2)">
                <div class="tab-icon">🔍</div>
                <div>Gundua</div>
            </button>
            <button class="tab" onclick="switchTab(3)">
                <div class="tab-icon">👤</div>
                <div>Mimi</div>
            </button>
        </div>
    </div>
    
    <script>
        // Tab switching
        function switchTab(tabIndex) {
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));
            
            document.querySelectorAll('.tab')[tabIndex].classList.add('active');
            document.getElementById(`tab-${tabIndex}`).classList.add('active');
        }
        
        // ProPay actions
        function sendMoney() {
            alert('💸 Tuma Pesa\n\nFungua app ya ProPay kutuma pesa kwa:\n• M-Pesa\n• Airtel Money\n• Tigo Pesa\n• Benki');
        }
        
        function receiveMoney() {
            alert('📥 Pokea Pesa\n\nNambari yako ya akaunti: +255 XXX XXX XXX\nQR Code imetengenezwa!\n\nWaweza kupokea pesa kutoka:\n• M-Pesa\n• Airtel Money\n• Benki');
        }
        
        function payBills() {
            alert('🧾 Lipa Bills\n\nLipa:\n• LUKU (Umeme)\n• DAWASCO (Maji)\n• Simu na Internet\n• DStv/StarTimes\n• Kodi za Serikali');
        }
        
        function buyAirtime() {
            alert('📱 Nunua Airtime\n\nChagua mtandao:\n• Vodacom\n• Airtel\n• Tigo\n• Halotel\n\nAu nunua bundles za data!');
        }
        
        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 ProChat App Loaded!');
            console.log('📱 Mobile-first design active');
            console.log('💰 ProPay wallet ready');
            console.log('📊 Real data from backend simulation');
        });
    </script>
</body>
</html>
