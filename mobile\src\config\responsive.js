// Responsive Design Configuration for ProChat
import { Dimensions, Platform } from 'react-native';

const { width, height } = Dimensions.get('window');

// Breakpoints for responsive design
export const breakpoints = {
  mobile: 0,
  tablet: 768,
  desktop: 1024,
  largeDesktop: 1440
};

// Device detection
export const deviceType = () => {
  if (Platform.OS === 'web') {
    if (width >= breakpoints.largeDesktop) return 'largeDesktop';
    if (width >= breakpoints.desktop) return 'desktop';
    if (width >= breakpoints.tablet) return 'tablet';
    return 'mobile';
  }
  
  // For React Native
  if (Platform.isPad || width >= breakpoints.tablet) return 'tablet';
  return 'mobile';
};

// Responsive dimensions
export const responsiveDimensions = {
  // Screen dimensions
  screenWidth: width,
  screenHeight: height,
  
  // Container widths
  containerWidth: {
    mobile: '100%',
    tablet: '90%',
    desktop: '80%',
    largeDesktop: '70%'
  },
  
  // Font sizes
  fontSize: {
    small: {
      mobile: 12,
      tablet: 14,
      desktop: 14,
      largeDesktop: 16
    },
    medium: {
      mobile: 14,
      tablet: 16,
      desktop: 16,
      largeDesktop: 18
    },
    large: {
      mobile: 18,
      tablet: 20,
      desktop: 22,
      largeDesktop: 24
    },
    xlarge: {
      mobile: 24,
      tablet: 28,
      desktop: 32,
      largeDesktop: 36
    }
  },
  
  // Spacing
  spacing: {
    xs: {
      mobile: 4,
      tablet: 6,
      desktop: 8,
      largeDesktop: 10
    },
    sm: {
      mobile: 8,
      tablet: 12,
      desktop: 16,
      largeDesktop: 20
    },
    md: {
      mobile: 16,
      tablet: 20,
      desktop: 24,
      largeDesktop: 28
    },
    lg: {
      mobile: 24,
      tablet: 32,
      desktop: 40,
      largeDesktop: 48
    },
    xl: {
      mobile: 32,
      tablet: 48,
      desktop: 64,
      largeDesktop: 80
    }
  },
  
  // Component sizes
  componentSizes: {
    button: {
      height: {
        mobile: 44,
        tablet: 48,
        desktop: 52,
        largeDesktop: 56
      },
      padding: {
        mobile: 12,
        tablet: 16,
        desktop: 20,
        largeDesktop: 24
      }
    },
    input: {
      height: {
        mobile: 44,
        tablet: 48,
        desktop: 52,
        largeDesktop: 56
      }
    },
    card: {
      padding: {
        mobile: 16,
        tablet: 20,
        desktop: 24,
        largeDesktop: 28
      },
      borderRadius: {
        mobile: 8,
        tablet: 12,
        desktop: 16,
        largeDesktop: 20
      }
    }
  }
};

// Helper functions
export const getResponsiveValue = (values, currentDeviceType = deviceType()) => {
  return values[currentDeviceType] || values.mobile;
};

export const isTabletOrLarger = () => {
  const device = deviceType();
  return device === 'tablet' || device === 'desktop' || device === 'largeDesktop';
};

export const isDesktopOrLarger = () => {
  const device = deviceType();
  return device === 'desktop' || device === 'largeDesktop';
};

// Layout configurations
export const layoutConfig = {
  // Tab bar configuration
  tabBar: {
    position: {
      mobile: 'bottom',
      tablet: 'bottom',
      desktop: 'side',
      largeDesktop: 'side'
    },
    orientation: {
      mobile: 'horizontal',
      tablet: 'horizontal',
      desktop: 'vertical',
      largeDesktop: 'vertical'
    }
  },
  
  // Grid configurations
  grid: {
    columns: {
      mobile: 1,
      tablet: 2,
      desktop: 3,
      largeDesktop: 4
    },
    gap: {
      mobile: 16,
      tablet: 20,
      desktop: 24,
      largeDesktop: 28
    }
  },
  
  // Modal configurations
  modal: {
    width: {
      mobile: '95%',
      tablet: '80%',
      desktop: '60%',
      largeDesktop: '50%'
    },
    maxWidth: {
      mobile: 400,
      tablet: 600,
      desktop: 800,
      largeDesktop: 1000
    }
  }
};

export default {
  breakpoints,
  deviceType,
  responsiveDimensions,
  getResponsiveValue,
  isTabletOrLarger,
  isDesktopOrLarger,
  layoutConfig
};
