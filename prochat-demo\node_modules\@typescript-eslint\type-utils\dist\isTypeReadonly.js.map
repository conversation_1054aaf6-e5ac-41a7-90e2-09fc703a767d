{"version": 3, "file": "isTypeReadonly.js", "sourceRoot": "", "sources": ["../src/isTypeReadonly.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,oDAAuD;AACvD,qCAQiB;AACjB,+CAAiC;AAEjC,yDAAsD;AACtD,mDAA0D;AAe7C,QAAA,yBAAyB,GAAG;IACvC,IAAI,EAAE,QAAQ;IACd,oBAAoB,EAAE,KAAK;IAC3B,UAAU,EAAE;QACV,sBAAsB,EAAE;YACtB,IAAI,EAAE,SAAS;SAChB;KACF;CACF,CAAC;AAEW,QAAA,2BAA2B,GAAwB;IAC9D,sBAAsB,EAAE,KAAK;CAC9B,CAAC;AAEF,SAAS,SAAS,CAAC,IAAa;IAC9B,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC9D,CAAC;AAED,SAAS,0BAA0B,CACjC,OAAuB,EACvB,IAAa,EACb,OAA4B,EAC5B,SAAuB;IAEvB,SAAS,kBAAkB,CAAC,SAA2B;QACrD,MAAM,aAAa;QACjB,2CAA2C;QAC3C,IAAA,mCAAgB,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAEvC,uCAAuC;QACvC,4CAA4C;QAC5C,oDAAoD;QACpD,wBAAwB,CAAC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;YACvD,qCAA6B;SAC9B;QAED,+CAA+C;QAC/C,IACE,aAAa,CAAC,IAAI,CAChB,OAAO,CAAC,EAAE,CACR,sBAAsB,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC;wCACxC,CACvB,EACD;YACA,oCAA4B;SAC7B;QACD,qCAA6B;IAC/B,CAAC;IAED,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;QAC7B,MAAM,MAAM,GAAG,mBAAW,CAAC,UAAU,CACnC,IAAI,CAAC,SAAS,EAAE,EAChB,mBAAW,CAAC,iBAAiB,CAAC,YAAY,CAAC,QAAQ,EAAE,YAAY,CAAC,CACnE,CAAC;QACF,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;QAC5C,IAAI,WAAW,KAAK,OAAO,EAAE;YAC3B,oCAA4B;SAC7B;QAED,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC;KACjC;IAED,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;QAC7B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YACzB,oCAA4B;SAC7B;QAED,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC;KACjC;IAED,wCAAgC;AAClC,CAAC;AAED,SAAS,oBAAoB,CAC3B,OAAuB,EACvB,IAAa,EACb,OAA4B,EAC5B,SAAuB;IAEvB,SAAS,mBAAmB,CAAC,IAAkB;QAC7C,MAAM,SAAS,GAAG,OAAO,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACzD,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;gBACzB,oCAA4B;aAC7B;YAED,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,IAAI,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;gBAC5D,qCAA6B;aAC9B;YAED,OAAO,sBAAsB,CAC3B,OAAO,EACP,SAAS,CAAC,IAAI,EACd,OAAO,EACP,SAAS,CACV,CAAC;SACH;QAED,wCAAgC;IAClC,CAAC;IAED,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;IACxC,IAAI,UAAU,CAAC,MAAM,EAAE;QACrB,+CAA+C;QAC/C,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE;YACjC,IAAI,OAAO,CAAC,sBAAsB,EAAE;gBAClC,IACE,QAAQ,CAAC,gBAAgB,KAAK,SAAS;oBACvC,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC;oBACpC,IAAA,yBAAe,EACb,QAAQ,CAAC,gBAAgB,CAAC,MAAM,EAChC,EAAE,CAAC,WAAW,CAAC,MAAM,CACtB,EACD;oBACA,SAAS;iBACV;gBAED,MAAM,YAAY,GAAG,QAAQ,CAAC,eAAe,EAAE,CAAC;gBAChD,MAAM,eAAe,GACnB,YAAY,KAAK,SAAS,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC;oBACnD,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;oBACvC,CAAC,CAAC,SAAS,CAAC;gBAChB,IACE,eAAe,KAAK,SAAS;oBAC7B,SAAS,CAAC,eAAe,CAAC;oBAC1B,IAAA,yBAAe,EAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,EAC9D;oBACA,SAAS;iBACV;aACF;YAED,IAAI,IAAA,kCAAwB,EAAC,IAAI,EAAE,QAAQ,CAAC,cAAc,EAAE,EAAE,OAAO,CAAC,EAAE;gBACtE,SAAS;aACV;YAED,MAAM,IAAI,GAAG,EAAE,CAAC,oBAAoB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAChE,IAAI,IAAI,IAAI,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE;gBACxC,SAAS;aACV;YAED,oCAA4B;SAC7B;QAED,+BAA+B;QAC/B,uDAAuD;QAEvD,wEAAwE;QACxE,yEAAyE;QACzE,gDAAgD;QAChD,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE;YACjC,MAAM,YAAY,GAAG,mBAAW,CAAC,UAAU,CACzC,IAAA,uCAAuB,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,EAChD,mBAAW,CAAC,iBAAiB,CAAC,YAAY,CACxC,aAAa,QAAQ,CAAC,IAAI,GAAG,EAC7B,MAAM,CACP,CACF,CAAC;YAEF,0BAA0B;YAC1B,gHAAgH;YAChH,IAAI,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;gBAC/B,SAAS;aACV;YAED,IACE,sBAAsB,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC;4CAC7C,EACpB;gBACA,oCAA4B;aAC7B;SACF;KACF;IAED,MAAM,wBAAwB,GAAG,mBAAmB,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC1E,IAAI,wBAAwB,iCAAyB,EAAE;QACrD,OAAO,wBAAwB,CAAC;KACjC;IAED,MAAM,wBAAwB,GAAG,mBAAmB,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC1E,IAAI,wBAAwB,iCAAyB,EAAE;QACrD,OAAO,wBAAwB,CAAC;KACjC;IAED,qCAA6B;AAC/B,CAAC;AAED,qGAAqG;AACrG,SAAS,sBAAsB,CAC7B,OAAuB,EACvB,IAAa,EACb,OAA4B,EAC5B,SAAuB;IAEvB,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAEpB,IAAI,IAAA,qBAAW,EAAC,IAAI,CAAC,EAAE;QACrB,0CAA0C;QAC1C,MAAM,MAAM,GAAG,IAAA,wBAAc,EAAC,IAAI,CAAC,CAAC,KAAK,CACvC,CAAC,CAAC,EAAE,CACF,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YAChB,sBAAsB,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,SAAS,CAAC;6CAC/B,CAC1B,CAAC;QACF,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,+BAAuB,CAAC,6BAAqB,CAAC;QAC3E,OAAO,YAAY,CAAC;KACrB;IAED,IAAI,IAAA,4BAAkB,EAAC,IAAI,CAAC,EAAE;QAC5B,mGAAmG;QACnG,IACE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EACtE;YACA,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CACvC,CAAC,CAAC,EAAE,CACF,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;gBAChB,sBAAsB,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,SAAS,CAAC;iDAC/B,CAC1B,CAAC;YACF,OAAO,gBAAgB,CAAC,CAAC,+BAAuB,CAAC,6BAAqB,CAAC;SACxE;QAED,eAAe;QACf,MAAM,gBAAgB,GAAG,oBAAoB,CAC3C,OAAO,EACP,IAAI,EACJ,OAAO,EACP,SAAS,CACV,CAAC;QACF,IAAI,gBAAgB,qCAA6B,EAAE;YACjD,OAAO,gBAAgB,CAAC;SACzB;KACF;IAED,IAAI,IAAA,2BAAiB,EAAC,IAAI,CAAC,EAAE;QAC3B,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;aAC/D,GAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC;aAChC,KAAK,CACJ,CAAC,CAAC,EAAE,CACF,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YAChB,sBAAsB,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,SAAS,CAAC;6CAC/B,CAC1B,CAAC;QAEJ,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,+BAAuB,CAAC,6BAAqB,CAAC;QAC3E,OAAO,YAAY,CAAC;KACrB;IAED,uDAAuD;IACvD,sCAAsC;IACtC,IAAI,CAAC,IAAA,sBAAY,EAAC,IAAI,CAAC,EAAE;QACvB,qCAA6B;KAC9B;IAED,mCAAmC;IACnC,IACE,IAAI,CAAC,iBAAiB,EAAE,CAAC,MAAM,GAAG,CAAC;QACnC,IAAI,CAAC,aAAa,EAAE,CAAC,MAAM,KAAK,CAAC,EACjC;QACA,qCAA6B;KAC9B;IAED,MAAM,eAAe,GAAG,0BAA0B,CAChD,OAAO,EACP,IAAI,EACJ,OAAO,EACP,SAAS,CACV,CAAC;IACF,IAAI,eAAe,qCAA6B,EAAE;QAChD,OAAO,eAAe,CAAC;KACxB;IAED,MAAM,gBAAgB,GAAG,oBAAoB,CAC3C,OAAO,EACP,IAAI,EACJ,OAAO,EACP,SAAS,CACV,CAAC;IACF,0BAA0B,CAAC,IACzB,gBAAgB,qCAA6B,EAC7C;QACA,OAAO,gBAAgB,CAAC;KACzB;IAED,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;AACpC,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CACrB,OAAuB,EACvB,IAAa,EACb,UAA+B,mCAA2B;IAE1D,OAAO,CACL,sBAAsB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,GAAG,EAAE,CAAC;qCACpC,CACtB,CAAC;AACJ,CAAC;AAEQ,wCAAc"}