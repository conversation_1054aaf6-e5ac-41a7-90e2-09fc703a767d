// ProChat Payment Form Component
// Complete payment form for all payment methods

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Divider,
} from '@mui/material';
import {
  Payment,
  Security,
  CheckCircle,
  Error,
  Info,
  Phone,
  CreditCard,
  AccountBalance,
} from '@mui/icons-material';
// import { useFormik } from 'formik';
// import * as Yup from 'yup';
import PaymentMethodSelector from './PaymentMethodSelector';
import paymentAPI from '../../api/paymentAPI';

const PaymentForm = ({ 
  amount, 
  currency = 'TZS', 
  description = '', 
  onSuccess, 
  onError,
  metadata = {},
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [selectedMethod, setSelectedMethod] = useState('');
  const [processing, setProcessing] = useState(false);
  const [paymentResult, setPaymentResult] = useState(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [fees, setFees] = useState(null);

  const steps = ['Chagua Njia ya Malipo', 'Jaza Maelezo', 'Thibitisha Malipo'];

  // Form state
  const [formData, setFormData] = useState({
    amount: amount || '',
    customerPhone: '',
    customerAccount: '',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
    walletAddress: '',
    description: description,
  });
  const [errors, setErrors] = useState({});

  // Simple validation function
  const validateForm = () => {
    const newErrors = {};

    if (!formData.amount || formData.amount < 100) {
      newErrors.amount = 'Kiasi cha chini ni TZS 100';
    }
    if (formData.amount > ********) {
      newErrors.amount = 'Kiasi cha juu ni TZS 10M';
    }

    if (['mpesa', 'airtel_money', 'tigo_pesa', 'halopesa', 'azampesa', 'nara'].includes(selectedMethod)) {
      if (!formData.customerPhone) {
        newErrors.customerPhone = 'Nambari ya simu ni lazima';
      } else if (!/^(\+255|0)[67]\d{8}$/.test(formData.customerPhone)) {
        newErrors.customerPhone = 'Nambari ya simu si sahihi';
      }
    }

    if (['crdb_bank', 'nmb_bank', 'nbc_bank'].includes(selectedMethod)) {
      if (!formData.customerAccount) {
        newErrors.customerAccount = 'Nambari ya akaunti ni lazima';
      } else if (formData.customerAccount.length < 10) {
        newErrors.customerAccount = 'Nambari ya akaunti ni fupi';
      }
    }

    if (['visa', 'mastercard', 'stripe'].includes(selectedMethod)) {
      if (!formData.cardNumber) {
        newErrors.cardNumber = 'Nambari ya kadi ni lazima';
      } else if (!/^\d{16}$/.test(formData.cardNumber.replace(/\s/g, ''))) {
        newErrors.cardNumber = 'Nambari ya kadi ni lazima iwe na tarakimu 16';
      }

      if (!formData.expiryDate) {
        newErrors.expiryDate = 'Tarehe ya mwisho ni lazima';
      } else if (!/^(0[1-9]|1[0-2])\/\d{2}$/.test(formData.expiryDate)) {
        newErrors.expiryDate = 'Tumia muundo MM/YY';
      }

      if (!formData.cvv) {
        newErrors.cvv = 'CVV ni lazima';
      } else if (!/^\d{3,4}$/.test(formData.cvv)) {
        newErrors.cvv = 'CVV ni lazima iwe na tarakimu 3 au 4';
      }

      if (!formData.cardholderName) {
        newErrors.cardholderName = 'Jina la mmiliki wa kadi ni lazima';
      }
    }

    if (['bitcoin', 'usdt'].includes(selectedMethod)) {
      if (!formData.walletAddress) {
        newErrors.walletAddress = 'Anwani ya wallet ni lazima';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      setShowConfirmDialog(true);
    }
  };

  useEffect(() => {
    if (selectedMethod && formData.amount) {
      calculateFees();
    }
  }, [selectedMethod, formData.amount]);

  const calculateFees = async () => {
    try {
      const feeData = await paymentAPI.calculateFees(
        formData.amount,
        currency,
        selectedMethod
      );
      setFees(feeData);
    } catch (error) {
      console.error('Failed to calculate fees:', error);
    }
  };

  const handleMethodSelect = (method) => {
    setSelectedMethod(method);
    setActiveStep(1);
  };

  const handleNext = () => {
    if (activeStep === 1) {
      if (validateForm()) {
        setShowConfirmDialog(true);
      }
    } else {
      setActiveStep((prevStep) => prevStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const processPayment = async () => {
    try {
      setProcessing(true);
      setShowConfirmDialog(false);

      const paymentData = {
        amount: parseFloat(formData.amount),
        currency,
        paymentMethod: selectedMethod,
        provider: selectedMethod,
        description: formData.description,
        metadata,
      };

      // Add method-specific data
      if (['mpesa', 'airtel_money', 'tigo_pesa', 'halopesa', 'azampesa', 'nara'].includes(selectedMethod)) {
        paymentData.customerPhone = formData.customerPhone;
      }

      if (['crdb_bank', 'nmb_bank', 'nbc_bank'].includes(selectedMethod)) {
        paymentData.customerAccount = formData.customerAccount;
      }

      if (['visa', 'mastercard', 'stripe'].includes(selectedMethod)) {
        paymentData.cardData = {
          number: formData.cardNumber,
          expiryDate: formData.expiryDate,
          cvv: formData.cvv,
          holderName: formData.cardholderName,
        };
      }

      if (['bitcoin', 'usdt'].includes(selectedMethod)) {
        paymentData.walletAddress = formData.walletAddress;
      }

      const result = await paymentAPI.processPayment(paymentData);
      
      setPaymentResult(result);
      setActiveStep(2);

      if (result.success && onSuccess) {
        onSuccess(result);
      } else if (!result.success && onError) {
        onError(result);
      }

    } catch (error) {
      setPaymentResult({
        success: false,
        message: error.message || 'Malipo yameshindwa',
      });
      if (onError) {
        onError(error);
      }
    } finally {
      setProcessing(false);
    }
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <PaymentMethodSelector
            amount={formData.amount}
            currency={currency}
            onMethodSelect={handleMethodSelect}
            selectedMethod={selectedMethod}
            showFees={true}
          />
        );

      case 1:
        return (
          <Box component="form" onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Kiasi"
                  name="amount"
                  type="number"
                  value={formData.amount}
                  onChange={(e) => handleInputChange('amount', e.target.value)}
                  error={Boolean(errors.amount)}
                  helperText={errors.amount}
                  InputProps={{
                    startAdornment: <Typography sx={{ mr: 1 }}>{currency}</Typography>,
                  }}
                />
              </Grid>

              {/* Mobile Money Fields */}
              {['mpesa', 'airtel_money', 'tigo_pesa', 'halopesa', 'azampesa', 'nara'].includes(selectedMethod) && (
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Nambari ya Simu"
                    name="customerPhone"
                    value={formik.values.customerPhone}
                    onChange={formik.handleChange}
                    error={formik.touched.customerPhone && Boolean(formik.errors.customerPhone)}
                    helperText={formik.touched.customerPhone && formik.errors.customerPhone}
                    placeholder="+************"
                    InputProps={{
                      startAdornment: <Phone sx={{ mr: 1, color: 'action.active' }} />,
                    }}
                  />
                </Grid>
              )}

              {/* Bank Account Fields */}
              {['crdb_bank', 'nmb_bank', 'nbc_bank'].includes(selectedMethod) && (
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Nambari ya Akaunti"
                    name="customerAccount"
                    value={formik.values.customerAccount}
                    onChange={formik.handleChange}
                    error={formik.touched.customerAccount && Boolean(formik.errors.customerAccount)}
                    helperText={formik.touched.customerAccount && formik.errors.customerAccount}
                    InputProps={{
                      startAdornment: <AccountBalance sx={{ mr: 1, color: 'action.active' }} />,
                    }}
                  />
                </Grid>
              )}

              {/* Card Fields */}
              {['visa', 'mastercard', 'stripe'].includes(selectedMethod) && (
                <>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Jina la Mmiliki wa Kadi"
                      name="cardholderName"
                      value={formik.values.cardholderName}
                      onChange={formik.handleChange}
                      error={formik.touched.cardholderName && Boolean(formik.errors.cardholderName)}
                      helperText={formik.touched.cardholderName && formik.errors.cardholderName}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Nambari ya Kadi"
                      name="cardNumber"
                      value={formik.values.cardNumber}
                      onChange={formik.handleChange}
                      error={formik.touched.cardNumber && Boolean(formik.errors.cardNumber)}
                      helperText={formik.touched.cardNumber && formik.errors.cardNumber}
                      placeholder="1234 5678 9012 3456"
                      InputProps={{
                        startAdornment: <CreditCard sx={{ mr: 1, color: 'action.active' }} />,
                      }}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Tarehe ya Mwisho"
                      name="expiryDate"
                      value={formik.values.expiryDate}
                      onChange={formik.handleChange}
                      error={formik.touched.expiryDate && Boolean(formik.errors.expiryDate)}
                      helperText={formik.touched.expiryDate && formik.errors.expiryDate}
                      placeholder="MM/YY"
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="CVV"
                      name="cvv"
                      value={formik.values.cvv}
                      onChange={formik.handleChange}
                      error={formik.touched.cvv && Boolean(formik.errors.cvv)}
                      helperText={formik.touched.cvv && formik.errors.cvv}
                      placeholder="123"
                    />
                  </Grid>
                </>
              )}

              {/* Crypto Fields */}
              {['bitcoin', 'usdt'].includes(selectedMethod) && (
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Anwani ya Wallet"
                    name="walletAddress"
                    value={formik.values.walletAddress}
                    onChange={formik.handleChange}
                    error={formik.touched.walletAddress && Boolean(formik.errors.walletAddress)}
                    helperText={formik.touched.walletAddress && formik.errors.walletAddress}
                  />
                </Grid>
              )}

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Maelezo (si lazima)"
                  name="description"
                  multiline
                  rows={3}
                  value={formik.values.description}
                  onChange={formik.handleChange}
                />
              </Grid>

              {fees && (
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Muhtasari wa Malipo
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography>Kiasi:</Typography>
                        <Typography>{currency} {formik.values.amount}</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography>Ada:</Typography>
                        <Typography>{currency} {fees.total}</Typography>
                      </Box>
                      <Divider sx={{ my: 1 }} />
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="h6">Jumla:</Typography>
                        <Typography variant="h6">
                          {currency} {(parseFloat(formik.values.amount) + fees.total).toLocaleString()}
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              )}
            </Grid>
          </Box>
        );

      case 2:
        return (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            {processing ? (
              <>
                <CircularProgress size={60} sx={{ mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Inachakata malipo...
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Tafadhali subiri, usifunge ukurasa huu
                </Typography>
              </>
            ) : paymentResult ? (
              <>
                {paymentResult.success ? (
                  <>
                    <CheckCircle sx={{ fontSize: 60, color: 'success.main', mb: 2 }} />
                    <Typography variant="h5" gutterBottom color="success.main">
                      Malipo Yamefanikiwa!
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      {paymentResult.message}
                    </Typography>
                    {paymentResult.transactionId && (
                      <Chip
                        label={`ID: ${paymentResult.transactionId}`}
                        variant="outlined"
                        sx={{ mt: 2 }}
                      />
                    )}
                  </>
                ) : (
                  <>
                    <Error sx={{ fontSize: 60, color: 'error.main', mb: 2 }} />
                    <Typography variant="h5" gutterBottom color="error.main">
                      Malipo Yameshindwa
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      {paymentResult.message}
                    </Typography>
                    <Button
                      variant="contained"
                      onClick={() => setActiveStep(1)}
                      sx={{ mt: 2 }}
                    >
                      Jaribu Tena
                    </Button>
                  </>
                )}
              </>
            ) : null}
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Box>
      <Card>
        <CardContent>
          <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <Payment sx={{ mr: 1 }} />
            Malipo ya ProChat
          </Typography>

          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {renderStepContent()}

          {activeStep < 2 && !processing && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
              <Button
                disabled={activeStep === 0}
                onClick={handleBack}
              >
                Rudi Nyuma
              </Button>
              <Button
                variant="contained"
                onClick={handleNext}
                disabled={activeStep === 0 && !selectedMethod}
              >
                {activeStep === 1 ? 'Thibitisha Malipo' : 'Endelea'}
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onClose={() => setShowConfirmDialog(false)}>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Security sx={{ mr: 1 }} />
            Thibitisha Malipo
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Je, una uhakika unataka kuendelea na malipo haya?
          </Typography>
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Kiasi:</strong> {currency} {formik.values.amount}
            </Typography>
            <Typography variant="body2">
              <strong>Njia ya Malipo:</strong> {selectedMethod}
            </Typography>
            {fees && (
              <Typography variant="body2">
                <strong>Ada:</strong> {currency} {fees.total}
              </Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowConfirmDialog(false)}>
            Ghairi
          </Button>
          <Button
            variant="contained"
            onClick={processPayment}
            disabled={processing}
          >
            Thibitisha
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PaymentForm;
