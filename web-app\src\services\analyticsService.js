// ProChat Advanced Analytics & Monitoring Service
// Cross-platform analytics with privacy-focused tracking

import { SecurityLogger } from '../config/security';
import { detectDeviceType } from '../config/responsive';

class AnalyticsService {
  constructor() {
    this.isEnabled = process.env.REACT_APP_ANALYTICS_ENABLED === 'true';
    this.sessionId = this.generateSessionId();
    this.userId = null;
    this.deviceInfo = this.getDeviceInfo();
    this.startTime = Date.now();
    this.events = [];
    this.pageViews = [];
    this.errors = [];
    this.performance = [];
    
    this.init();
  }

  init() {
    if (!this.isEnabled) {
      console.log('ProChat Analytics: Disabled');
      return;
    }

    // Initialize analytics providers
    this.initializeGoogleAnalytics();
    this.initializeFacebookPixel();
    this.initializeHotjar();
    this.initializeSentry();
    
    // Setup automatic tracking
    this.setupPageViewTracking();
    this.setupErrorTracking();
    this.setupPerformanceTracking();
    this.setupUserInteractionTracking();
    
    // Track session start
    this.trackEvent('session_start', {
      sessionId: this.sessionId,
      deviceInfo: this.deviceInfo,
      timestamp: new Date().toISOString(),
    });

    console.log('ProChat Analytics: Initialized');
  }

  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getDeviceInfo() {
    return {
      deviceType: detectDeviceType(),
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth,
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      connection: this.getConnectionInfo(),
    };
  }

  getConnectionInfo() {
    if ('connection' in navigator) {
      const conn = navigator.connection;
      return {
        effectiveType: conn.effectiveType,
        downlink: conn.downlink,
        rtt: conn.rtt,
        saveData: conn.saveData,
      };
    }
    return null;
  }

  // Google Analytics 4
  initializeGoogleAnalytics() {
    const gaId = process.env.REACT_APP_GOOGLE_ANALYTICS_ID;
    if (!gaId) return;

    try {
      // Load gtag script
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
      document.head.appendChild(script);

      // Initialize gtag
      window.dataLayer = window.dataLayer || [];
      function gtag() { window.dataLayer.push(arguments); }
      window.gtag = gtag;

      gtag('js', new Date());
      gtag('config', gaId, {
        send_page_view: false, // We'll handle this manually
        anonymize_ip: true,
        allow_google_signals: false,
        custom_map: {
          custom_parameter_1: 'device_type',
          custom_parameter_2: 'user_type',
        },
      });

      console.log('Google Analytics initialized');
    } catch (error) {
      console.error('Failed to initialize Google Analytics:', error);
    }
  }

  // Facebook Pixel
  initializeFacebookPixel() {
    const pixelId = process.env.REACT_APP_FACEBOOK_PIXEL_ID;
    if (!pixelId) return;

    try {
      !function(f,b,e,v,n,t,s)
      {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)};
      if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
      n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t,s)}(window, document,'script',
      'https://connect.facebook.net/en_US/fbevents.js');

      window.fbq('init', pixelId);
      window.fbq('track', 'PageView');

      console.log('Facebook Pixel initialized');
    } catch (error) {
      console.error('Failed to initialize Facebook Pixel:', error);
    }
  }

  // Hotjar
  initializeHotjar() {
    const hotjarId = process.env.REACT_APP_HOTJAR_ID;
    if (!hotjarId) return;

    try {
      (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:hotjarId,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
      })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');

      console.log('Hotjar initialized');
    } catch (error) {
      console.error('Failed to initialize Hotjar:', error);
    }
  }

  // Sentry Error Monitoring
  initializeSentry() {
    const sentryDsn = process.env.REACT_APP_SENTRY_DSN;
    if (!sentryDsn) return;

    try {
      // This would require @sentry/react package
      // import * as Sentry from "@sentry/react";
      
      // Sentry.init({
      //   dsn: sentryDsn,
      //   environment: process.env.NODE_ENV,
      //   beforeSend(event) {
      //     // Filter sensitive data
      //     return event;
      //   },
      // });

      console.log('Sentry initialized');
    } catch (error) {
      console.error('Failed to initialize Sentry:', error);
    }
  }

  // Automatic tracking setup
  setupPageViewTracking() {
    // Track initial page view
    this.trackPageView(window.location.pathname);

    // Track navigation changes (for SPA)
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function(...args) {
      originalPushState.apply(history, args);
      setTimeout(() => {
        analyticsService.trackPageView(window.location.pathname);
      }, 0);
    };

    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args);
      setTimeout(() => {
        analyticsService.trackPageView(window.location.pathname);
      }, 0);
    };

    window.addEventListener('popstate', () => {
      this.trackPageView(window.location.pathname);
    });
  }

  setupErrorTracking() {
    window.addEventListener('error', (event) => {
      this.trackError({
        type: 'javascript_error',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
      });
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.trackError({
        type: 'unhandled_promise_rejection',
        message: event.reason?.message || 'Unhandled promise rejection',
        stack: event.reason?.stack,
      });
    });
  }

  setupPerformanceTracking() {
    // Track page load performance
    window.addEventListener('load', () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType('navigation')[0];
        if (perfData) {
          this.trackPerformance('page_load', {
            loadTime: perfData.loadEventEnd - perfData.loadEventStart,
            domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
            firstPaint: this.getFirstPaint(),
            firstContentfulPaint: this.getFirstContentfulPaint(),
          });
        }
      }, 0);
    });

    // Track Core Web Vitals
    this.trackWebVitals();
  }

  setupUserInteractionTracking() {
    // Track clicks on important elements
    document.addEventListener('click', (event) => {
      const target = event.target;
      
      // Track button clicks
      if (target.tagName === 'BUTTON' || target.type === 'button') {
        this.trackEvent('button_click', {
          buttonText: target.textContent?.trim(),
          buttonId: target.id,
          buttonClass: target.className,
        });
      }
      
      // Track link clicks
      if (target.tagName === 'A') {
        this.trackEvent('link_click', {
          linkText: target.textContent?.trim(),
          linkHref: target.href,
          linkId: target.id,
        });
      }
    });

    // Track form submissions
    document.addEventListener('submit', (event) => {
      const form = event.target;
      this.trackEvent('form_submit', {
        formId: form.id,
        formClass: form.className,
        formAction: form.action,
      });
    });
  }

  // Core tracking methods
  trackEvent(eventName, parameters = {}) {
    if (!this.isEnabled) return;

    const event = {
      name: eventName,
      parameters: {
        ...parameters,
        session_id: this.sessionId,
        user_id: this.userId,
        timestamp: new Date().toISOString(),
        page_url: window.location.href,
        page_title: document.title,
        device_type: this.deviceInfo.deviceType,
      },
    };

    // Store locally
    this.events.push(event);

    // Send to Google Analytics
    if (window.gtag) {
      window.gtag('event', eventName, event.parameters);
    }

    // Send to Facebook Pixel
    if (window.fbq) {
      window.fbq('track', eventName, event.parameters);
    }

    // Send to backend
    this.sendToBackend('event', event);

    console.log('Analytics Event:', event);
  }

  trackPageView(path) {
    if (!this.isEnabled) return;

    const pageView = {
      page_path: path,
      page_title: document.title,
      page_url: window.location.href,
      session_id: this.sessionId,
      user_id: this.userId,
      timestamp: new Date().toISOString(),
      device_type: this.deviceInfo.deviceType,
    };

    // Store locally
    this.pageViews.push(pageView);

    // Send to Google Analytics
    if (window.gtag) {
      window.gtag('config', process.env.REACT_APP_GOOGLE_ANALYTICS_ID, {
        page_path: path,
        page_title: document.title,
      });
    }

    // Send to Facebook Pixel
    if (window.fbq) {
      window.fbq('track', 'PageView');
    }

    // Send to backend
    this.sendToBackend('pageview', pageView);

    console.log('Analytics Page View:', pageView);
  }

  trackError(errorInfo) {
    if (!this.isEnabled) return;

    const error = {
      ...errorInfo,
      session_id: this.sessionId,
      user_id: this.userId,
      timestamp: new Date().toISOString(),
      page_url: window.location.href,
      device_type: this.deviceInfo.deviceType,
    };

    // Store locally
    this.errors.push(error);

    // Send to backend
    this.sendToBackend('error', error);

    // Log to security logger
    SecurityLogger.logSecurityEvent('JAVASCRIPT_ERROR', error);

    console.error('Analytics Error:', error);
  }

  trackPerformance(metricName, data) {
    if (!this.isEnabled) return;

    const performance = {
      metric: metricName,
      data,
      session_id: this.sessionId,
      user_id: this.userId,
      timestamp: new Date().toISOString(),
      page_url: window.location.href,
      device_type: this.deviceInfo.deviceType,
    };

    // Store locally
    this.performance.push(performance);

    // Send to backend
    this.sendToBackend('performance', performance);

    console.log('Analytics Performance:', performance);
  }

  // User identification
  setUserId(userId) {
    this.userId = userId;
    
    if (window.gtag) {
      window.gtag('config', process.env.REACT_APP_GOOGLE_ANALYTICS_ID, {
        user_id: userId,
      });
    }

    if (window.fbq) {
      window.fbq('track', 'Login');
    }
  }

  setUserProperties(properties) {
    if (!this.isEnabled) return;

    if (window.gtag) {
      window.gtag('set', properties);
    }

    this.trackEvent('user_properties_set', properties);
  }

  // E-commerce tracking
  trackPurchase(transactionData) {
    if (!this.isEnabled) return;

    const purchase = {
      transaction_id: transactionData.transactionId,
      value: transactionData.value,
      currency: transactionData.currency || 'TZS',
      items: transactionData.items,
    };

    if (window.gtag) {
      window.gtag('event', 'purchase', purchase);
    }

    if (window.fbq) {
      window.fbq('track', 'Purchase', {
        value: transactionData.value,
        currency: transactionData.currency || 'TZS',
      });
    }

    this.trackEvent('purchase', purchase);
  }

  // Utility methods
  getFirstPaint() {
    const paintEntries = performance.getEntriesByType('paint');
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
    return firstPaint ? firstPaint.startTime : null;
  }

  getFirstContentfulPaint() {
    const paintEntries = performance.getEntriesByType('paint');
    const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    return fcp ? fcp.startTime : null;
  }

  trackWebVitals() {
    // This would require web-vitals library
    // import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';
    
    // getCLS((metric) => this.trackPerformance('CLS', metric));
    // getFID((metric) => this.trackPerformance('FID', metric));
    // getFCP((metric) => this.trackPerformance('FCP', metric));
    // getLCP((metric) => this.trackPerformance('LCP', metric));
    // getTTFB((metric) => this.trackPerformance('TTFB', metric));
  }

  async sendToBackend(type, data) {
    try {
      // Send analytics data to backend (optional)
      await fetch('/api/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          data,
          timestamp: new Date().toISOString(),
        }),
      });
    } catch (error) {
      // Fail silently for analytics
      console.debug('Failed to send analytics to backend:', error);
    }
  }

  // Session management
  getSessionData() {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
      startTime: this.startTime,
      duration: Date.now() - this.startTime,
      events: this.events.length,
      pageViews: this.pageViews.length,
      errors: this.errors.length,
      deviceInfo: this.deviceInfo,
    };
  }

  endSession() {
    const sessionData = this.getSessionData();
    this.trackEvent('session_end', sessionData);
    
    console.log('Analytics Session Ended:', sessionData);
  }
}

// Create singleton instance
const analyticsService = new AnalyticsService();

// Track session end on page unload
window.addEventListener('beforeunload', () => {
  analyticsService.endSession();
});

export default analyticsService;
