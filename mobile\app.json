{"expo": {"name": "ProChat", "slug": "prochat-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#007AFF"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.prochat.app", "buildNumber": "1.0.0", "infoPlist": {"NSCameraUsageDescription": "ProChat needs camera access to take photos and videos for posts and profile pictures.", "NSMicrophoneUsageDescription": "ProChat needs microphone access for voice messages and live streaming.", "NSPhotoLibraryUsageDescription": "ProChat needs photo library access to share photos and videos.", "NSLocationWhenInUseUsageDescription": "ProChat uses location to show nearby events and users.", "NSContactsUsageDescription": "ProChat accesses contacts to help you find friends on the platform."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.ramstech.prochat", "versionCode": 1, "permissions": ["CAMERA", "RECORD_AUDIO", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "READ_CONTACTS", "INTERNET", "ACCESS_NETWORK_STATE", "WAKE_LOCK", "VIBRATE"], "useNextNotificationsApi": true}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "prochat-mobile-app"}}, "plugins": ["expo-camera", "expo-av", "expo-location", "expo-contacts", "expo-notifications", ["expo-image-picker", {"photosPermission": "ProChat needs access to your photos to share images.", "cameraPermission": "ProChat needs access to your camera to take photos."}], ["@react-native-firebase/app", {"android": {"googleServicesFile": "./google-services.json"}}]]}}