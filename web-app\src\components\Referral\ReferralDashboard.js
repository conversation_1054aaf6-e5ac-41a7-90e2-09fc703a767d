// ProChat Referral Dashboard Component
// Help users invite friends and earn amazing bonuses!

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Alert,
  Chip,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Fab,
  Snackbar,
} from '@mui/material';
import {
  Share,
  ContentCopy,
  WhatsApp,
  Telegram,
  Facebook,
  Twitter,
  Email,
  Sms,
  QrCode,
  EmojiEvents,
  MonetizationOn,
  People,
  TrendingUp,
  Gift,
  Star,
  CheckCircle,
} from '@mui/icons-material';

import { useAuth } from '../../contexts/AuthContext';
import { useDeviceDetection } from '../../hooks/useDeviceDetection';
import referralBonusService from '../../services/referralBonusSystem';

const ReferralDashboard = () => {
  const { user } = useAuth();
  const { isMobile } = useDeviceDetection();
  const [referralStats, setReferralStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [shareDialog, setShareDialog] = useState(false);
  const [qrDialog, setQrDialog] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  useEffect(() => {
    loadReferralStats();
  }, [user]);

  const loadReferralStats = async () => {
    try {
      setLoading(true);
      const stats = await referralBonusService.getReferralStats(user.id);
      setReferralStats(stats);
    } catch (error) {
      console.error('Failed to load referral stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyReferralCode = () => {
    const referralLink = `https://prochat.app/join?ref=${referralStats.referralCode}`;
    navigator.clipboard.writeText(referralLink);
    setCopySuccess(true);
  };

  const handleShare = (platform) => {
    const referralLink = `https://prochat.app/join?ref=${referralStats.referralCode}`;
    const message = `🎉 Join ProChat and get TZS 1,500 FREE! I'll get TZS 2,000 when you join! Use my code: ${referralStats.referralCode} 💰`;
    
    const urls = {
      whatsapp: `https://wa.me/?text=${encodeURIComponent(message + ' ' + referralLink)}`,
      telegram: `https://t.me/share/url?url=${encodeURIComponent(referralLink)}&text=${encodeURIComponent(message)}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(referralLink)}`,
      twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}&url=${encodeURIComponent(referralLink)}`,
      email: `mailto:?subject=Join ProChat and Get FREE Money!&body=${encodeURIComponent(message + '\n\n' + referralLink)}`,
      sms: `sms:?body=${encodeURIComponent(message + ' ' + referralLink)}`,
    };

    if (urls[platform]) {
      window.open(urls[platform], '_blank');
    }
  };

  const StatCard = ({ title, value, subtitle, icon, color = 'primary', progress }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {icon}
          <Typography variant="h6" sx={{ ml: 1, flexGrow: 1 }}>
            {title}
          </Typography>
        </Box>
        <Typography variant="h3" color={`${color}.main`} sx={{ fontWeight: 'bold', mb: 1 }}>
          {value}
        </Typography>
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
        {progress !== undefined && (
          <Box sx={{ mt: 2 }}>
            <LinearProgress 
              variant="determinate" 
              value={progress} 
              color={color}
              sx={{ height: 8, borderRadius: 4 }}
            />
            <Typography variant="caption" color="text.secondary">
              {Math.round(progress)}% complete
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );

  const MilestoneCard = ({ milestone, current, target, reward, achieved }) => {
    const progress = (current / target) * 100;
    
    return (
      <Card sx={{ mb: 2, opacity: achieved ? 0.8 : 1 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <EmojiEvents 
              color={achieved ? 'success' : 'primary'} 
              sx={{ mr: 2 }}
            />
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="h6">
                {milestone}
                {achieved && <CheckCircle color="success" sx={{ ml: 1 }} />}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Invite {target} friends to earn TZS {reward.toLocaleString()}
              </Typography>
            </Box>
            <Chip 
              label={achieved ? 'Achieved!' : `${current}/${target}`}
              color={achieved ? 'success' : 'primary'}
              variant={achieved ? 'filled' : 'outlined'}
            />
          </Box>
          
          {!achieved && (
            <Box>
              <LinearProgress 
                variant="determinate" 
                value={Math.min(progress, 100)} 
                color="primary"
                sx={{ height: 8, borderRadius: 4, mb: 1 }}
              />
              <Typography variant="caption" color="text.secondary">
                {target - current} more friends needed
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" sx={{ mb: 2 }}>Referral Dashboard</Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: isMobile ? 2 : 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Share sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          🤝 Invite Friends & Earn!
        </Typography>
      </Box>

      {/* Welcome Message */}
      <Alert severity="success" sx={{ mb: 3 }}>
        <Typography variant="h6">
          💰 Earn TZS 2,000 for each friend you invite!
        </Typography>
        <Typography variant="body2">
          Your friends get TZS 1,500 welcome bonus too! It's a win-win! 🎉
        </Typography>
      </Alert>

      {/* Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Referrals"
            value={referralStats.totalReferrals || 0}
            subtitle="Friends you've invited"
            icon={<People color="primary" />}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Earned"
            value={`TZS ${(referralStats.totalEarned || 0).toLocaleString()}`}
            subtitle="From referral bonuses"
            icon={<MonetizationOn color="success" />}
            color="success"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Referrals"
            value={referralStats.activeReferrals?.length || 0}
            subtitle="Friends still using app"
            icon={<TrendingUp color="info" />}
            color="info"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Next Milestone"
            value="5 Friends"
            subtitle="TZS 5,000 bonus waiting!"
            icon={<Gift color="warning" />}
            color="warning"
            progress={((referralStats.totalReferrals || 0) / 5) * 100}
          />
        </Grid>
      </Grid>

      {/* Referral Code Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            🔗 Your Referral Code
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <TextField
              fullWidth
              value={`https://prochat.app/join?ref=${referralStats.referralCode || 'LOADING'}`}
              InputProps={{
                readOnly: true,
                endAdornment: (
                  <IconButton onClick={handleCopyReferralCode}>
                    <ContentCopy />
                  </IconButton>
                ),
              }}
              sx={{ mr: 2 }}
            />
            <Button
              variant="contained"
              startIcon={<QrCode />}
              onClick={() => setQrDialog(true)}
            >
              QR Code
            </Button>
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Share this link with friends. They get TZS 1,500, you get TZS 2,000!
          </Typography>

          {/* Share Buttons */}
          <Typography variant="h6" sx={{ mb: 2 }}>
            📱 Share Now
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={6} sm={4} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<WhatsApp />}
                onClick={() => handleShare('whatsapp')}
                sx={{ color: '#25D366', borderColor: '#25D366' }}
              >
                WhatsApp
              </Button>
            </Grid>
            
            <Grid item xs={6} sm={4} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Telegram />}
                onClick={() => handleShare('telegram')}
                sx={{ color: '#0088cc', borderColor: '#0088cc' }}
              >
                Telegram
              </Button>
            </Grid>
            
            <Grid item xs={6} sm={4} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook />}
                onClick={() => handleShare('facebook')}
                sx={{ color: '#1877f2', borderColor: '#1877f2' }}
              >
                Facebook
              </Button>
            </Grid>
            
            <Grid item xs={6} sm={4} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Twitter />}
                onClick={() => handleShare('twitter')}
                sx={{ color: '#1da1f2', borderColor: '#1da1f2' }}
              >
                Twitter
              </Button>
            </Grid>
            
            <Grid item xs={6} sm={4} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Email />}
                onClick={() => handleShare('email')}
              >
                Email
              </Button>
            </Grid>
            
            <Grid item xs={6} sm={4} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Sms />}
                onClick={() => handleShare('sms')}
              >
                SMS
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Milestone Progress */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                🏆 Milestone Rewards
              </Typography>
              
              <MilestoneCard
                milestone="5 Friends Milestone"
                current={referralStats.totalReferrals || 0}
                target={5}
                reward={5000}
                achieved={(referralStats.totalReferrals || 0) >= 5}
              />
              
              <MilestoneCard
                milestone="10 Friends Milestone"
                current={referralStats.totalReferrals || 0}
                target={10}
                reward={15000}
                achieved={(referralStats.totalReferrals || 0) >= 10}
              />
              
              <MilestoneCard
                milestone="25 Friends Milestone"
                current={referralStats.totalReferrals || 0}
                target={25}
                reward={50000}
                achieved={(referralStats.totalReferrals || 0) >= 25}
              />
              
              <MilestoneCard
                milestone="50 Friends Milestone"
                current={referralStats.totalReferrals || 0}
                target={50}
                reward={150000}
                achieved={(referralStats.totalReferrals || 0) >= 50}
              />
              
              <MilestoneCard
                milestone="100 Friends Milestone"
                current={referralStats.totalReferrals || 0}
                target={100}
                reward={500000}
                achieved={(referralStats.totalReferrals || 0) >= 100}
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          {/* Recent Referrals */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                👥 Recent Referrals
              </Typography>
              
              {referralStats.recentReferrals?.length > 0 ? (
                <List>
                  {referralStats.recentReferrals.map((referral, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          {referral.name?.charAt(0) || '?'}
                        </Avatar>
                      </ListItemIcon>
                      <ListItemText
                        primary={referral.name || 'New User'}
                        secondary={`Joined ${new Date(referral.joinedAt).toLocaleDateString()}`}
                      />
                      <Chip label="TZS 2,000" color="success" size="small" />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No referrals yet. Start inviting friends!
                </Typography>
              )}
            </CardContent>
          </Card>

          {/* Tips */}
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                💡 Referral Tips
              </Typography>
              
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <Star color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Share on WhatsApp groups"
                    secondary="Reach many friends at once"
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <Star color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Post on social media"
                    secondary="Let everyone know about free money"
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <Star color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Tell family members"
                    secondary="They trust you most"
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <Star color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Share at work/school"
                    secondary="Colleagues love free money too"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => setShareDialog(true)}
      >
        <Share />
      </Fab>

      {/* Share Dialog */}
      <Dialog open={shareDialog} onClose={() => setShareDialog(false)}>
        <DialogTitle>Share Your Referral Link</DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Choose how you want to share your referral link:
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<WhatsApp />}
                onClick={() => {
                  handleShare('whatsapp');
                  setShareDialog(false);
                }}
              >
                WhatsApp
              </Button>
            </Grid>
            <Grid item xs={6}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Telegram />}
                onClick={() => {
                  handleShare('telegram');
                  setShareDialog(false);
                }}
              >
                Telegram
              </Button>
            </Grid>
            <Grid item xs={6}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook />}
                onClick={() => {
                  handleShare('facebook');
                  setShareDialog(false);
                }}
              >
                Facebook
              </Button>
            </Grid>
            <Grid item xs={6}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<ContentCopy />}
                onClick={() => {
                  handleCopyReferralCode();
                  setShareDialog(false);
                }}
              >
                Copy Link
              </Button>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShareDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* QR Code Dialog */}
      <Dialog open={qrDialog} onClose={() => setQrDialog(false)}>
        <DialogTitle>QR Code</DialogTitle>
        <DialogContent>
          <Box sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Let friends scan this QR code to join with your referral link:
            </Typography>
            
            {/* QR Code would be generated here */}
            <Box
              sx={{
                width: 200,
                height: 200,
                bgcolor: 'grey.200',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mx: 'auto',
                mb: 2,
              }}
            >
              <QrCode sx={{ fontSize: 100, color: 'grey.600' }} />
            </Box>
            
            <Typography variant="caption" color="text.secondary">
              QR Code for: {referralStats.referralCode}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setQrDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Copy Success Snackbar */}
      <Snackbar
        open={copySuccess}
        autoHideDuration={3000}
        onClose={() => setCopySuccess(false)}
        message="Referral link copied to clipboard!"
      />
    </Box>
  );
};

export default ReferralDashboard;
