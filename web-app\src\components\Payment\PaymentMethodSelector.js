// ProChat Payment Method Selector Component
// Allows users to select from all available payment methods

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Chip,
  Avatar,
  Divider,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Phone,
  AccountBalance,
  CreditCard,
  CurrencyBitcoin,
  CheckCircle,
  Warning,
  Info,
} from '@mui/icons-material';
import paymentAPI from '../../api/paymentAPI';

const PaymentMethodSelector = ({ 
  amount, 
  currency = 'TZS', 
  onMethodSelect, 
  selectedMethod,
  showFees = true,
  excludeProviders = [],
}) => {
  const [providers, setProviders] = useState({});
  const [loading, setLoading] = useState(true);
  const [fees, setFees] = useState({});
  const [error, setError] = useState(null);

  useEffect(() => {
    loadAvailableProviders();
  }, []);

  useEffect(() => {
    if (amount && showFees) {
      calculateFeesForAllProviders();
    }
  }, [amount, currency, providers]);

  const loadAvailableProviders = async () => {
    try {
      setLoading(true);
      const response = await paymentAPI.getAvailableProviders();
      setProviders(response);
    } catch (error) {
      setError('Imeshindwa kupata njia za malipo');
    } finally {
      setLoading(false);
    }
  };

  const calculateFeesForAllProviders = async () => {
    const allProviders = [
      ...Object.keys(providers.mobileMoneyProviders || {}),
      ...Object.keys(providers.bankingProviders || {}),
      ...Object.keys(providers.cardProviders || {}),
      ...Object.keys(providers.cryptoProviders || {}),
    ];

    const feePromises = allProviders.map(async (provider) => {
      try {
        const feeData = await paymentAPI.calculateFees(amount, currency, provider);
        return { provider, fees: feeData };
      } catch (error) {
        return { provider, fees: null };
      }
    });

    const feeResults = await Promise.all(feePromises);
    const feeMap = {};
    feeResults.forEach(({ provider, fees }) => {
      if (fees) {
        feeMap[provider] = fees;
      }
    });
    setFees(feeMap);
  };

  const getProviderIcon = (provider) => {
    const iconMap = {
      // Mobile Money
      mpesa: '📱',
      airtel_money: '📱',
      tigo_pesa: '📱',
      halopesa: '📱',
      azampesa: '📱',
      nara: '📱',
      
      // Banks
      crdb_bank: '🏦',
      nmb_bank: '🏦',
      nbc_bank: '🏦',
      
      // Cards
      visa: '💳',
      mastercard: '💳',
      stripe: '💳',
      
      // Crypto
      bitcoin: '₿',
      usdt: '💰',
    };
    return iconMap[provider] || '💳';
  };

  const getProviderName = (provider) => {
    const nameMap = {
      mpesa: 'M-Pesa',
      airtel_money: 'Airtel Money',
      tigo_pesa: 'Tigo Pesa (MIXX)',
      halopesa: 'HaloPesa',
      azampesa: 'AzamPesa',
      nara: 'NARA',
      crdb_bank: 'CRDB Bank',
      nmb_bank: 'NMB Bank',
      nbc_bank: 'NBC Bank',
      visa: 'Visa',
      mastercard: 'Mastercard',
      stripe: 'International Cards',
      bitcoin: 'Bitcoin',
      usdt: 'USDT',
    };
    return nameMap[provider] || provider;
  };

  const getProviderDescription = (provider) => {
    const descMap = {
      mpesa: 'Lipa kwa M-Pesa - Njia rahisi na salama',
      airtel_money: 'Airtel Money - Haraka na rahisi',
      tigo_pesa: 'Tigo Pesa - MIXX BY YAS',
      halopesa: 'HaloPesa - Malipo ya haraka',
      azampesa: 'AzamPesa - Malipo salama',
      nara: 'NARA - Huduma za kifedha',
      crdb_bank: 'CRDB Bank - Benki ya kwanza Tanzania',
      nmb_bank: 'NMB Bank - Benki ya Taifa',
      nbc_bank: 'NBC Bank - Benki ya Biashara',
      visa: 'Visa - Kadi za kimataifa',
      mastercard: 'Mastercard - Kadi za kimataifa',
      stripe: 'Kadi za kimataifa - Visa, Mastercard',
      bitcoin: 'Bitcoin - Sarafu ya kidijitali',
      usdt: 'USDT - Dola za kidijitali',
    };
    return descMap[provider] || 'Njia ya malipo';
  };

  const formatFee = (fee) => {
    if (!fee) return '';
    const total = fee.percentage + fee.fixed;
    return `Ada: ${currency} ${total.toLocaleString()}`;
  };

  const renderProviderCard = (provider, category) => {
    if (excludeProviders.includes(provider)) return null;

    const isSelected = selectedMethod === provider;
    const providerFees = fees[provider];

    return (
      <Grid item xs={12} sm={6} md={4} key={provider}>
        <Card
          sx={{
            cursor: 'pointer',
            border: isSelected ? 2 : 1,
            borderColor: isSelected ? 'primary.main' : 'grey.300',
            '&:hover': {
              borderColor: 'primary.main',
              boxShadow: 2,
            },
            position: 'relative',
          }}
          onClick={() => onMethodSelect(provider)}
        >
          {isSelected && (
            <CheckCircle
              sx={{
                position: 'absolute',
                top: 8,
                right: 8,
                color: 'primary.main',
              }}
            />
          )}
          
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar sx={{ mr: 2, bgcolor: 'primary.light' }}>
                {getProviderIcon(provider)}
              </Avatar>
              <Box>
                <Typography variant="h6" component="div">
                  {getProviderName(provider)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {getProviderDescription(provider)}
                </Typography>
              </Box>
            </Box>

            {showFees && providerFees && (
              <Box sx={{ mt: 2 }}>
                <Chip
                  label={formatFee(providerFees)}
                  size="small"
                  color="info"
                  variant="outlined"
                />
              </Box>
            )}

            <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
              {category === 'mobile' && (
                <Chip label="Simu" size="small" icon={<Phone />} />
              )}
              {category === 'bank' && (
                <Chip label="Benki" size="small" icon={<AccountBalance />} />
              )}
              {category === 'card' && (
                <Chip label="Kadi" size="small" icon={<CreditCard />} />
              )}
              {category === 'crypto' && (
                <Chip label="Crypto" size="small" icon={<CurrencyBitcoin />} />
              )}
            </Box>
          </CardContent>
        </Card>
      </Grid>
    );
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Chagua Njia ya Malipo
      </Typography>

      {amount && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            Kiasi: {currency} {amount.toLocaleString()}
          </Typography>
        </Alert>
      )}

      {/* Mobile Money Providers */}
      {providers.mobileMoneyProviders && Object.keys(providers.mobileMoneyProviders).length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <Phone sx={{ mr: 1 }} />
            Malipo ya Simu
          </Typography>
          <Grid container spacing={2}>
            {Object.keys(providers.mobileMoneyProviders).map(provider =>
              renderProviderCard(provider, 'mobile')
            )}
          </Grid>
        </Box>
      )}

      <Divider sx={{ my: 3 }} />

      {/* Banking Providers */}
      {providers.bankingProviders && Object.keys(providers.bankingProviders).length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <AccountBalance sx={{ mr: 1 }} />
            Malipo ya Benki
          </Typography>
          <Grid container spacing={2}>
            {Object.keys(providers.bankingProviders).map(provider =>
              renderProviderCard(provider, 'bank')
            )}
          </Grid>
        </Box>
      )}

      <Divider sx={{ my: 3 }} />

      {/* Card Providers */}
      {providers.cardProviders && Object.keys(providers.cardProviders).length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <CreditCard sx={{ mr: 1 }} />
            Kadi za Kimataifa
          </Typography>
          <Grid container spacing={2}>
            {Object.keys(providers.cardProviders).map(provider =>
              renderProviderCard(provider, 'card')
            )}
          </Grid>
        </Box>
      )}

      <Divider sx={{ my: 3 }} />

      {/* Crypto Providers */}
      {providers.cryptoProviders && Object.keys(providers.cryptoProviders).length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <CurrencyBitcoin sx={{ mr: 1 }} />
            Sarafu za Kidijitali
          </Typography>
          <Grid container spacing={2}>
            {Object.keys(providers.cryptoProviders).map(provider =>
              renderProviderCard(provider, 'crypto')
            )}
          </Grid>
        </Box>
      )}

      {!selectedMethod && (
        <Alert severity="warning" sx={{ mt: 3 }}>
          <Typography variant="body2">
            Tafadhali chagua njia ya malipo ili kuendelea
          </Typography>
        </Alert>
      )}
    </Box>
  );
};

export default PaymentMethodSelector;
