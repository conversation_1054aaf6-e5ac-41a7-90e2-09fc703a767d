import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { StatusBar } from 'expo-status-bar';
import { Provider as PaperProvider } from 'react-native-paper';
import { Alert, View, Text, Platform } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Auth Screens
import WelcomeScreen from '../mobile/src/screens/auth/WelcomeScreen';
import LoginScreen from '../mobile/src/screens/auth/LoginScreen';
import RegisterScreen from '../mobile/src/screens/auth/RegisterScreen';
import ForgotPasswordScreen from '../mobile/src/screens/auth/ForgotPasswordScreen';

// Main Tab Screens
import ChatsScreen from '../mobile/src/screens/tabs/ChatsScreen';
import HomeScreen from '../mobile/src/screens/tabs/HomeScreen';
import DiscoverScreen from '../mobile/src/screens/tabs/DiscoverScreen';
import MeScreen from '../mobile/src/screens/tabs/MeScreen';

  const renderTabContent = () => {
    switch (activeTab) {
      case 'Home':
        return (
          <ScrollView style={styles.content}>
            <View style={styles.card}>
              <Text style={styles.cardTitle}>📱 Machapisho ya Kijamii</Text>
              <View style={styles.post}>
                <Text style={styles.postAuthor}>👤 John Doe</Text>
                <Text style={styles.postContent}>Habari za asubuhi! Leo ni siku nzuri ya kufanya biashara.</Text>
                <View style={styles.postActions}>
                  <Text style={styles.postAction}>❤️ 15</Text>
                  <Text style={styles.postAction}>💬 3</Text>
                  <Text style={styles.postAction}>📤 Share</Text>
                </View>
              </View>
            </View>
          </ScrollView>
        );
      case 'Chats':
        return (
          <ScrollView style={styles.content}>
            <View style={styles.card}>
              <Text style={styles.cardTitle}>💬 Mazungumzo</Text>
              <View style={styles.chatItem}>
                <Text style={styles.chatName}>👥 Family Group</Text>
                <Text style={styles.chatMessage}>Habari za leo?</Text>
              </View>
            </View>
          </ScrollView>
        );
      case 'Discover':
        return (
          <ScrollView style={styles.content}>
            <View style={styles.card}>
              <Text style={styles.cardTitle}>🎫 Matukio</Text>
              <View style={styles.listItem}>
                <Text style={styles.itemTitle}>Muziki wa Bongo Flava</Text>
                <Text style={styles.itemDetails}>📅 2024-12-15 | 📍 Mlimani City</Text>
              </View>
            </View>
          </ScrollView>
        );
      case 'Me':
        return (
          <ScrollView style={styles.content}>
            <View style={[styles.card, styles.walletCard]}>
              <Text style={styles.walletTitle}>💰 ProPay Wallet</Text>
              <Text style={styles.balance}>TZS 250,000</Text>
            </View>
          </ScrollView>
        );
      default:
        return <Text>Tab not found</Text>;
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>🚀 ProChat</Text>
        <Text style={styles.headerSubtitle}>Tanzania's Super App</Text>
      </View>

      <View style={styles.body}>
        {renderTabContent()}
      </View>

      <View style={styles.tabBar}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.name}
            style={[
              styles.tab,
              activeTab === tab.name && { backgroundColor: tab.color }
            ]}
            onPress={() => setActiveTab(tab.name)}
          >
            <Text style={styles.tabIcon}>{tab.icon}</Text>
            <Text style={[
              styles.tabLabel,
              activeTab === tab.name && { color: 'white' }
            ]}>
              {tab.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  header: {
    backgroundColor: '#667eea',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
  },
  body: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 16,
  },
  post: {
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
    paddingBottom: 12,
    marginBottom: 12,
  },
  postAuthor: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#495057',
    marginBottom: 8,
  },
  postContent: {
    fontSize: 14,
    color: '#6c757d',
    lineHeight: 20,
    marginBottom: 8,
  },
  postActions: {
    flexDirection: 'row',
    gap: 16,
  },
  postAction: {
    fontSize: 12,
    color: '#6c757d',
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  chatName: {
    flex: 1,
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  chatMessage: {
    flex: 2,
    fontSize: 12,
    color: '#6c757d',
  },
  listItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  itemTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  itemDetails: {
    fontSize: 12,
    color: '#6c757d',
  },
  walletCard: {
    backgroundColor: '#667eea',
  },
  walletTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
  },
  balance: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 20,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingVertical: 8,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  tabIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  tabLabel: {
    fontSize: 10,
    color: '#6c757d',
    fontWeight: 'bold',
  },
});
