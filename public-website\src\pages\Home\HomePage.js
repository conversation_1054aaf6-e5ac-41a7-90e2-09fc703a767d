import React from 'react';
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  IconButton,
} from '@mui/material';
import {
  Chat,
  AccountBalance,
  Event,
  Work,
  Videocam,
  Security,
  Speed,
  Public,
  GetApp,
  PlayArrow,
  SmartToy,
  ArrowForward,
  Close,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const features = [
  {
    icon: <Chat />,
    title: 'Mazung<PERSON>zo Salama',
    description: '<PERSON><PERSON> simu, tuma ujumbe, na ushiriki maudhui kwa usalama wa hali ya juu.',
  },
  {
    icon: <AccountBalance />,
    title: 'ProPay Wallet',
    description: 'Tuma na pokea pesa, lipa bili, na fanya biashara kwa urahisi.',
  },
  {
    icon: <Event />,
    title: '<PERSON><PERSON><PERSON>',
    description: '<PERSON><PERSON> matukio ya karibu, nunua tiketi, na unda matukio yako.',
  },
  {
    icon: <Work />,
    title: '<PERSON><PERSON> na Zabuni',
    description: '<PERSON><PERSON><PERSON> kazi, omba kazi, na pata fursa za kibiashara.',
  },
  {
    icon: <Videocam />,
    title: 'Live Streaming',
    description: '<PERSON><PERSON><PERSON> talanta yako na pata pesa kupitia zawadi za waonaji.',
  },
  {
    icon: <Security />,
    title: 'Usalama wa Hali ya Juu',
    description: 'Data yako ni salama na mazungumzo yako yamehifadhiwa.',
  },
];

const stats = [
  { number: '500K+', label: 'Watumiaji' },
  { number: '1M+', label: 'Miamala' },
  { number: '50K+', label: 'Matukio' },
  { number: '99.9%', label: 'Uptime' },
];

export default function HomePage() {
  const navigate = useNavigate();
  const [showJobsBanner, setShowJobsBanner] = React.useState(true);

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #007AFF 0%, #4DA3FF 100%)',
          color: 'white',
          py: { xs: 8, md: 12 },
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography
                variant="h1"
                component="h1"
                gutterBottom
                data-aos="fade-up"
                sx={{ fontSize: { xs: '2.5rem', md: '3.5rem' } }}
              >
                Karibu ProChat
              </Typography>
              <Typography
                variant="h5"
                component="p"
                sx={{ mb: 4, opacity: 0.9, lineHeight: 1.6 }}
                data-aos="fade-up"
                data-aos-delay="200"
              >
                Jukwaa la kisasa la mitandao ya kijamii na miamala ya kifedha. 
                Changanya mazungumzo, biashara, na burudani katika programu moja.
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }} data-aos="fade-up" data-aos-delay="400">
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<GetApp />}
                  sx={{
                    backgroundColor: 'white',
                    color: 'primary.main',
                    '&:hover': {
                      backgroundColor: 'grey.100',
                    },
                  }}
                >
                  Pakua Sasa
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<PlayArrow />}
                  sx={{
                    borderColor: 'white',
                    color: 'white',
                    '&:hover': {
                      borderColor: 'white',
                      backgroundColor: 'rgba(255,255,255,0.1)',
                    },
                  }}
                >
                  Ona Video
                </Button>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: { xs: 300, md: 500 },
                }}
                data-aos="fade-left"
                data-aos-delay="600"
              >
                {/* Phone Mockup */}
                <Box
                  sx={{
                    width: { xs: 200, md: 300 },
                    height: { xs: 350, md: 500 },
                    backgroundColor: 'rgba(255,255,255,0.1)',
                    borderRadius: 6,
                    border: '2px solid rgba(255,255,255,0.2)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Typography variant="h6" sx={{ opacity: 0.7 }}>
                    ProChat App
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Jobs Platform Announcement Banner */}
      {showJobsBanner && (
        <Box
          sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            py: 3,
            position: 'relative',
          }}
        >
          <Container maxWidth="lg">
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <SmartToy sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                    🚀 MPYA! Jukwaa la Kazi la AI
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Tafuta kazi, fanya mahojiano ya AI, na pata ajira kwa teknolojia ya kisasa zaidi duniani!
                  </Typography>
                </Box>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Button
                  variant="contained"
                  onClick={() => navigate('/jobs')}
                  sx={{
                    bgcolor: 'white',
                    color: 'primary.main',
                    '&:hover': { bgcolor: 'grey.100' },
                  }}
                  endIcon={<ArrowForward />}
                >
                  Jaribu Sasa
                </Button>
                <IconButton
                  onClick={() => setShowJobsBanner(false)}
                  sx={{ color: 'white' }}
                >
                  <Close />
                </IconButton>
              </Box>
            </Box>
          </Container>
        </Box>
      )}

      {/* Stats Section */}
      <Box sx={{ py: 6, backgroundColor: 'grey.50' }}>
        <Container maxWidth="lg">
          <Grid container spacing={4}>
            {stats.map((stat, index) => (
              <Grid item xs={6} md={3} key={index}>
                <Box sx={{ textAlign: 'center' }} data-aos="fade-up" data-aos-delay={index * 100}>
                  <Typography variant="h3" component="div" color="primary.main" fontWeight="bold">
                    {stat.number}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    {stat.label}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Features Section */}
      <Box sx={{ py: 8 }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography variant="h2" component="h2" gutterBottom data-aos="fade-up">
              Vipengele Vikuu
            </Typography>
            <Typography variant="h6" color="text.secondary" data-aos="fade-up" data-aos-delay="200">
              Kila kitu unachohitaji katika programu moja
            </Typography>
          </Box>
          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Card
                  sx={{
                    height: '100%',
                    textAlign: 'center',
                    transition: 'transform 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                    },
                  }}
                  data-aos="fade-up"
                  data-aos-delay={index * 100}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Avatar
                      sx={{
                        width: 60,
                        height: 60,
                        mx: 'auto',
                        mb: 2,
                        backgroundColor: 'primary.main',
                      }}
                    >
                      {feature.icon}
                    </Avatar>
                    <Typography variant="h5" component="h3" gutterBottom fontWeight="bold">
                      {feature.title}
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      {feature.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* CTA Section */}
      <Box
        sx={{
          py: 8,
          background: 'linear-gradient(135deg, #4CAF50 0%, #7BC142 100%)',
          color: 'white',
        }}
      >
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="h3" component="h2" gutterBottom data-aos="fade-up">
              Anza Safari Yako Leo
            </Typography>
            <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }} data-aos="fade-up" data-aos-delay="200">
              Jiunge na maelfu ya watumiaji wanaofurahia huduma za ProChat
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }} data-aos="fade-up" data-aos-delay="400">
              <Chip label="Bure kabisa" sx={{ backgroundColor: 'white', color: 'success.main' }} />
              <Chip label="Usalama wa hali ya juu" sx={{ backgroundColor: 'white', color: 'success.main' }} />
              <Chip label="Msaada wa 24/7" sx={{ backgroundColor: 'white', color: 'success.main' }} />
            </Box>
            <Box sx={{ mt: 4 }}>
              <Button
                variant="contained"
                size="large"
                startIcon={<GetApp />}
                sx={{
                  backgroundColor: 'white',
                  color: 'success.main',
                  px: 4,
                  py: 2,
                  '&:hover': {
                    backgroundColor: 'grey.100',
                  },
                }}
              >
                Pakua ProChat Sasa
              </Button>
            </Box>
          </Box>
        </Container>
      </Box>
    </Box>
  );
}
