// ProChat Transaction Model
// Database model for payment transactions

const mongoose = require('mongoose');

const transactionSchema = new mongoose.Schema({
  // Transaction identification
  id: {
    type: String,
    required: true,
    unique: true,
    index: true,
  },
  
  providerTransactionId: {
    type: String,
    index: true,
  },
  
  // User information
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  
  // Transaction details
  type: {
    type: String,
    enum: ['payment', 'withdrawal', 'refund', 'transfer'],
    required: true,
    default: 'payment',
  },
  
  amount: {
    type: Number,
    required: true,
    min: 0,
  },
  
  currency: {
    type: String,
    required: true,
    enum: ['TZS', 'USD', 'EUR', 'KES', 'UGX', 'RWF', 'BTC', 'USDT'],
    default: 'TZS',
  },
  
  // Payment provider information
  provider: {
    type: String,
    required: true,
    enum: [
      // Mobile Money
      'mpesa', 'airtel_money', 'tigo_pesa', 'halopesa', 'azampesa', 'nara',
      // Banks
      'crdb_bank', 'nmb_bank', 'nbc_bank',
      // Cards
      'visa', 'mastercard', 'stripe',
      // Crypto
      'bitcoin', 'usdt'
    ],
  },
  
  paymentMethod: {
    type: String,
    required: true,
  },
  
  // Transaction status
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'],
    required: true,
    default: 'pending',
    index: true,
  },
  
  // Customer information
  customerPhone: {
    type: String,
    validate: {
      validator: function(v) {
        // Only validate if this is a mobile money transaction
        if (['mpesa', 'airtel_money', 'tigo_pesa', 'halopesa', 'azampesa', 'nara'].includes(this.provider)) {
          return /^(\+255|0)[67]\d{8}$/.test(v);
        }
        return true;
      },
      message: 'Invalid Tanzanian phone number'
    }
  },
  
  customerEmail: {
    type: String,
    validate: {
      validator: function(v) {
        if (v) {
          return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
        }
        return true;
      },
      message: 'Invalid email address'
    }
  },
  
  customerAccount: {
    type: String,
    validate: {
      validator: function(v) {
        // Only validate if this is a bank transaction
        if (['crdb_bank', 'nmb_bank', 'nbc_bank'].includes(this.provider)) {
          return v && v.length >= 10 && v.length <= 20;
        }
        return true;
      },
      message: 'Invalid bank account number'
    }
  },
  
  // Card information (encrypted)
  cardData: {
    lastFourDigits: String,
    cardType: String,
    expiryMonth: Number,
    expiryYear: Number,
    holderName: String,
  },
  
  // Crypto information
  walletAddress: {
    type: String,
    validate: {
      validator: function(v) {
        if (['bitcoin', 'usdt'].includes(this.provider)) {
          return v && v.length > 20;
        }
        return true;
      },
      message: 'Invalid wallet address'
    }
  },
  
  // Transaction description
  description: {
    type: String,
    maxlength: 500,
  },
  
  // Fee information
  fees: {
    percentage: {
      type: Number,
      default: 0,
    },
    fixed: {
      type: Number,
      default: 0,
    },
    total: {
      type: Number,
      default: 0,
    },
  },
  
  // Provider response
  providerResponse: {
    type: mongoose.Schema.Types.Mixed,
  },
  
  // Error information
  errorCode: String,
  errorMessage: String,
  
  // Webhook information
  webhookReceived: {
    type: Boolean,
    default: false,
  },
  
  webhookData: {
    type: mongoose.Schema.Types.Mixed,
  },
  
  // Refund information
  refundId: String,
  refundAmount: Number,
  refundReason: String,
  refundedAt: Date,
  
  // Metadata
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {},
  },
  
  // IP and device information for security
  ipAddress: String,
  userAgent: String,
  deviceId: String,
  
  // Fraud detection
  fraudScore: {
    type: Number,
    min: 0,
    max: 1,
    default: 0,
  },
  
  fraudFlags: [{
    type: String,
    enum: [
      'rapid_succession',
      'round_numbers',
      'unusual_hours',
      'new_device',
      'high_amount',
      'suspicious_pattern'
    ]
  }],
  
  // Compliance
  kycVerified: {
    type: Boolean,
    default: false,
  },
  
  amlChecked: {
    type: Boolean,
    default: false,
  },
  
  sanctionScreened: {
    type: Boolean,
    default: false,
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now,
    index: true,
  },
  
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  
  completedAt: Date,
  failedAt: Date,
  
}, {
  timestamps: true,
  collection: 'transactions',
});

// Indexes for better query performance
transactionSchema.index({ userId: 1, createdAt: -1 });
transactionSchema.index({ provider: 1, status: 1 });
transactionSchema.index({ status: 1, createdAt: -1 });
transactionSchema.index({ providerTransactionId: 1 });
transactionSchema.index({ customerPhone: 1 });
transactionSchema.index({ amount: 1, currency: 1 });
transactionSchema.index({ 'metadata.orderId': 1 });

// Virtual for total amount including fees
transactionSchema.virtual('totalAmount').get(function() {
  return this.amount + (this.fees?.total || 0);
});

// Virtual for formatted amount
transactionSchema.virtual('formattedAmount').get(function() {
  return `${this.currency} ${this.amount.toLocaleString()}`;
});

// Virtual for provider display name
transactionSchema.virtual('providerDisplayName').get(function() {
  const providerNames = {
    mpesa: 'M-Pesa',
    airtel_money: 'Airtel Money',
    tigo_pesa: 'Tigo Pesa',
    halopesa: 'HaloPesa',
    azampesa: 'AzamPesa',
    nara: 'NARA',
    crdb_bank: 'CRDB Bank',
    nmb_bank: 'NMB Bank',
    nbc_bank: 'NBC Bank',
    visa: 'Visa',
    mastercard: 'Mastercard',
    stripe: 'International Cards',
    bitcoin: 'Bitcoin',
    usdt: 'USDT',
  };
  return providerNames[this.provider] || this.provider;
});

// Pre-save middleware
transactionSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Update completion/failure timestamps
  if (this.isModified('status')) {
    if (this.status === 'completed') {
      this.completedAt = new Date();
    } else if (this.status === 'failed') {
      this.failedAt = new Date();
    }
  }
  
  // Calculate total fees
  if (this.fees && (this.fees.percentage || this.fees.fixed)) {
    this.fees.total = (this.fees.percentage || 0) + (this.fees.fixed || 0);
  }
  
  next();
});

// Static methods
transactionSchema.statics.findByUserId = function(userId, options = {}) {
  const query = this.find({ userId });
  
  if (options.status) {
    query.where('status').equals(options.status);
  }
  
  if (options.provider) {
    query.where('provider').equals(options.provider);
  }
  
  if (options.startDate || options.endDate) {
    const dateQuery = {};
    if (options.startDate) dateQuery.$gte = new Date(options.startDate);
    if (options.endDate) dateQuery.$lte = new Date(options.endDate);
    query.where('createdAt', dateQuery);
  }
  
  return query.sort({ createdAt: -1 });
};

transactionSchema.statics.getStatsByProvider = function(provider, timeRange = '30d') {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - parseInt(timeRange));
  
  return this.aggregate([
    {
      $match: {
        provider,
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        avgAmount: { $avg: '$amount' }
      }
    }
  ]);
};

transactionSchema.statics.getRevenueStats = function(timeRange = '30d') {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - parseInt(timeRange));
  
  return this.aggregate([
    {
      $match: {
        status: 'completed',
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          provider: '$provider',
          currency: '$currency'
        },
        totalTransactions: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        totalFees: { $sum: '$fees.total' },
        avgAmount: { $avg: '$amount' }
      }
    },
    {
      $sort: { totalAmount: -1 }
    }
  ]);
};

// Instance methods
transactionSchema.methods.updateStatus = function(newStatus, providerResponse = null) {
  this.status = newStatus;
  if (providerResponse) {
    this.providerResponse = providerResponse;
  }
  return this.save();
};

transactionSchema.methods.addFraudFlag = function(flag) {
  if (!this.fraudFlags.includes(flag)) {
    this.fraudFlags.push(flag);
  }
  return this.save();
};

transactionSchema.methods.markAsRefunded = function(refundId, refundAmount, reason) {
  this.status = 'refunded';
  this.refundId = refundId;
  this.refundAmount = refundAmount;
  this.refundReason = reason;
  this.refundedAt = new Date();
  return this.save();
};

// Export model
module.exports = mongoose.model('Transaction', transactionSchema);
