{"version": 3, "file": "bulk-suppressions-file.js", "sourceRoot": "", "sources": ["../../src/eslint-bulk-suppressions/bulk-suppressions-file.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;AAsC3D,gGAoDC;AAED,8GAOC;AAED,kEAYC;AAED,gGAGC;AAUD,oDAEC;AAhID,4CAAoB;AACpB,2CAAsD;AAmBtD,MAAM,oBAAoB,GAAY,OAAO,CAAC,GAAG,CAAC,mCAAuB,CAAC,KAAK,SAAS,CAAC;AACzF,MAAM,cAAc,GAAW,EAAE,GAAG,IAAI,CAAC;AACzC,MAAM,0BAA0B,GAAW,gCAAgC,CAAC;AAE5E,SAAS,qCAAqC,CAAC,CAAwB;IACrE,IAAI,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,IAAI,MAAK,QAAQ,EAAE,CAAC;QACzB,wDAAwD;QACxD,MAAM,CAAC,CAAC;IACV,CAAC;AACH,CAAC;AAMD,MAAM,4BAA4B,GAA+C,IAAI,GAAG,EAAE,CAAC;AAC3F,SAAgB,0CAA0C,CACxD,kBAA0B;IAE1B,MAAM,wBAAwB,GAC5B,4BAA4B,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAEvD,IAAI,UAAmB,CAAC;IACxB,IAAI,kBAA2C,CAAC;IAChD,IAAI,wBAAwB,EAAE,CAAC;QAC7B,UAAU,GAAG,oBAAoB,IAAI,wBAAwB,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,CAAC;QACrG,kBAAkB,GAAG,wBAAwB,CAAC,kBAAkB,CAAC;IACnE,CAAC;SAAM,CAAC;QACN,UAAU,GAAG,IAAI,CAAC;IACpB,CAAC;IAED,IAAI,UAAU,EAAE,CAAC;QACf,MAAM,gBAAgB,GAAW,GAAG,kBAAkB,IAAI,0BAA0B,EAAE,CAAC;QACvF,IAAI,WAA+B,CAAC;QACpC,IAAI,CAAC;YACH,WAAW,GAAG,YAAE,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC7D,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,qCAAqC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,kBAAkB,GAAG;gBACnB,sBAAsB,EAAE,IAAI,GAAG,EAAE;gBACjC,UAAU,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE;gBAChC,yBAAyB,EAAE,IAAI,GAAG,EAAE;gBACpC,aAAa,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE;aACpC,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,UAAU,GAA0B,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAClE,wBAAwB,CAAC,UAAU,CAAC,CAAC;YAErC,MAAM,sBAAsB,GAAgB,IAAI,GAAG,EAAE,CAAC;YACtD,KAAK,MAAM,WAAW,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;gBAClD,sBAAsB,CAAC,GAAG,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC,CAAC;YAChE,CAAC;YAED,kBAAkB,GAAG;gBACnB,sBAAsB;gBACtB,UAAU;gBACV,yBAAyB,EAAE,IAAI,GAAG,EAAE;gBACpC,aAAa,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE;aACpC,CAAC;QACJ,CAAC;QAED,4BAA4B,CAAC,GAAG,CAAC,kBAAkB,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,kBAAkB,EAAE,CAAC,CAAC;IACrG,CAAC;IAED,OAAO,kBAAmB,CAAC;AAC7B,CAAC;AAED,SAAgB,iDAAiD;IAC/D,MAAM,MAAM,GAAwC,EAAE,CAAC;IACvD,KAAK,MAAM,CAAC,kBAAkB,EAAE,EAAE,kBAAkB,EAAE,CAAC,IAAI,4BAA4B,EAAE,CAAC;QACxF,MAAM,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC,CAAC;IACxD,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAgB,2BAA2B,CACzC,kBAA0B,EAC1B,kBAA2C;IAE3C,4BAA4B,CAAC,GAAG,CAAC,kBAAkB,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,kBAAkB,EAAE,CAAC,CAAC;IACnG,MAAM,gBAAgB,GAAW,GAAG,kBAAkB,IAAI,0BAA0B,EAAE,CAAC;IACvF,IAAI,kBAAkB,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC5D,UAAU,CAAC,gBAAgB,CAAC,CAAC;IAC/B,CAAC;SAAM,CAAC;QACN,kBAAkB,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACrE,YAAE,CAAC,aAAa,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,UAAU,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;IAClG,CAAC;AACH,CAAC;AAED,SAAgB,0CAA0C,CAAC,kBAA0B;IACnF,MAAM,gBAAgB,GAAW,GAAG,kBAAkB,IAAI,0BAA0B,EAAE,CAAC;IACvF,UAAU,CAAC,gBAAgB,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,UAAU,CAAC,QAAgB;IAClC,IAAI,CAAC;QACH,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,qCAAqC,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC;AACH,CAAC;AAED,SAAgB,oBAAoB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAgB;IACxE,OAAO,GAAG,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;AACtC,CAAC;AAED,SAAS,mBAAmB,CAAC,CAAe,EAAE,CAAe;IAC3D,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QACpB,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC;SAAM,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QAC3B,OAAO,CAAC,CAAC;IACX,CAAC;SAAM,IAAI,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;QACjC,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC;SAAM,IAAI,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;QACjC,OAAO,CAAC,CAAC;IACX,CAAC;SAAM,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QAC3B,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC;SAAM,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QAC3B,OAAO,CAAC,CAAC;IACX,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,CAAC;IACX,CAAC;AACH,CAAC;AAED,SAAS,wBAAwB,CAAC,IAA2B;IAC3D,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC7B,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3E,CAAC;IAED,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC1C,CAAC;IAED,MAAM,4BAA4B,GAAqC,IAAI,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC;IAEjG,KAAK,MAAM,YAAY,IAAI,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5D,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,YAA2C,CAAC,EAAE,CAAC;YACnF,MAAM,IAAI,KAAK,CAAC,6BAA6B,YAAY,EAAE,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC;IAC9B,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACtD,CAAC;IAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;QACjC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;IAC9D,CAAC;IAED,MAAM,mCAAmC,GAA4B,IAAI,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;IAC1G,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;QACvC,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClF,CAAC;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClF,CAAC;QAED,KAAK,MAAM,YAAY,IAAI,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC,mCAAmC,CAAC,GAAG,CAAC,YAAkC,CAAC,EAAE,CAAC;gBACjF,MAAM,IAAI,KAAK,CAAC,6BAA6B,YAAY,EAAE,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,KAAK,MAAM,YAAY,IAAI,mCAAmC,EAAE,CAAC;YAC/D,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC9C,MAAM,IAAI,KAAK,CACb,YAAY,YAAY,8BAA8B,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAC7F,CAAC;YACJ,CAAC;iBAAM,IAAI,OAAO,WAAW,CAAC,YAAY,CAAC,KAAK,QAAQ,EAAE,CAAC;gBACzD,MAAM,IAAI,KAAK,CACb,IAAI,YAAY,8CAA8C,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CACrG,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\nimport fs from 'fs';\nimport { VSCODE_PID_ENV_VAR_NAME } from './constants';\n\nexport interface ISuppression {\n  file: string;\n  scopeId: string;\n  rule: string;\n}\n\nexport interface IBulkSuppressionsConfig {\n  serializedSuppressions: Set<string>;\n  jsonObject: IBulkSuppressionsJson;\n  newSerializedSuppressions: Set<string>;\n  newJsonObject: IBulkSuppressionsJson;\n}\n\nexport interface IBulkSuppressionsJson {\n  suppressions: ISuppression[];\n}\n\nconst IS_RUNNING_IN_VSCODE: boolean = process.env[VSCODE_PID_ENV_VAR_NAME] !== undefined;\nconst TEN_SECONDS_MS: number = 10 * 1000;\nconst SUPPRESSIONS_JSON_FILENAME: string = '.eslint-bulk-suppressions.json';\n\nfunction throwIfAnythingOtherThanNotExistError(e: NodeJS.ErrnoException): void | never {\n  if (e?.code !== 'ENOENT') {\n    // Throw an error if any other error than file not found\n    throw e;\n  }\n}\n\ninterface ICachedBulkSuppressionsConfig {\n  readTime: number;\n  suppressionsConfig: IBulkSuppressionsConfig;\n}\nconst suppressionsJsonByFolderPath: Map<string, ICachedBulkSuppressionsConfig> = new Map();\nexport function getSuppressionsConfigForEslintrcFolderPath(\n  eslintrcFolderPath: string\n): IBulkSuppressionsConfig {\n  const cachedSuppressionsConfig: ICachedBulkSuppressionsConfig | undefined =\n    suppressionsJsonByFolderPath.get(eslintrcFolderPath);\n\n  let shouldLoad: boolean;\n  let suppressionsConfig: IBulkSuppressionsConfig;\n  if (cachedSuppressionsConfig) {\n    shouldLoad = IS_RUNNING_IN_VSCODE && cachedSuppressionsConfig.readTime < Date.now() - TEN_SECONDS_MS;\n    suppressionsConfig = cachedSuppressionsConfig.suppressionsConfig;\n  } else {\n    shouldLoad = true;\n  }\n\n  if (shouldLoad) {\n    const suppressionsPath: string = `${eslintrcFolderPath}/${SUPPRESSIONS_JSON_FILENAME}`;\n    let rawJsonFile: string | undefined;\n    try {\n      rawJsonFile = fs.readFileSync(suppressionsPath).toString();\n    } catch (e) {\n      throwIfAnythingOtherThanNotExistError(e);\n    }\n\n    if (!rawJsonFile) {\n      suppressionsConfig = {\n        serializedSuppressions: new Set(),\n        jsonObject: { suppressions: [] },\n        newSerializedSuppressions: new Set(),\n        newJsonObject: { suppressions: [] }\n      };\n    } else {\n      const jsonObject: IBulkSuppressionsJson = JSON.parse(rawJsonFile);\n      validateSuppressionsJson(jsonObject);\n\n      const serializedSuppressions: Set<string> = new Set();\n      for (const suppression of jsonObject.suppressions) {\n        serializedSuppressions.add(serializeSuppression(suppression));\n      }\n\n      suppressionsConfig = {\n        serializedSuppressions,\n        jsonObject,\n        newSerializedSuppressions: new Set(),\n        newJsonObject: { suppressions: [] }\n      };\n    }\n\n    suppressionsJsonByFolderPath.set(eslintrcFolderPath, { readTime: Date.now(), suppressionsConfig });\n  }\n\n  return suppressionsConfig!;\n}\n\nexport function getAllBulkSuppressionsConfigsByEslintrcFolderPath(): [string, IBulkSuppressionsConfig][] {\n  const result: [string, IBulkSuppressionsConfig][] = [];\n  for (const [eslintrcFolderPath, { suppressionsConfig }] of suppressionsJsonByFolderPath) {\n    result.push([eslintrcFolderPath, suppressionsConfig]);\n  }\n\n  return result;\n}\n\nexport function writeSuppressionsJsonToFile(\n  eslintrcFolderPath: string,\n  suppressionsConfig: IBulkSuppressionsConfig\n): void {\n  suppressionsJsonByFolderPath.set(eslintrcFolderPath, { readTime: Date.now(), suppressionsConfig });\n  const suppressionsPath: string = `${eslintrcFolderPath}/${SUPPRESSIONS_JSON_FILENAME}`;\n  if (suppressionsConfig.jsonObject.suppressions.length === 0) {\n    deleteFile(suppressionsPath);\n  } else {\n    suppressionsConfig.jsonObject.suppressions.sort(compareSuppressions);\n    fs.writeFileSync(suppressionsPath, JSON.stringify(suppressionsConfig.jsonObject, undefined, 2));\n  }\n}\n\nexport function deleteBulkSuppressionsFileInEslintrcFolder(eslintrcFolderPath: string): void {\n  const suppressionsPath: string = `${eslintrcFolderPath}/${SUPPRESSIONS_JSON_FILENAME}`;\n  deleteFile(suppressionsPath);\n}\n\nfunction deleteFile(filePath: string): void {\n  try {\n    fs.unlinkSync(filePath);\n  } catch (e) {\n    throwIfAnythingOtherThanNotExistError(e);\n  }\n}\n\nexport function serializeSuppression({ file, scopeId, rule }: ISuppression): string {\n  return `${file}|${scopeId}|${rule}`;\n}\n\nfunction compareSuppressions(a: ISuppression, b: ISuppression): -1 | 0 | 1 {\n  if (a.file < b.file) {\n    return -1;\n  } else if (a.file > b.file) {\n    return 1;\n  } else if (a.scopeId < b.scopeId) {\n    return -1;\n  } else if (a.scopeId > b.scopeId) {\n    return 1;\n  } else if (a.rule < b.rule) {\n    return -1;\n  } else if (a.rule > b.rule) {\n    return 1;\n  } else {\n    return 0;\n  }\n}\n\nfunction validateSuppressionsJson(json: IBulkSuppressionsJson): json is IBulkSuppressionsJson {\n  if (typeof json !== 'object') {\n    throw new Error(`Invalid JSON object: ${JSON.stringify(json, null, 2)}`);\n  }\n\n  if (!json) {\n    throw new Error('JSON object is null.');\n  }\n\n  const EXPECTED_ROOT_PROPERTY_NAMES: Set<keyof IBulkSuppressionsJson> = new Set(['suppressions']);\n\n  for (const propertyName of Object.getOwnPropertyNames(json)) {\n    if (!EXPECTED_ROOT_PROPERTY_NAMES.has(propertyName as keyof IBulkSuppressionsJson)) {\n      throw new Error(`Unexpected property name: ${propertyName}`);\n    }\n  }\n\n  const { suppressions } = json;\n  if (!suppressions) {\n    throw new Error('Missing \"suppressions\" property.');\n  }\n\n  if (!Array.isArray(suppressions)) {\n    throw new Error('\"suppressions\" property is not an array.');\n  }\n\n  const EXPECTED_SUPPRESSION_PROPERTY_NAMES: Set<keyof ISuppression> = new Set(['file', 'scopeId', 'rule']);\n  for (const suppression of suppressions) {\n    if (typeof suppression !== 'object') {\n      throw new Error(`Invalid suppression: ${JSON.stringify(suppression, null, 2)}`);\n    }\n\n    if (!suppression) {\n      throw new Error(`Suppression is null: ${JSON.stringify(suppression, null, 2)}`);\n    }\n\n    for (const propertyName of Object.getOwnPropertyNames(suppression)) {\n      if (!EXPECTED_SUPPRESSION_PROPERTY_NAMES.has(propertyName as keyof ISuppression)) {\n        throw new Error(`Unexpected property name: ${propertyName}`);\n      }\n    }\n\n    for (const propertyName of EXPECTED_SUPPRESSION_PROPERTY_NAMES) {\n      if (!suppression.hasOwnProperty(propertyName)) {\n        throw new Error(\n          `Missing \"${propertyName}\" property in suppression: ${JSON.stringify(suppression, null, 2)}`\n        );\n      } else if (typeof suppression[propertyName] !== 'string') {\n        throw new Error(\n          `\"${propertyName}\" property in suppression is not a string: ${JSON.stringify(suppression, null, 2)}`\n        );\n      }\n    }\n  }\n\n  return true;\n}\n"]}