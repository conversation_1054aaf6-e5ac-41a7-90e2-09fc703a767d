// ProChat Advanced User Engagement & Monetization System
// Make users ADDICTED to the app with real rewards!

import { SecurityLogger } from '../config/security';
import offlineStorage from './offlineStorage';
import walletSecurityService from './walletSecurity';
import adminFinancialControlService from './adminFinancialControl';
import smartNotificationService from './smartNotifications';

class EngagementRewardsService {
  constructor() {
    this.userEngagement = new Map();
    this.rewardRules = new Map();
    this.dailyMissions = new Map();
    this.streakBonuses = new Map();
    this.levelSystem = new Map();
    this.coinSystem = new Map();
    this.marketplaceEarnings = new Map();
    
    this.initializeRewardSystem();
    this.startEngagementTracking();
  }

  initializeRewardSystem() {
    // Initialize reward rules
    this.setupRewardRules();
    
    // Initialize daily missions
    this.setupDailyMissions();
    
    // Initialize streak bonuses
    this.setupStreakBonuses();
    
    // Initialize level system
    this.setupLevelSystem();
    
    // Initialize coin system
    this.setupCoinSystem();
    
    // Initialize marketplace
    this.setupMarketplace();
  }

  setupRewardRules() {
    // 💰 POST REWARDS - Watu wanapata hela kwa posts!
    this.rewardRules.set('post_rewards', {
      name: 'Post Yalipwe System',
      rules: {
        'post_created': { coins: 5, cash: 100 }, // TZS 100 kwa post
        'post_liked': { coins: 1, cash: 10 }, // TZS 10 kwa like
        'post_shared': { coins: 3, cash: 50 }, // TZS 50 kwa share
        'post_commented': { coins: 2, cash: 25 }, // TZS 25 kwa comment
        'post_viral': { coins: 100, cash: 5000 }, // TZS 5,000 kwa viral post (100+ likes)
      },
      description: 'Pata hela kwa kila post, like, share na comment!',
    });

    // 🎁 ACTIVITY REWARDS - Auto rewards kwa activities
    this.rewardRules.set('activity_rewards', {
      name: 'Auto-Reward kwa Activity',
      rules: {
        'profile_completed': { coins: 50, cash: 500 }, // TZS 500 kwa profile kamili
        'first_post': { coins: 20, cash: 200 }, // TZS 200 kwa post ya kwanza
        'invite_friend': { coins: 100, cash: 1000 }, // TZS 1,000 kwa invite
        'friend_joins': { coins: 200, cash: 2000 }, // TZS 2,000 wakati rafiki anajiunga
        'daily_login': { coins: 10, cash: 50 }, // TZS 50 kwa login ya kila siku
        'weekly_active': { coins: 100, cash: 1000 }, // TZS 1,000 kwa kuwa active wiki nzima
      },
      description: 'Pata zawadi kiotomatiki kwa shughuli za kawaida!',
    });

    // 🛒 MARKETPLACE REWARDS - Pesa kwa biashara
    this.rewardRules.set('marketplace_rewards', {
      name: 'Marketplace Earnings',
      rules: {
        'product_listed': { coins: 10, cash: 100 }, // TZS 100 kwa kulist bidhaa
        'product_sold': { coins: 50, percentage: 5 }, // 5% ya bei ya bidhaa
        'product_reviewed': { coins: 5, cash: 50 }, // TZS 50 kwa review
        'store_featured': { coins: 500, cash: 5000 }, // TZS 5,000 kwa featured store
      },
      description: 'Pata hela kwa kuuza bidhaa kwenye marketplace!',
    });

    // 🎯 ENGAGEMENT REWARDS - Rewards kwa engagement
    this.rewardRules.set('engagement_rewards', {
      name: 'Engagement Bonuses',
      rules: {
        'video_watched': { coins: 2, cash: 20 }, // TZS 20 kwa video
        'news_read': { coins: 1, cash: 10 }, // TZS 10 kwa news
        'event_attended': { coins: 50, cash: 500 }, // TZS 500 kwa event
        'job_applied': { coins: 20, cash: 200 }, // TZS 200 kwa job application
        'donation_made': { coins: 100, cash: 0 }, // Coins tu kwa donation
      },
      description: 'Pata zawadi kwa kushiriki katika shughuli mbalimbali!',
    });
  }

  setupDailyMissions() {
    // 📅 DAILY MISSIONS - Kazi za kila siku
    this.dailyMissions.set('basic_missions', [
      {
        id: 'daily_post',
        name: 'Tuma Post Leo',
        description: 'Tuma post moja leo upate 50 coins',
        requirement: { type: 'post_created', count: 1 },
        reward: { coins: 50, cash: 200 },
        difficulty: 'easy',
      },
      {
        id: 'daily_likes',
        name: 'Penda Posts 10',
        description: 'Penda posts 10 za watu upate 30 coins',
        requirement: { type: 'post_liked', count: 10 },
        reward: { coins: 30, cash: 100 },
        difficulty: 'easy',
      },
      {
        id: 'daily_comments',
        name: 'Andika Comments 5',
        description: 'Andika comments 5 upate 40 coins',
        requirement: { type: 'post_commented', count: 5 },
        reward: { coins: 40, cash: 150 },
        difficulty: 'medium',
      },
      {
        id: 'daily_invite',
        name: 'Alika Rafiki 1',
        description: 'Alika rafiki mmoja upate 100 coins',
        requirement: { type: 'invite_friend', count: 1 },
        reward: { coins: 100, cash: 500 },
        difficulty: 'medium',
      },
      {
        id: 'daily_marketplace',
        name: 'Tembelea Marketplace',
        description: 'Tembelea marketplace na uangalie bidhaa 5',
        requirement: { type: 'marketplace_visit', count: 5 },
        reward: { coins: 25, cash: 100 },
        difficulty: 'easy',
      },
    ]);

    this.dailyMissions.set('premium_missions', [
      {
        id: 'viral_post',
        name: 'Tengeneza Post Viral',
        description: 'Post yako ipate likes 50+ upate 500 coins',
        requirement: { type: 'post_viral', count: 1 },
        reward: { coins: 500, cash: 2000 },
        difficulty: 'hard',
      },
      {
        id: 'marketplace_sale',
        name: 'Uza Bidhaa Marketplace',
        description: 'Uza bidhaa moja marketplace upate 200 coins',
        requirement: { type: 'product_sold', count: 1 },
        reward: { coins: 200, cash: 1000 },
        difficulty: 'hard',
      },
    ]);
  }

  setupStreakBonuses() {
    // 🔁 STREAK BONUSES - Bonuses za mfululizo
    this.streakBonuses.set('login_streak', {
      name: 'Login Streak Bonus',
      description: 'Bonus kwa kuingia kila siku mfululizo',
      bonuses: {
        3: { coins: 50, cash: 200, message: 'Siku 3 mfululizo! 🔥' },
        7: { coins: 200, cash: 1000, message: 'Wiki nzima! 🎉' },
        14: { coins: 500, cash: 2500, message: 'Wiki 2 mfululizo! 💪' },
        30: { coins: 1500, cash: 10000, message: 'Mwezi mzima! 👑' },
        100: { coins: 5000, cash: 50000, message: 'Siku 100! Wewe ni LEGEND! 🏆' },
      },
    });

    this.streakBonuses.set('post_streak', {
      name: 'Post Streak Bonus',
      description: 'Bonus kwa kutuma post kila siku',
      bonuses: {
        7: { coins: 100, cash: 500, message: 'Post kila siku wiki nzima! 📝' },
        30: { coins: 1000, cash: 5000, message: 'Post kila siku mwezi mzima! 🚀' },
      },
    });
  }

  setupLevelSystem() {
    // 🎖 LEVEL SYSTEM - Levels na badges
    this.levelSystem.set('user_levels', {
      1: { name: 'Mwanzo', xp: 0, badge: '🌱', perks: [] },
      2: { name: 'Mwanafunzi', xp: 100, badge: '📚', perks: ['daily_bonus_x1.2'] },
      3: { name: 'Mshiriki', xp: 300, badge: '👥', perks: ['daily_bonus_x1.5', 'marketplace_discount_5%'] },
      4: { name: 'Mbunifu', xp: 600, badge: '🎨', perks: ['post_boost_free', 'daily_bonus_x2'] },
      5: { name: 'Mtaalamu', xp: 1000, badge: '⭐', perks: ['premium_features', 'daily_bonus_x2.5'] },
      6: { name: 'Mwongozi', xp: 1500, badge: '👑', perks: ['all_features', 'daily_bonus_x3'] },
      7: { name: 'Mfalme', xp: 2500, badge: '🏆', perks: ['vip_status', 'daily_bonus_x5'] },
    });

    this.levelSystem.set('creator_levels', {
      1: { name: 'Creator Mpya', posts: 10, badge: '📝', earnings_bonus: 1.0 },
      2: { name: 'Content Creator', posts: 50, badge: '🎬', earnings_bonus: 1.2 },
      3: { name: 'Influencer', posts: 100, badge: '📢', earnings_bonus: 1.5 },
      4: { name: 'Super Creator', posts: 500, badge: '🌟', earnings_bonus: 2.0 },
      5: { name: 'Viral King/Queen', posts: 1000, badge: '👑', earnings_bonus: 3.0 },
    });
  }

  setupCoinSystem() {
    // 🪙 COIN SYSTEM - In-app currency
    this.coinSystem.set('exchange_rates', {
      coins_to_cash: 10, // 10 coins = TZS 1
      cash_to_coins: 0.1, // TZS 1 = 0.1 coins
      minimum_cashout: 1000, // Minimum TZS 1,000 kwa cashout
    });

    this.coinSystem.set('coin_uses', {
      'post_boost': { cost: 50, description: 'Boost post yako kwa masaa 24' },
      'profile_highlight': { cost: 30, description: 'Highlight profile yako' },
      'premium_features': { cost: 100, description: 'Premium features kwa siku 1' },
      'marketplace_featured': { cost: 200, description: 'Feature bidhaa yako' },
      'gift_coins': { cost: 1, description: 'Tuma coins kwa rafiki' },
    });
  }

  setupMarketplace() {
    // 🛒 MARKETPLACE INTEGRATION
    this.marketplaceEarnings.set('commission_rates', {
      'electronics': 5, // 5% commission
      'fashion': 8, // 8% commission
      'food': 10, // 10% commission
      'services': 15, // 15% commission
      'digital': 20, // 20% commission
    });
  }

  startEngagementTracking() {
    // Start tracking user engagement
    setInterval(() => {
      this.updateEngagementMetrics();
      this.processDailyMissions();
      this.checkStreakBonuses();
      this.updateLevels();
    }, 60000); // Every minute

    // Daily reset
    setInterval(() => {
      this.resetDailyMissions();
      this.processStreakBonuses();
    }, 24 * 60 * 60 * 1000); // Every 24 hours

    console.log('🎯 Engagement & Rewards System: Tracking started');
  }

  // Track user actions and award rewards
  async trackUserAction(userId, actionType, metadata = {}) {
    try {
      // Get user engagement data
      let userEngagement = this.userEngagement.get(userId) || this.createNewUserEngagement(userId);
      
      // Update action count
      userEngagement.actions[actionType] = (userEngagement.actions[actionType] || 0) + 1;
      userEngagement.lastActivity = Date.now();
      userEngagement.totalActions++;

      // Calculate rewards
      const rewards = await this.calculateRewards(userId, actionType, metadata);
      
      if (rewards.coins > 0 || rewards.cash > 0) {
        // Award rewards
        await this.awardRewards(userId, rewards, actionType);
        
        // Update user engagement
        userEngagement.totalCoins += rewards.coins;
        userEngagement.totalCash += rewards.cash;
        userEngagement.totalRewards++;
      }

      // Update XP
      const xpGained = this.calculateXP(actionType, metadata);
      userEngagement.xp += xpGained;

      // Check for level up
      await this.checkLevelUp(userId, userEngagement);

      // Check daily missions
      await this.checkDailyMissions(userId, actionType, metadata);

      // Update engagement data
      this.userEngagement.set(userId, userEngagement);

      // Log engagement event
      SecurityLogger.logSecurityEvent('USER_ENGAGEMENT', {
        userId,
        actionType,
        rewards,
        xpGained,
        totalXP: userEngagement.xp,
        level: userEngagement.level,
      });

      return {
        success: true,
        rewards,
        xpGained,
        level: userEngagement.level,
        message: this.getRewardMessage(actionType, rewards),
      };

    } catch (error) {
      console.error('Failed to track user action:', error);
      return { success: false, error: error.message };
    }
  }

  createNewUserEngagement(userId) {
    return {
      userId,
      level: 1,
      xp: 0,
      totalCoins: 0,
      totalCash: 0,
      totalActions: 0,
      totalRewards: 0,
      actions: {},
      dailyMissions: {},
      streaks: {
        login: 0,
        post: 0,
        lastLogin: null,
        lastPost: null,
      },
      badges: [],
      createdAt: Date.now(),
      lastActivity: Date.now(),
    };
  }

  async calculateRewards(userId, actionType, metadata) {
    // 💰 USE ADMIN-CONTROLLED REWARD RATES
    const result = await adminFinancialControlService.processRewardTransaction(
      userId,
      actionType,
      metadata
    );

    if (!result.success) {
      return { coins: 0, cash: 0, reason: result.reason };
    }

    // Get admin-controlled reward settings
    const rewardSettings = adminFinancialControlService.getRewardSettings();
    let totalCoins = 0;
    let totalCash = 0;

    // Check all admin-controlled reward rules
    for (const [category, rules] of Object.entries(rewardSettings)) {
      if (rules[actionType] && rules[actionType].enabled) {
        const reward = rules[actionType];

        // Base rewards
        totalCoins += reward.coins || 0;
        totalCash += reward.cash || 0;

        // Percentage-based rewards (for marketplace)
        if (reward.percentage && metadata.amount) {
          totalCash += (metadata.amount * reward.percentage) / 100;
        }
      }
    }

    // Apply admin-controlled global multiplier
    const globalSettings = adminFinancialControlService.getFinancialPolicies().global_settings;
    if (globalSettings) {
      totalCoins *= globalSettings.reward_multiplier_global || 1.0;
      totalCash *= globalSettings.reward_multiplier_global || 1.0;
    }

    // Apply level multipliers
    const userEngagement = this.userEngagement.get(userId);
    if (userEngagement) {
      const levelData = this.levelSystem.get('user_levels')[userEngagement.level];
      if (levelData && levelData.perks) {
        levelData.perks.forEach(perk => {
          if (perk.startsWith('daily_bonus_x')) {
            const multiplier = parseFloat(perk.replace('daily_bonus_x', ''));
            totalCoins *= multiplier;
            totalCash *= multiplier;
          }
        });
      }
    }

    // Apply creator level bonuses
    const creatorLevel = this.getCreatorLevel(userId);
    if (creatorLevel) {
      totalCash *= creatorLevel.earnings_bonus;
    }

    return {
      coins: Math.round(totalCoins),
      cash: Math.round(totalCash),
    };
  }

  async awardRewards(userId, rewards, actionType) {
    try {
      // Add coins to user account
      if (rewards.coins > 0) {
        await this.addCoinsToUser(userId, rewards.coins);
      }

      // Add cash to wallet
      if (rewards.cash > 0) {
        await this.addCashToWallet(userId, rewards.cash, `Reward: ${actionType}`);
      }

      // Store reward transaction
      await offlineStorage.add('reward_transactions', {
        userId,
        actionType,
        rewards,
        timestamp: Date.now(),
        status: 'completed',
      });

      // 🔔 TRIGGER AUTOMATIC REWARD NOTIFICATION
      if (rewards.cash > 0) {
        await smartNotificationService.triggerMoneyEarnedNotification(
          userId,
          rewards.cash,
          actionType
        );
      }

    } catch (error) {
      console.error('Failed to award rewards:', error);
      throw error;
    }
  }

  async addCoinsToUser(userId, coins) {
    // Get current coins
    const currentCoins = await offlineStorage.get('user_coins', userId) || { coins: 0 };
    
    // Add new coins
    currentCoins.coins += coins;
    currentCoins.lastUpdated = Date.now();
    
    // Save updated coins
    await offlineStorage.add('user_coins', {
      id: userId,
      ...currentCoins,
    });
  }

  async addCashToWallet(userId, amount, description) {
    // Use wallet security service to add money safely
    const transactionData = {
      amount,
      type: 'reward',
      description,
      recipient: userId,
    };

    const authData = {
      method: 'system',
      systemAuth: true,
    };

    return await walletSecurityService.processSecureTransaction(
      'system',
      transactionData,
      authData
    );
  }

  calculateXP(actionType, metadata) {
    const xpRates = {
      'post_created': 10,
      'post_liked': 1,
      'post_shared': 5,
      'post_commented': 3,
      'post_viral': 50,
      'profile_completed': 25,
      'invite_friend': 20,
      'friend_joins': 30,
      'daily_login': 5,
      'product_sold': 15,
      'video_watched': 2,
      'news_read': 1,
      'event_attended': 25,
    };

    return xpRates[actionType] || 1;
  }

  async checkLevelUp(userId, userEngagement) {
    const currentLevel = userEngagement.level;
    const userLevels = this.levelSystem.get('user_levels');
    
    // Find the highest level user qualifies for
    let newLevel = currentLevel;
    for (const [level, data] of Object.entries(userLevels)) {
      if (userEngagement.xp >= data.xp && parseInt(level) > newLevel) {
        newLevel = parseInt(level);
      }
    }

    if (newLevel > currentLevel) {
      userEngagement.level = newLevel;
      const levelData = userLevels[newLevel];
      
      // Award level up bonus
      const levelUpReward = {
        coins: newLevel * 100,
        cash: newLevel * 500,
      };

      await this.awardRewards(userId, levelUpReward, 'level_up');

      // Add badge
      if (!userEngagement.badges.includes(levelData.badge)) {
        userEngagement.badges.push(levelData.badge);
      }

      // Notify user
      this.notifyLevelUp(userId, newLevel, levelData);
    }
  }

  async checkDailyMissions(userId, actionType, metadata) {
    const today = new Date().toDateString();
    let userEngagement = this.userEngagement.get(userId);
    
    if (!userEngagement.dailyMissions[today]) {
      userEngagement.dailyMissions[today] = {};
    }

    const todayMissions = userEngagement.dailyMissions[today];

    // Check all daily missions
    const allMissions = [
      ...this.dailyMissions.get('basic_missions'),
      ...this.dailyMissions.get('premium_missions'),
    ];

    for (const mission of allMissions) {
      if (mission.requirement.type === actionType) {
        if (!todayMissions[mission.id]) {
          todayMissions[mission.id] = { progress: 0, completed: false };
        }

        const missionProgress = todayMissions[mission.id];
        if (!missionProgress.completed) {
          missionProgress.progress++;
          
          if (missionProgress.progress >= mission.requirement.count) {
            missionProgress.completed = true;
            
            // Award mission reward
            await this.awardRewards(userId, mission.reward, `mission_${mission.id}`);
            
            // Notify user
            this.notifyMissionCompleted(userId, mission);
          }
        }
      }
    }
  }

  // Streak management
  updateLoginStreak(userId) {
    const userEngagement = this.userEngagement.get(userId);
    if (!userEngagement) return;

    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString();
    const lastLogin = userEngagement.streaks.lastLogin;

    if (lastLogin === yesterday) {
      // Continue streak
      userEngagement.streaks.login++;
    } else if (lastLogin !== today) {
      // Reset streak if not consecutive
      userEngagement.streaks.login = 1;
    }

    userEngagement.streaks.lastLogin = today;
    this.checkStreakBonus(userId, 'login_streak', userEngagement.streaks.login);
  }

  checkStreakBonus(userId, streakType, streakCount) {
    const streakBonuses = this.streakBonuses.get(streakType);
    if (!streakBonuses) return;

    const bonus = streakBonuses.bonuses[streakCount];
    if (bonus) {
      this.awardRewards(userId, bonus, `streak_${streakType}_${streakCount}`);
      this.notifyStreakBonus(userId, streakCount, bonus);
    }
  }

  // Marketplace integration
  async processMarketplaceSale(userId, saleData) {
    const { productId, amount, category, buyerId } = saleData;
    
    // Calculate commission
    const commissionRates = this.marketplaceEarnings.get('commission_rates');
    const commissionRate = commissionRates[category] || 10;
    const commission = (amount * commissionRate) / 100;
    
    // Award seller
    await this.trackUserAction(userId, 'product_sold', { amount: commission });
    
    // Award buyer (small reward for purchasing)
    await this.trackUserAction(buyerId, 'product_purchased', { amount: amount * 0.01 });
    
    return {
      sellerReward: commission,
      buyerReward: amount * 0.01,
    };
  }

  // Coin exchange
  async exchangeCoinsForCash(userId, coins) {
    const exchangeRates = this.coinSystem.get('exchange_rates');
    const cashAmount = coins / exchangeRates.coins_to_cash;
    
    if (cashAmount < exchangeRates.minimum_cashout) {
      throw new Error(`Minimum cashout is TZS ${exchangeRates.minimum_cashout}`);
    }

    // Deduct coins
    const currentCoins = await offlineStorage.get('user_coins', userId);
    if (!currentCoins || currentCoins.coins < coins) {
      throw new Error('Insufficient coins');
    }

    currentCoins.coins -= coins;
    await offlineStorage.add('user_coins', { id: userId, ...currentCoins });

    // Add cash to wallet
    await this.addCashToWallet(userId, cashAmount, 'Coin exchange');

    return { cashAmount, remainingCoins: currentCoins.coins };
  }

  // Utility methods
  getRewardMessage(actionType, rewards) {
    const messages = {
      'post_created': `🎉 Umepata TZS ${rewards.cash} kwa post yako!`,
      'post_liked': `❤️ Umepata TZS ${rewards.cash} kwa like!`,
      'post_viral': `🔥 POST VIRAL! Umepata TZS ${rewards.cash}!`,
      'invite_friend': `👥 Umepata TZS ${rewards.cash} kwa kumalika rafiki!`,
      'product_sold': `💰 Umepata TZS ${rewards.cash} kwa kuuza bidhaa!`,
    };

    return messages[actionType] || `🎁 Umepata TZS ${rewards.cash} na ${rewards.coins} coins!`;
  }

  getCreatorLevel(userId) {
    // Get user's post count and determine creator level
    const userEngagement = this.userEngagement.get(userId);
    if (!userEngagement) return null;

    const postCount = userEngagement.actions.post_created || 0;
    const creatorLevels = this.levelSystem.get('creator_levels');

    let currentLevel = null;
    for (const [level, data] of Object.entries(creatorLevels)) {
      if (postCount >= data.posts) {
        currentLevel = data;
      }
    }

    return currentLevel;
  }

  notifyLevelUp(userId, newLevel, levelData) {
    console.log(`🎉 User ${userId} leveled up to ${levelData.name} ${levelData.badge}!`);
    // In production, send push notification
  }

  notifyMissionCompleted(userId, mission) {
    console.log(`✅ User ${userId} completed mission: ${mission.name}`);
    // In production, send push notification
  }

  notifyStreakBonus(userId, streakCount, bonus) {
    console.log(`🔥 User ${userId} got ${streakCount}-day streak bonus: TZS ${bonus.cash}!`);
    // In production, send push notification
  }

  // Public API methods
  async getUserEngagement(userId) {
    return this.userEngagement.get(userId) || this.createNewUserEngagement(userId);
  }

  async getUserCoins(userId) {
    const coins = await offlineStorage.get('user_coins', userId);
    return coins ? coins.coins : 0;
  }

  async getDailyMissions(userId) {
    const today = new Date().toDateString();
    const userEngagement = this.userEngagement.get(userId);
    const todayMissions = userEngagement?.dailyMissions[today] || {};

    const allMissions = [
      ...this.dailyMissions.get('basic_missions'),
      ...this.dailyMissions.get('premium_missions'),
    ];

    return allMissions.map(mission => ({
      ...mission,
      progress: todayMissions[mission.id]?.progress || 0,
      completed: todayMissions[mission.id]?.completed || false,
    }));
  }

  getEngagementStats() {
    const totalUsers = this.userEngagement.size;
    const activeUsers = Array.from(this.userEngagement.values())
      .filter(user => Date.now() - user.lastActivity < 24 * 60 * 60 * 1000).length;

    const totalRewards = Array.from(this.userEngagement.values())
      .reduce((sum, user) => sum + user.totalCash, 0);

    return {
      totalUsers,
      activeUsers,
      totalRewards,
      averageLevel: this.calculateAverageLevel(),
      topCreators: this.getTopCreators(),
    };
  }

  calculateAverageLevel() {
    const users = Array.from(this.userEngagement.values());
    if (users.length === 0) return 0;
    
    const totalLevels = users.reduce((sum, user) => sum + user.level, 0);
    return totalLevels / users.length;
  }

  getTopCreators(limit = 10) {
    return Array.from(this.userEngagement.values())
      .sort((a, b) => (b.actions.post_created || 0) - (a.actions.post_created || 0))
      .slice(0, limit)
      .map(user => ({
        userId: user.userId,
        posts: user.actions.post_created || 0,
        level: user.level,
        totalEarnings: user.totalCash,
      }));
  }

  // Mock methods (replace with real implementations)
  updateEngagementMetrics() { /* Update metrics */ }
  processDailyMissions() { /* Process missions */ }
  checkStreakBonuses() { /* Check streaks */ }
  updateLevels() { /* Update levels */ }
  resetDailyMissions() { /* Reset missions */ }
  processStreakBonuses() { /* Process streaks */ }
}

// Create singleton instance
const engagementRewardsService = new EngagementRewardsService();

export default engagementRewardsService;
