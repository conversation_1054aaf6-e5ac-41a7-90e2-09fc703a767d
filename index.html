<!DOCTYPE html>
<html lang="sw">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProChat - Tanzania's Super App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .status {
            background: rgba(255,255,255,0.1);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            display: inline-block;
            margin-top: 0.5rem;
        }
        
        .tabs {
            display: flex;
            background: white;
            border-bottom: 2px solid #e0e0e0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .tab {
            flex: 1;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            border: none;
            background: none;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .tab:hover {
            background: #f8f9fa;
        }
        
        .tab.active {
            color: #667eea;
            background: #f0f4ff;
        }
        
        .tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: #667eea;
        }
        
        .tab-content {
            padding: 2rem;
            min-height: 500px;
        }
        
        .tab-panel {
            display: none;
        }
        
        .tab-panel.active {
            display: block;
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .balance-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        
        .balance {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 1rem 0;
        }
        
        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .action-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
        }
        
        .action-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .post {
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 1rem;
            margin-bottom: 1rem;
        }
        
        .post:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .post-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 1rem;
        }
        
        .post-content {
            margin-bottom: 1rem;
            line-height: 1.6;
        }
        
        .post-actions {
            display: flex;
            gap: 1rem;
        }
        
        .post-action {
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        
        .post-action:hover {
            background: #f8f9fa;
            color: #333;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        @media (max-width: 768px) {
            .tab {
                padding: 0.8rem 0.5rem;
                font-size: 0.9rem;
            }
            
            .balance {
                font-size: 2rem;
            }
            
            .actions {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <h1>🚀 ProChat</h1>
            <div class="status" id="status">🔄 Connecting...</div>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="switchTab(0)">🏠 Nyumbani</button>
            <button class="tab" onclick="switchTab(1)">💬 Mazungumzo</button>
            <button class="tab" onclick="switchTab(2)">🔍 Gundua</button>
            <button class="tab" onclick="switchTab(3)">👤 Mimi</button>
        </div>
        
        <div class="tab-content">
            <!-- Home Tab -->
            <div class="tab-panel active" id="tab-0">
                <div class="card">
                    <h3>📱 Machapisho ya Kijamii</h3>
                    <div id="posts-container">
                        <div class="loading">Inapakia machapisho...</div>
                    </div>
                </div>
            </div>
            
            <!-- Chats Tab -->
            <div class="tab-panel" id="tab-1">
                <div class="card">
                    <h3>💬 Mazungumzo</h3>
                    <div id="chats-container">
                        <div class="loading">Inapakia mazungumzo...</div>
                    </div>
                </div>
            </div>
            
            <!-- Discover Tab -->
            <div class="tab-panel" id="tab-2">
                <div class="card">
                    <h3>🎫 Matukio</h3>
                    <div id="events-container">
                        <div class="loading">Inapakia matukio...</div>
                    </div>
                </div>
                
                <div class="card">
                    <h3>💼 Fursa za Kazi</h3>
                    <div id="jobs-container">
                        <div class="loading">Inapakia kazi...</div>
                    </div>
                </div>
            </div>
            
            <!-- Me Tab -->
            <div class="tab-panel" id="tab-3">
                <div class="card balance-card">
                    <h3>💰 ProPay Wallet</h3>
                    <div class="balance" id="balance">TZS 0</div>
                    <div class="actions">
                        <button class="action-btn" onclick="sendMoney()">📤 Tuma Pesa</button>
                        <button class="action-btn" onclick="receiveMoney()">📥 Pokea Pesa</button>
                        <button class="action-btn" onclick="payBills()">🧾 Lipa Bill</button>
                        <button class="action-btn" onclick="buyAirtime()">📱 Airtime</button>
                    </div>
                </div>
                
                <div class="card">
                    <h3>📊 Historia ya Malipo</h3>
                    <div id="transactions-container">
                        <div class="loading">Inapakia historia...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        const API_BASE = 'http://localhost:8080/api';
        
        // Tab switching
        function switchTab(tabIndex) {
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));
            
            document.querySelectorAll('.tab')[tabIndex].classList.add('active');
            document.getElementById(`tab-${tabIndex}`).classList.add('active');
            
            // Load data for active tab
            loadTabData(tabIndex);
        }
        
        // Load data based on active tab
        function loadTabData(tabIndex) {
            switch(tabIndex) {
                case 0: loadPosts(); break;
                case 1: loadChats(); break;
                case 2: loadEvents(); loadJobs(); break;
                case 3: loadBalance(); loadTransactions(); break;
            }
        }
        
        // API calls
        async function apiCall(endpoint) {
            try {
                const response = await fetch(`${API_BASE}${endpoint}`);
                return await response.json();
            } catch (error) {
                console.error('API Error:', error);
                return null;
            }
        }
        
        // Load posts
        async function loadPosts() {
            const posts = await apiCall('/posts');
            const container = document.getElementById('posts-container');
            
            if (posts) {
                container.innerHTML = posts.map(post => `
                    <div class="post">
                        <div class="post-header">
                            <div class="avatar">${post.user.username.charAt(0).toUpperCase()}</div>
                            <div>
                                <strong>${post.user.username}</strong><br>
                                <small>Sasa hivi</small>
                            </div>
                        </div>
                        <div class="post-content">${post.content}</div>
                        <div class="post-actions">
                            <button class="post-action">❤️ ${post.likes}</button>
                            <button class="post-action">💬 ${post.comments}</button>
                            <button class="post-action">📤 Share</button>
                        </div>
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<div class="error">Imeshindwa kupakia machapisho</div>';
            }
        }
        
        // Load chats
        async function loadChats() {
            const chats = await apiCall('/chats');
            const container = document.getElementById('chats-container');
            
            if (chats) {
                container.innerHTML = chats.map(chat => `
                    <div class="post">
                        <div class="post-header">
                            <div class="avatar">${chat.name.charAt(0)}</div>
                            <div>
                                <strong>${chat.name}</strong><br>
                                <small>${chat.lastMessage}</small>
                            </div>
                        </div>
                        ${chat.unreadCount > 0 ? `<span style="background: #ff4757; color: white; padding: 0.2rem 0.5rem; border-radius: 10px; font-size: 0.8rem;">${chat.unreadCount}</span>` : ''}
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<div class="error">Imeshindwa kupakia mazungumzo</div>';
            }
        }
        
        // Load events
        async function loadEvents() {
            const events = await apiCall('/events');
            const container = document.getElementById('events-container');
            
            if (events) {
                container.innerHTML = events.map(event => `
                    <div class="post">
                        <strong>${event.title}</strong><br>
                        <small>📅 ${event.date} | 📍 ${event.location} | 💰 TZS ${event.price.toLocaleString()}</small>
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<div class="error">Imeshindwa kupakia matukio</div>';
            }
        }
        
        // Load jobs
        async function loadJobs() {
            const jobs = await apiCall('/jobs');
            const container = document.getElementById('jobs-container');
            
            if (jobs) {
                container.innerHTML = jobs.map(job => `
                    <div class="post">
                        <strong>${job.title}</strong><br>
                        <small>🏢 ${job.company} | 💰 TZS ${job.salary} | 📍 ${job.location}</small>
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<div class="error">Imeshindwa kupakia kazi</div>';
            }
        }
        
        // Load balance
        async function loadBalance() {
            const balance = await apiCall('/wallet/balance');
            if (balance) {
                document.getElementById('balance').textContent = balance.formatted;
            }
        }
        
        // Load transactions
        async function loadTransactions() {
            const transactions = await apiCall('/wallet/transactions');
            const container = document.getElementById('transactions-container');
            
            if (transactions) {
                container.innerHTML = transactions.map(tx => `
                    <div class="post">
                        <strong>${tx.description}</strong><br>
                        <small>${tx.type} | TZS ${tx.amount.toLocaleString()} | ${tx.status}</small>
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<div class="error">Imeshindwa kupakia historia</div>';
            }
        }
        
        // ProPay actions
        function sendMoney() {
            alert('💸 Tuma Pesa\n\nFungua app ya ProPay kutuma pesa kwa M-Pesa, Airtel Money, au benki.');
        }
        
        function receiveMoney() {
            alert('📥 Pokea Pesa\n\nNambari yako ya akaunti: +255 XXX XXX XXX\nQR Code imetengenezwa!');
        }
        
        function payBills() {
            alert('🧾 Lipa Bills\n\nLipa:\n• LUKU (Umeme)\n• DAWASCO (Maji)\n• Simu na Internet\n• DStv/StarTimes');
        }
        
        function buyAirtime() {
            alert('📱 Nunua Airtime\n\nChagua mtandao:\n• Vodacom\n• Airtel\n• Tigo\n• Halotel');
        }
        
        // Check backend status
        async function checkStatus() {
            const health = await apiCall('/health');
            const statusEl = document.getElementById('status');
            
            if (health) {
                statusEl.textContent = '✅ Connected';
                statusEl.style.background = 'rgba(76, 175, 80, 0.2)';
            } else {
                statusEl.textContent = '❌ Disconnected';
                statusEl.style.background = 'rgba(244, 67, 54, 0.2)';
            }
        }
        
        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            checkStatus();
            loadTabData(0); // Load home tab data
            
            // Check status every 30 seconds
            setInterval(checkStatus, 30000);
        });
    </script>
</body>
</html>
