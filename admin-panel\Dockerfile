# ProChat Admin Panel Dockerfile
# Multi-stage build for React application

# Build stage
FROM node:18-alpine AS build

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production --silent

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy custom nginx config
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built app from build stage
COPY --from=build /app/build /usr/share/nginx/html

# Create non-root user
RUN addgroup -g 1001 -S prochat && \
    adduser -S prochat -u 1001

# Set permissions
RUN chown -R prochat:prochat /usr/share/nginx/html && \
    chown -R prochat:prochat /var/cache/nginx && \
    chown -R prochat:prochat /var/log/nginx && \
    chown -R prochat:prochat /etc/nginx/conf.d

# Switch to non-root user
USER prochat

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3000 || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
