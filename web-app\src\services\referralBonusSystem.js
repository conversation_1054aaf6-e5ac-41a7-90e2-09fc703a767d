// ProChat Advanced Referral & Sign-Up Bonus System
// Make the app VIRAL with amazing referral rewards!

import { SecurityLogger } from '../config/security';
import offlineStorage from './offlineStorage';
import walletSecurityService from './walletSecurity';
import smartNotificationService from './smartNotifications';
import adminFinancialControlService from './adminFinancialControl';
import fraudDetectionService from './fraudDetection';

class ReferralBonusService {
  constructor() {
    this.referralCodes = new Map();
    this.bonusHistory = new Map();
    this.referralChains = new Map();
    this.deviceTracking = new Map();
    this.ipTracking = new Map();
    this.bonusSettings = new Map();
    
    this.initializeReferralSystem();
  }

  initializeReferralSystem() {
    // Initialize bonus settings
    this.setupBonusSettings();
    
    // Initialize security measures
    this.setupSecurityMeasures();
    
    // Initialize referral tracking
    this.setupReferralTracking();
  }

  setupBonusSettings() {
    // 🎁 SIGN-UP BONUSES - Welcome bonuses for new users
    this.bonusSettings.set('signup_bonuses', {
      welcome_bonus: {
        amount: 1000, // TZS 1,000 welcome bonus
        coins: 100, // 100 coins
        enabled: true,
        description: 'Welcome to ProChat! Here\'s your starter bonus!',
      },
      profile_completion_bonus: {
        amount: 500, // TZS 500 for completing profile
        coins: 50,
        enabled: true,
        description: 'Profile completed! Bonus earned!',
      },
      phone_verification_bonus: {
        amount: 300, // TZS 300 for phone verification
        coins: 30,
        enabled: true,
        description: 'Phone verified! Security bonus!',
      },
      email_verification_bonus: {
        amount: 200, // TZS 200 for email verification
        coins: 20,
        enabled: true,
        description: 'Email verified! Communication bonus!',
      },
    });

    // 🤝 REFERRAL BONUSES - Bonuses for inviting friends
    this.bonusSettings.set('referral_bonuses', {
      referrer_bonus: {
        amount: 2000, // TZS 2,000 for person who invited
        coins: 200,
        enabled: true,
        description: 'Friend joined through your invite!',
      },
      referee_bonus: {
        amount: 1500, // TZS 1,500 for person who joined
        coins: 150,
        enabled: true,
        description: 'Welcome! Your friend invited you!',
      },
      mutual_activity_bonus: {
        amount: 1000, // TZS 1,000 when both are active for 7 days
        coins: 100,
        enabled: true,
        description: 'Both you and your friend are active!',
      },
    });

    // 🔥 VIRAL BONUSES - Multi-level referral bonuses
    this.bonusSettings.set('viral_bonuses', {
      level_2_bonus: {
        amount: 500, // TZS 500 when friend's friend joins
        coins: 50,
        enabled: true,
        description: 'Your referral network is growing!',
      },
      level_3_bonus: {
        amount: 200, // TZS 200 when friend's friend's friend joins
        coins: 20,
        enabled: true,
        description: 'Amazing referral network!',
      },
      viral_milestone_bonuses: {
        5_referrals: { amount: 5000, coins: 500, description: '5 friends invited!' },
        10_referrals: { amount: 15000, coins: 1500, description: '10 friends invited!' },
        25_referrals: { amount: 50000, coins: 5000, description: '25 friends invited!' },
        50_referrals: { amount: 150000, coins: 15000, description: '50 friends invited!' },
        100_referrals: { amount: 500000, coins: 50000, description: '100 friends invited!' },
      },
    });

    // 🎯 SPECIAL BONUSES - Time-limited and special bonuses
    this.bonusSettings.set('special_bonuses', {
      early_adopter_bonus: {
        amount: 5000, // TZS 5,000 for first 1000 users
        coins: 500,
        enabled: true,
        limit: 1000,
        description: 'Early adopter bonus!',
      },
      weekend_signup_bonus: {
        amount: 1500, // TZS 1,500 extra for weekend signups
        coins: 150,
        enabled: true,
        description: 'Weekend signup bonus!',
      },
      holiday_bonus: {
        amount: 3000, // TZS 3,000 during holidays
        coins: 300,
        enabled: false, // Enable during holidays
        description: 'Holiday special bonus!',
      },
    });
  }

  setupSecurityMeasures() {
    // Anti-fraud measures
    this.securitySettings = {
      max_referrals_per_device: 3, // Max 3 accounts per device
      max_referrals_per_ip: 5, // Max 5 accounts per IP
      min_activity_days: 7, // Minimum 7 days activity for bonus
      phone_verification_required: true,
      email_verification_required: true,
      device_fingerprinting: true,
      ip_geolocation_check: true,
      suspicious_pattern_detection: true,
    };
  }

  setupReferralTracking() {
    // Initialize tracking systems
    console.log('🤝 Referral & Bonus System: Initialized');
  }

  // 🎁 SIGN-UP BONUS PROCESSING
  async processSignUpBonus(userId, bonusType, metadata = {}) {
    try {
      // Check if user already received this bonus
      const existingBonus = await this.checkExistingBonus(userId, bonusType);
      if (existingBonus) {
        return { success: false, reason: 'Bonus already received' };
      }

      // Get bonus settings
      const signupBonuses = this.bonusSettings.get('signup_bonuses');
      const bonus = signupBonuses[bonusType];
      
      if (!bonus || !bonus.enabled) {
        return { success: false, reason: 'Bonus not available' };
      }

      // Security checks
      const securityCheck = await this.performSecurityChecks(userId, 'signup', metadata);
      if (!securityCheck.passed) {
        return { success: false, reason: securityCheck.reason };
      }

      // Process bonus
      const result = await this.awardBonus(userId, bonus, bonusType, 'signup');
      
      if (result.success) {
        // Log bonus
        await this.logBonusTransaction(userId, bonusType, bonus, 'signup');
        
        // Send notification
        await smartNotificationService.triggerMoneyEarnedNotification(
          userId,
          bonus.amount,
          bonus.description
        );
      }

      return result;

    } catch (error) {
      console.error('Failed to process signup bonus:', error);
      return { success: false, reason: 'Bonus processing failed' };
    }
  }

  // 🤝 REFERRAL BONUS PROCESSING
  async processReferralBonus(referrerId, refereeId, referralCode) {
    try {
      // Validate referral code
      const codeValidation = await this.validateReferralCode(referralCode, referrerId);
      if (!codeValidation.valid) {
        return { success: false, reason: codeValidation.reason };
      }

      // Check if referral already processed
      const existingReferral = await this.checkExistingReferral(referrerId, refereeId);
      if (existingReferral) {
        return { success: false, reason: 'Referral already processed' };
      }

      // Security checks for both users
      const referrerCheck = await this.performSecurityChecks(referrerId, 'referral_give');
      const refereeCheck = await this.performSecurityChecks(refereeId, 'referral_receive');
      
      if (!referrerCheck.passed || !refereeCheck.passed) {
        return { 
          success: false, 
          reason: 'Security check failed',
          details: { referrerCheck, refereeCheck }
        };
      }

      // Get referral bonus settings
      const referralBonuses = this.bonusSettings.get('referral_bonuses');
      
      // Award bonus to referrer (person who invited)
      const referrerResult = await this.awardBonus(
        referrerId,
        referralBonuses.referrer_bonus,
        'referrer_bonus',
        'referral'
      );

      // Award bonus to referee (person who joined)
      const refereeResult = await this.awardBonus(
        refereeId,
        referralBonuses.referee_bonus,
        'referee_bonus',
        'referral'
      );

      if (referrerResult.success && refereeResult.success) {
        // Create referral chain
        await this.createReferralChain(referrerId, refereeId, referralCode);
        
        // Process viral bonuses (multi-level)
        await this.processViralBonuses(referrerId, refereeId);
        
        // Log referral
        await this.logReferralTransaction(referrerId, refereeId, referralCode);
        
        // Send notifications
        await smartNotificationService.triggerMoneyEarnedNotification(
          referrerId,
          referralBonuses.referrer_bonus.amount,
          `Friend ${refereeId} joined through your invite!`
        );
        
        await smartNotificationService.triggerMoneyEarnedNotification(
          refereeId,
          referralBonuses.referee_bonus.amount,
          'Welcome bonus from your friend\'s invite!'
        );

        return {
          success: true,
          referrerBonus: referralBonuses.referrer_bonus.amount,
          refereeBonus: referralBonuses.referee_bonus.amount,
        };
      }

      return { success: false, reason: 'Bonus award failed' };

    } catch (error) {
      console.error('Failed to process referral bonus:', error);
      return { success: false, reason: 'Referral processing failed' };
    }
  }

  // 🔥 VIRAL BONUS PROCESSING (Multi-level referrals)
  async processViralBonuses(referrerId, newUserId) {
    try {
      const viralBonuses = this.bonusSettings.get('viral_bonuses');
      
      // Get referral chain
      const chain = await this.getReferralChain(referrerId);
      
      // Level 2 bonus (referrer's referrer gets bonus)
      if (chain.level1Referrer) {
        await this.awardBonus(
          chain.level1Referrer,
          viralBonuses.level_2_bonus,
          'level_2_viral',
          'viral'
        );
        
        await smartNotificationService.triggerMoneyEarnedNotification(
          chain.level1Referrer,
          viralBonuses.level_2_bonus.amount,
          'Your referral network is growing!'
        );
      }

      // Level 3 bonus (referrer's referrer's referrer gets bonus)
      if (chain.level2Referrer) {
        await this.awardBonus(
          chain.level2Referrer,
          viralBonuses.level_3_bonus,
          'level_3_viral',
          'viral'
        );
      }

      // Check milestone bonuses
      await this.checkMilestoneBonuses(referrerId);

    } catch (error) {
      console.error('Failed to process viral bonuses:', error);
    }
  }

  // 🎯 MILESTONE BONUS CHECKING
  async checkMilestoneBonuses(userId) {
    try {
      const referralCount = await this.getUserReferralCount(userId);
      const viralBonuses = this.bonusSettings.get('viral_bonuses');
      const milestones = viralBonuses.viral_milestone_bonuses;

      // Check each milestone
      for (const [milestone, bonus] of Object.entries(milestones)) {
        const requiredCount = parseInt(milestone.split('_')[0]);
        
        if (referralCount >= requiredCount) {
          // Check if milestone bonus already awarded
          const alreadyAwarded = await this.checkExistingBonus(userId, `milestone_${milestone}`);
          
          if (!alreadyAwarded) {
            await this.awardBonus(userId, bonus, `milestone_${milestone}`, 'milestone');
            
            await smartNotificationService.triggerMoneyEarnedNotification(
              userId,
              bonus.amount,
              `🎉 MILESTONE ACHIEVED! ${bonus.description}`
            );
          }
        }
      }
    } catch (error) {
      console.error('Failed to check milestone bonuses:', error);
    }
  }

  // 🔒 SECURITY CHECKS
  async performSecurityChecks(userId, actionType, metadata = {}) {
    try {
      const checks = {
        passed: true,
        reason: '',
        flags: [],
      };

      // 1. Device fingerprinting check
      const deviceId = metadata.deviceId || 'unknown';
      const deviceCount = this.deviceTracking.get(deviceId) || 0;
      
      if (deviceCount >= this.securitySettings.max_referrals_per_device) {
        checks.passed = false;
        checks.reason = 'Too many accounts from this device';
        checks.flags.push('device_limit_exceeded');
      }

      // 2. IP address check
      const ipAddress = metadata.ipAddress || 'unknown';
      const ipCount = this.ipTracking.get(ipAddress) || 0;
      
      if (ipCount >= this.securitySettings.max_referrals_per_ip) {
        checks.passed = false;
        checks.reason = 'Too many accounts from this IP';
        checks.flags.push('ip_limit_exceeded');
      }

      // 3. Phone verification check
      if (this.securitySettings.phone_verification_required) {
        const phoneVerified = await this.checkPhoneVerification(userId);
        if (!phoneVerified) {
          checks.passed = false;
          checks.reason = 'Phone verification required';
          checks.flags.push('phone_not_verified');
        }
      }

      // 4. Email verification check
      if (this.securitySettings.email_verification_required) {
        const emailVerified = await this.checkEmailVerification(userId);
        if (!emailVerified) {
          checks.passed = false;
          checks.reason = 'Email verification required';
          checks.flags.push('email_not_verified');
        }
      }

      // 5. Fraud detection check
      const fraudCheck = await fraudDetectionService.checkUser(userId);
      if (fraudCheck.blocked || fraudCheck.suspicious) {
        checks.passed = false;
        checks.reason = 'User flagged by fraud detection';
        checks.flags.push('fraud_detected');
      }

      // Update tracking if checks passed
      if (checks.passed) {
        this.deviceTracking.set(deviceId, deviceCount + 1);
        this.ipTracking.set(ipAddress, ipCount + 1);
      }

      return checks;

    } catch (error) {
      return {
        passed: false,
        reason: 'Security check failed',
        flags: ['security_error'],
      };
    }
  }

  // 💰 BONUS AWARDING
  async awardBonus(userId, bonus, bonusType, category) {
    try {
      // Check admin financial controls
      const adminCheck = await adminFinancialControlService.processRewardTransaction(
        userId,
        bonusType,
        { amount: bonus.amount, category }
      );

      if (!adminCheck.success) {
        return { success: false, reason: adminCheck.reason };
      }

      // Award cash to wallet
      if (bonus.amount > 0) {
        const walletResult = await walletSecurityService.processSecureTransaction(
          'system',
          {
            amount: bonus.amount,
            type: 'bonus',
            description: bonus.description,
            recipient: userId,
          },
          {
            method: 'system',
            systemAuth: true,
          }
        );

        if (!walletResult.success) {
          return { success: false, reason: 'Wallet transaction failed' };
        }
      }

      // Award coins
      if (bonus.coins > 0) {
        await this.addCoinsToUser(userId, bonus.coins);
      }

      return {
        success: true,
        amount: bonus.amount,
        coins: bonus.coins,
        description: bonus.description,
      };

    } catch (error) {
      console.error('Failed to award bonus:', error);
      return { success: false, reason: 'Bonus award failed' };
    }
  }

  // 🔗 REFERRAL CODE MANAGEMENT
  generateReferralCode(userId) {
    const code = `PC${userId.slice(-4)}${Math.random().toString(36).substr(2, 4).toUpperCase()}`;
    
    this.referralCodes.set(code, {
      userId,
      createdAt: Date.now(),
      usageCount: 0,
      active: true,
    });

    return code;
  }

  async validateReferralCode(code, expectedUserId) {
    const codeData = this.referralCodes.get(code);
    
    if (!codeData) {
      return { valid: false, reason: 'Invalid referral code' };
    }

    if (!codeData.active) {
      return { valid: false, reason: 'Referral code deactivated' };
    }

    if (codeData.userId !== expectedUserId) {
      return { valid: false, reason: 'Referral code mismatch' };
    }

    return { valid: true, codeData };
  }

  // 📊 ANALYTICS & REPORTING
  async getReferralStats(userId) {
    const stats = {
      totalReferrals: await this.getUserReferralCount(userId),
      totalEarned: await this.getUserReferralEarnings(userId),
      activeReferrals: await this.getActiveReferrals(userId),
      referralCode: await this.getUserReferralCode(userId),
      milestoneProgress: await this.getMilestoneProgress(userId),
      recentReferrals: await this.getRecentReferrals(userId, 10),
    };

    return stats;
  }

  async getSystemReferralStats() {
    return {
      totalUsers: this.bonusHistory.size,
      totalBonusesAwarded: Array.from(this.bonusHistory.values()).length,
      totalAmountAwarded: this.calculateTotalBonusAmount(),
      topReferrers: await this.getTopReferrers(10),
      conversionRate: await this.calculateConversionRate(),
      averageBonusPerUser: await this.calculateAverageBonusPerUser(),
    };
  }

  // Utility methods
  async checkExistingBonus(userId, bonusType) {
    const bonuses = Array.from(this.bonusHistory.values());
    return bonuses.find(b => b.userId === userId && b.bonusType === bonusType);
  }

  async checkExistingReferral(referrerId, refereeId) {
    const chains = Array.from(this.referralChains.values());
    return chains.find(c => c.referrerId === referrerId && c.refereeId === refereeId);
  }

  async logBonusTransaction(userId, bonusType, bonus, category) {
    const logId = this.generateLogId();
    const logEntry = {
      id: logId,
      userId,
      bonusType,
      category,
      amount: bonus.amount,
      coins: bonus.coins,
      description: bonus.description,
      timestamp: Date.now(),
    };

    this.bonusHistory.set(logId, logEntry);

    SecurityLogger.logSecurityEvent('BONUS_AWARDED', {
      userId,
      bonusType,
      amount: bonus.amount,
      category,
    });
  }

  async logReferralTransaction(referrerId, refereeId, referralCode) {
    const logId = this.generateLogId();
    const logEntry = {
      id: logId,
      referrerId,
      refereeId,
      referralCode,
      timestamp: Date.now(),
    };

    await offlineStorage.add('referral_transactions', logEntry);
  }

  async createReferralChain(referrerId, refereeId, referralCode) {
    const chainId = this.generateLogId();
    const chain = {
      id: chainId,
      referrerId,
      refereeId,
      referralCode,
      createdAt: Date.now(),
    };

    this.referralChains.set(chainId, chain);
  }

  generateLogId() {
    return `ref_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Mock methods (implement with real data)
  async checkPhoneVerification(userId) { return true; }
  async checkEmailVerification(userId) { return true; }
  async addCoinsToUser(userId, coins) { /* Add coins */ }
  async getReferralChain(userId) { return { level1Referrer: null, level2Referrer: null }; }
  async getUserReferralCount(userId) { return 0; }
  async getUserReferralEarnings(userId) { return 0; }
  async getActiveReferrals(userId) { return []; }
  async getUserReferralCode(userId) { return this.generateReferralCode(userId); }
  async getMilestoneProgress(userId) { return {}; }
  async getRecentReferrals(userId, limit) { return []; }
  calculateTotalBonusAmount() { return 0; }
  async getTopReferrers(limit) { return []; }
  async calculateConversionRate() { return 0; }
  async calculateAverageBonusPerUser() { return 0; }

  // Public API methods
  async processWelcomeBonus(userId, metadata) {
    return await this.processSignUpBonus(userId, 'welcome_bonus', metadata);
  }

  async processProfileCompletionBonus(userId) {
    return await this.processSignUpBonus(userId, 'profile_completion_bonus');
  }

  async processPhoneVerificationBonus(userId) {
    return await this.processSignUpBonus(userId, 'phone_verification_bonus');
  }

  async processEmailVerificationBonus(userId) {
    return await this.processSignUpBonus(userId, 'email_verification_bonus');
  }

  getBonusSettings() {
    return Object.fromEntries(this.bonusSettings);
  }

  getBonusHistory(limit = 100) {
    return Array.from(this.bonusHistory.values())
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }
}

// Create singleton instance
const referralBonusService = new ReferralBonusService();

export default referralBonusService;
