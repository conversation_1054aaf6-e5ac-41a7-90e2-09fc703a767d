// ProChat Home Tab Component
// Social media feed with posts, stories, and interactions

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Avatar,
  IconButton,
  Button,
  Chip,
  Grid,
  Paper,
  Divider,
  TextField,
  InputAdornment,
  Fab,
  Badge,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Favorite,
  FavoriteBorder,
  ChatBubbleOutline,
  Share,
  MoreVert,
  Add,
  Search,
  Verified,
  CardGiftcard,
  Repeat,
  Visibility,
  TrendingUp,
  EmojiEvents,
  PhotoCamera,
  VideoCall,
  Mic,
} from '@mui/icons-material';

const HomeTab = () => {
  const [posts, setPosts] = useState([]);
  const [stories, setStories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  useEffect(() => {
    loadFeedData();
  }, []);

  const loadFeedData = async () => {
    setLoading(true);
    
    // Mock stories data
    const mockStories = [
      {
        id: 'story_1',
        user: {
          name: 'Yako',
          avatar: '/avatars/you.jpg',
          isYou: true,
        },
        hasStory: false,
      },
      {
        id: 'story_2',
        user: {
          name: 'Mary Kimani',
          avatar: '/avatars/mary.jpg',
          isVerified: false,
        },
        hasStory: true,
        viewed: false,
      },
      {
        id: 'story_3',
        user: {
          name: 'ProChat Official',
          avatar: '/avatars/prochat.jpg',
          isVerified: true,
        },
        hasStory: true,
        viewed: true,
      },
      {
        id: 'story_4',
        user: {
          name: 'John Mwangi',
          avatar: '/avatars/john.jpg',
          isVerified: false,
        },
        hasStory: true,
        viewed: false,
      },
    ];

    // Mock posts data
    const mockPosts = [
      {
        id: 'post_1',
        user: {
          name: 'John Mwangi',
          username: '@johnmwangi',
          avatar: '/avatars/john.jpg',
          isVerified: true,
        },
        content: 'Habari za asubuhi! Leo ni siku nzuri ya kufanya kazi. Nimepata kazi mpya kupitia ProChat Jobs! 🎉 #Motivation #Tanzania #ProChatJobs',
        images: ['/posts/post1.jpg'],
        timestamp: '2 saa zilizopita',
        likes: 45,
        comments: 12,
        shares: 8,
        gifts: 3,
        impressions: 234,
        isLiked: false,
        location: 'Dar es Salaam, Tanzania',
      },
      {
        id: 'post_2',
        user: {
          name: 'Mary Kimani',
          username: '@marykimani',
          avatar: '/avatars/mary.jpg',
          isVerified: false,
        },
        content: 'Nimepata kazi mpya! Asante kwa wote mlionisaidia. ProChat ni app ya kweli! 🙏✨',
        images: [],
        timestamp: '4 saa zilizopita',
        likes: 128,
        comments: 34,
        shares: 15,
        gifts: 7,
        impressions: 567,
        isLiked: true,
        location: 'Nairobi, Kenya',
      },
      {
        id: 'post_3',
        user: {
          name: 'ProChat Official',
          username: '@prochat',
          avatar: '/avatars/prochat.jpg',
          isVerified: true,
        },
        content: 'Tumefurahi kuwasilisha kipengele kipya cha ProPay! Sasa unaweza kulipa bili zako moja kwa moja kwenye app. 💰\n\n✅ M-Pesa\n✅ Airtel Money\n✅ Kadi za Kimataifa\n✅ Bitcoin & USDT\n\n#ProPay #DigitalPayments #Tanzania',
        images: ['/posts/propay1.jpg', '/posts/propay2.jpg'],
        timestamp: '6 saa zilizopita',
        likes: 289,
        comments: 67,
        shares: 145,
        gifts: 23,
        impressions: 1890,
        isLiked: false,
        isPinned: true,
      },
      {
        id: 'post_4',
        user: {
          name: 'Sarah Mwalimu',
          username: '@sarahmwalimu',
          avatar: '/avatars/sarah.jpg',
          isVerified: false,
        },
        content: 'Nimejifunza programming kupitia ProChat Learning! Sasa nina skills za React na Node.js. Asanteni ProChat! 💻📚',
        images: ['/posts/learning.jpg'],
        timestamp: '8 saa zilizopita',
        likes: 76,
        comments: 19,
        shares: 12,
        gifts: 5,
        impressions: 345,
        isLiked: false,
        location: 'Mwanza, Tanzania',
      },
    ];

    setStories(mockStories);
    setPosts(mockPosts);
    setLoading(false);
  };

  const handleLike = (postId) => {
    setPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? {
              ...post,
              isLiked: !post.isLiked,
              likes: post.isLiked ? post.likes - 1 : post.likes + 1,
            }
          : post
      )
    );
  };

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const renderStories = () => (
    <Paper sx={{ p: 2, mb: 2 }}>
      <Typography variant="h6" gutterBottom>
        Hadithi
      </Typography>
      <Box sx={{ display: 'flex', gap: 2, overflowX: 'auto', pb: 1 }}>
        {stories.map((story) => (
          <Box key={story.id} sx={{ textAlign: 'center', minWidth: 70 }}>
            <Box
              sx={{
                position: 'relative',
                width: 60,
                height: 60,
                borderRadius: '50%',
                border: story.hasStory ? (story.viewed ? 2 : 3) : 0,
                borderColor: story.viewed ? 'grey.400' : 'primary.main',
                p: story.hasStory ? 0.5 : 0,
                cursor: 'pointer',
              }}
            >
              <Avatar
                src={story.user.avatar}
                sx={{ width: '100%', height: '100%' }}
              />
              {story.user.isYou && (
                <IconButton
                  sx={{
                    position: 'absolute',
                    bottom: -5,
                    right: -5,
                    width: 24,
                    height: 24,
                    bgcolor: 'primary.main',
                    color: 'white',
                    '&:hover': { bgcolor: 'primary.dark' },
                  }}
                >
                  <Add sx={{ fontSize: 16 }} />
                </IconButton>
              )}
            </Box>
            <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
              {story.user.isYou ? 'Yako' : story.user.name.split(' ')[0]}
            </Typography>
          </Box>
        ))}
      </Box>
    </Paper>
  );

  const renderPost = (post) => (
    <Card key={post.id} sx={{ mb: 2 }}>
      <CardContent>
        {/* Post Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar src={post.user.avatar} sx={{ mr: 2 }} />
          <Box sx={{ flex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mr: 1 }}>
                {post.user.name}
              </Typography>
              {post.user.isVerified && (
                <Verified sx={{ fontSize: 16, color: 'primary.main', mr: 1 }} />
              )}
              {post.isPinned && (
                <Chip label="Iliyobandikwa" size="small" color="primary" sx={{ mr: 1 }} />
              )}
            </Box>
            <Typography variant="body2" color="text.secondary">
              {post.user.username} • {post.timestamp}
              {post.location && ` • ${post.location}`}
            </Typography>
          </Box>
          <IconButton>
            <MoreVert />
          </IconButton>
        </Box>

        {/* Post Content */}
        <Typography variant="body1" sx={{ mb: 2, lineHeight: 1.6 }}>
          {post.content}
        </Typography>

        {/* Post Images */}
        {post.images.length > 0 && (
          <Box sx={{ mb: 2 }}>
            {post.images.length === 1 ? (
              <Box
                component="img"
                src={post.images[0]}
                alt="Post image"
                sx={{
                  width: '100%',
                  maxHeight: 400,
                  objectFit: 'cover',
                  borderRadius: 2,
                  bgcolor: 'grey.100',
                }}
                onError={(e) => {
                  e.target.style.display = 'none';
                }}
              />
            ) : (
              <Grid container spacing={1}>
                {post.images.slice(0, 2).map((image, index) => (
                  <Grid item xs={6} key={index}>
                    <Box
                      component="img"
                      src={image}
                      alt={`Post image ${index + 1}`}
                      sx={{
                        width: '100%',
                        height: 200,
                        objectFit: 'cover',
                        borderRadius: 2,
                        bgcolor: 'grey.100',
                      }}
                      onError={(e) => {
                        e.target.style.display = 'none';
                      }}
                    />
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        )}

        {/* Post Stats */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            <Visibility sx={{ fontSize: 16, mr: 0.5 }} />
            {formatNumber(post.impressions)} maoni • {formatNumber(post.likes)} likes • {formatNumber(post.comments)} majibu
          </Typography>
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Post Actions */}
        <Box sx={{ display: 'flex', justifyContent: 'space-around' }}>
          <Button
            startIcon={post.isLiked ? <Favorite /> : <FavoriteBorder />}
            onClick={() => handleLike(post.id)}
            sx={{
              color: post.isLiked ? 'error.main' : 'text.secondary',
              '&:hover': { bgcolor: 'error.light', color: 'error.main' },
            }}
          >
            {formatNumber(post.likes)}
          </Button>

          <Button
            startIcon={<ChatBubbleOutline />}
            sx={{
              color: 'text.secondary',
              '&:hover': { bgcolor: 'primary.light', color: 'primary.main' },
            }}
          >
            {formatNumber(post.comments)}
          </Button>

          <Button
            startIcon={<Repeat />}
            sx={{
              color: 'text.secondary',
              '&:hover': { bgcolor: 'success.light', color: 'success.main' },
            }}
          >
            {formatNumber(post.shares)}
          </Button>

          <Button
            startIcon={<CardGiftcard />}
            sx={{
              color: 'text.secondary',
              '&:hover': { bgcolor: 'warning.light', color: 'warning.main' },
            }}
          >
            {formatNumber(post.gifts)}
          </Button>

          <IconButton
            sx={{
              color: 'text.secondary',
              '&:hover': { bgcolor: 'info.light', color: 'info.main' },
            }}
          >
            <Share />
          </IconButton>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ maxWidth: 600, mx: 'auto', p: isMobile ? 1 : 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          ProChat
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton>
            <Badge badgeContent={3} color="error">
              <TrendingUp />
            </Badge>
          </IconButton>
          <IconButton>
            <Badge badgeContent={5} color="error">
              <EmojiEvents />
            </Badge>
          </IconButton>
        </Box>
      </Box>

      {/* Search Bar */}
      <TextField
        fullWidth
        placeholder="Tafuta kwenye ProChat..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Search />
            </InputAdornment>
          ),
        }}
        sx={{ mb: 3 }}
      />

      {/* Stories */}
      {renderStories()}

      {/* Posts Feed */}
      {posts.map(renderPost)}

      {/* Floating Action Button */}
      <Fab
        color="primary"
        sx={{
          position: 'fixed',
          bottom: isMobile ? 90 : 20,
          right: 20,
        }}
      >
        <Add />
      </Fab>

      {/* Quick Actions for Mobile */}
      {isMobile && (
        <Box
          sx={{
            position: 'fixed',
            bottom: 90,
            right: 20,
            display: 'flex',
            flexDirection: 'column',
            gap: 1,
          }}
        >
          <Fab size="small" color="secondary">
            <PhotoCamera />
          </Fab>
          <Fab size="small" color="info">
            <VideoCall />
          </Fab>
          <Fab size="small" color="success">
            <Mic />
          </Fab>
        </Box>
      )}
    </Box>
  );
};

export default HomeTab;
