{"version": 3, "sources": ["../../../../../../src/run/ios/appleDevice/protocol/UsbmuxProtocol.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport plist from '@expo/plist';\nimport Debug from 'debug';\nimport { Socket } from 'net';\n\nimport type { ProtocolWriter } from './AbstractProtocol';\nimport { PlistProtocolReader, ProtocolClient, ProtocolReaderFactory } from './AbstractProtocol';\n\nconst debug = Debug('expo:apple-device:protocol:usbmux');\n\nexport const USBMUXD_HEADER_SIZE = 16;\n\nexport interface UsbmuxMessage {\n  messageType: string;\n  extraFields?: { [key: string]: any };\n}\n\nexport class UsbmuxProtocolClient extends ProtocolClient<UsbmuxMessage> {\n  constructor(socket: Socket) {\n    super(socket, new ProtocolReaderFactory(UsbmuxProtocolReader), new UsbmuxProtocolWriter());\n  }\n}\n\nexport class UsbmuxProtocolReader extends PlistProtocolReader {\n  constructor(callback: (data: any) => any) {\n    super(USBMUXD_HEADER_SIZE, callback);\n  }\n\n  parseHeader(data: Buffer) {\n    return data.readUInt32LE(0) - USBMUXD_HEADER_SIZE;\n  }\n\n  parseBody(data: Buffer) {\n    const resp = super.parseBody(data);\n    debug(`Response: ${JSON.stringify(resp)}`);\n    return resp;\n  }\n}\n\nexport class UsbmuxProtocolWriter implements ProtocolWriter {\n  private useTag = 0;\n\n  write(socket: Socket, msg: UsbmuxMessage) {\n    // TODO Usbmux message type\n    debug(`socket write: ${JSON.stringify(msg)}`);\n    const { messageType, extraFields } = msg;\n    const plistMessage = plist.build({\n      BundleID: 'dev.expo.native-run', // TODO\n      ClientVersionString: 'usbmux.js', // TODO\n      MessageType: messageType,\n      ProgName: 'native-run', // TODO\n      kLibUSBMuxVersion: 3,\n      ...extraFields,\n    });\n\n    const dataSize = plistMessage ? plistMessage.length : 0;\n    const protocolVersion = 1;\n    const messageCode = 8;\n\n    const header = Buffer.alloc(USBMUXD_HEADER_SIZE);\n    header.writeUInt32LE(USBMUXD_HEADER_SIZE + dataSize, 0);\n    header.writeUInt32LE(protocolVersion, 4);\n    header.writeUInt32LE(messageCode, 8);\n    header.writeUInt32LE(this.useTag++, 12); // TODO\n    socket.write(header);\n    socket.write(plistMessage);\n  }\n}\n"], "names": ["USBMUXD_HEADER_SIZE", "UsbmuxProtocolClient", "UsbmuxProtocolReader", "UsbmuxProtocolWriter", "debug", "Debug", "ProtocolClient", "constructor", "socket", "ProtocolReaderFactory", "PlistProtocolReader", "callback", "parse<PERSON><PERSON><PERSON>", "data", "readUInt32LE", "parseBody", "resp", "JSON", "stringify", "write", "msg", "messageType", "extraFields", "plistMessage", "plist", "build", "BundleID", "ClientVersionString", "MessageType", "ProgName", "kLibUSBMuxVersion", "dataSize", "length", "protocolVersion", "messageCode", "header", "<PERSON><PERSON><PERSON>", "alloc", "writeUInt32LE", "useTag"], "mappings": "AAAA;;;;;;CAMC;;;;;;;;;;;IAWYA,mBAAmB;eAAnBA;;IAOAC,oBAAoB;eAApBA;;IAMAC,oBAAoB;eAApBA;;IAgBAC,oBAAoB;eAApBA;;;;gEAtCK;;;;;;;gEACA;;;;;;kCAIyD;;;;;;AAE3E,MAAMC,QAAQC,IAAAA,gBAAK,EAAC;AAEb,MAAML,sBAAsB;AAO5B,MAAMC,6BAA6BK,gCAAc;IACtDC,YAAYC,MAAc,CAAE;QAC1B,KAAK,CAACA,QAAQ,IAAIC,uCAAqB,CAACP,uBAAuB,IAAIC;IACrE;AACF;AAEO,MAAMD,6BAA6BQ,qCAAmB;IAC3DH,YAAYI,QAA4B,CAAE;QACxC,KAAK,CAACX,qBAAqBW;IAC7B;IAEAC,YAAYC,IAAY,EAAE;QACxB,OAAOA,KAAKC,YAAY,CAAC,KAAKd;IAChC;IAEAe,UAAUF,IAAY,EAAE;QACtB,MAAMG,OAAO,KAAK,CAACD,UAAUF;QAC7BT,MAAM,CAAC,UAAU,EAAEa,KAAKC,SAAS,CAACF,OAAO;QACzC,OAAOA;IACT;AACF;AAEO,MAAMb;IAGXgB,MAAMX,MAAc,EAAEY,GAAkB,EAAE;QACxC,2BAA2B;QAC3BhB,MAAM,CAAC,cAAc,EAAEa,KAAKC,SAAS,CAACE,MAAM;QAC5C,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAE,GAAGF;QACrC,MAAMG,eAAeC,gBAAK,CAACC,KAAK,CAAC;YAC/BC,UAAU;YACVC,qBAAqB;YACrBC,aAAaP;YACbQ,UAAU;YACVC,mBAAmB;YACnB,GAAGR,WAAW;QAChB;QAEA,MAAMS,WAAWR,eAAeA,aAAaS,MAAM,GAAG;QACtD,MAAMC,kBAAkB;QACxB,MAAMC,cAAc;QAEpB,MAAMC,SAASC,OAAOC,KAAK,CAACrC;QAC5BmC,OAAOG,aAAa,CAACtC,sBAAsB+B,UAAU;QACrDI,OAAOG,aAAa,CAACL,iBAAiB;QACtCE,OAAOG,aAAa,CAACJ,aAAa;QAClCC,OAAOG,aAAa,CAAC,IAAI,CAACC,MAAM,IAAI,KAAK,OAAO;QAChD/B,OAAOW,KAAK,CAACgB;QACb3B,OAAOW,KAAK,CAACI;IACf;;aA1BQgB,SAAS;;AA2BnB"}