// ProChat Cross-Platform Security Configuration
// Shared security model for Web + Mobile App

// Environment-based configuration
const SECURITY_CONFIG = {
  development: {
    JWT_EXPIRY: '24h',
    REFRESH_EXPIRY: '7d',
    OTP_EXPIRY: 300, // 5 minutes
    MAX_LOGIN_ATTEMPTS: 5,
    LOCKOUT_DURATION: 900, // 15 minutes
    ENABLE_2FA: false,
    ENABLE_BIOMETRIC: false,
  },
  production: {
    JWT_EXPIRY: '1h',
    REFRESH_EXPIRY: '30d',
    OTP_EXPIRY: 180, // 3 minutes
    MAX_LOGIN_ATTEMPTS: 3,
    LOCKOUT_DURATION: 1800, // 30 minutes
    ENABLE_2FA: true,
    ENABLE_BIOMETRIC: true,
  }
};

const ENV = process.env.NODE_ENV || 'development';
export const securityConfig = SECURITY_CONFIG[ENV];

// API Keys Management (Separate for Web & Mobile)
export const API_KEYS = {
  WEB: {
    FIREBASE: process.env.REACT_APP_FIREBASE_API_KEY,
    GOOGLE_MAPS: process.env.REACT_APP_GOOGLE_MAPS_KEY,
    RECAPTCHA: process.env.REACT_APP_RECAPTCHA_SITE_KEY,
    CLOUDFLARE: process.env.REACT_APP_CLOUDFLARE_KEY,
  },
  MOBILE: {
    FIREBASE: process.env.REACT_APP_FIREBASE_API_KEY_MOBILE,
    GOOGLE_MAPS: process.env.REACT_APP_GOOGLE_MAPS_KEY_MOBILE,
    BIOMETRIC: process.env.REACT_APP_BIOMETRIC_KEY,
  },
  SHARED: {
    AWS_S3: process.env.REACT_APP_AWS_ACCESS_KEY,
    BACKEND: process.env.REACT_APP_API_BASE_URL,
    SOCKET: process.env.REACT_APP_SOCKET_URL,
  }
};

// Security Headers for Web
export const SECURITY_HEADERS = {
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' https://apis.google.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' wss: https:",
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=(self)',
};

// JWT Token Management
export class TokenManager {
  static setTokens(accessToken, refreshToken, platform = 'web') {
    const storage = platform === 'web' ? localStorage : sessionStorage;
    
    storage.setItem('prochat_access_token', accessToken);
    storage.setItem('prochat_refresh_token', refreshToken);
    storage.setItem('prochat_platform', platform);
    storage.setItem('prochat_login_time', Date.now().toString());
  }

  static getAccessToken(platform = 'web') {
    const storage = platform === 'web' ? localStorage : sessionStorage;
    return storage.getItem('prochat_access_token');
  }

  static getRefreshToken(platform = 'web') {
    const storage = platform === 'web' ? localStorage : sessionStorage;
    return storage.getItem('prochat_refresh_token');
  }

  static clearTokens(platform = 'web') {
    const storage = platform === 'web' ? localStorage : sessionStorage;
    
    storage.removeItem('prochat_access_token');
    storage.removeItem('prochat_refresh_token');
    storage.removeItem('prochat_platform');
    storage.removeItem('prochat_login_time');
    storage.removeItem('prochat_user_data');
  }

  static isTokenExpired(token) {
    if (!token) return true;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 < Date.now();
    } catch (error) {
      return true;
    }
  }

  static getTokenPayload(token) {
    if (!token) return null;
    
    try {
      return JSON.parse(atob(token.split('.')[1]));
    } catch (error) {
      return null;
    }
  }
}

// OTP Management
export class OTPManager {
  static generateOTP(length = 6) {
    const digits = '0123456789';
    let otp = '';
    for (let i = 0; i < length; i++) {
      otp += digits[Math.floor(Math.random() * 10)];
    }
    return otp;
  }

  static storeOTP(phoneNumber, otp, purpose = 'login') {
    const otpData = {
      otp,
      purpose,
      timestamp: Date.now(),
      attempts: 0,
      maxAttempts: 3,
    };
    
    sessionStorage.setItem(`prochat_otp_${phoneNumber}`, JSON.stringify(otpData));
    
    // Auto-clear after expiry
    setTimeout(() => {
      sessionStorage.removeItem(`prochat_otp_${phoneNumber}`);
    }, securityConfig.OTP_EXPIRY * 1000);
  }

  static verifyOTP(phoneNumber, inputOTP) {
    const storedData = sessionStorage.getItem(`prochat_otp_${phoneNumber}`);
    
    if (!storedData) {
      return { success: false, error: 'OTP imeisha muda' };
    }

    const otpData = JSON.parse(storedData);
    
    // Check expiry
    if (Date.now() - otpData.timestamp > securityConfig.OTP_EXPIRY * 1000) {
      sessionStorage.removeItem(`prochat_otp_${phoneNumber}`);
      return { success: false, error: 'OTP imeisha muda' };
    }

    // Check attempts
    if (otpData.attempts >= otpData.maxAttempts) {
      sessionStorage.removeItem(`prochat_otp_${phoneNumber}`);
      return { success: false, error: 'Umezidi majaribio' };
    }

    // Verify OTP
    if (otpData.otp === inputOTP) {
      sessionStorage.removeItem(`prochat_otp_${phoneNumber}`);
      return { success: true };
    } else {
      otpData.attempts++;
      sessionStorage.setItem(`prochat_otp_${phoneNumber}`, JSON.stringify(otpData));
      return { 
        success: false, 
        error: `OTP si sahihi. Umebakia majaribio ${otpData.maxAttempts - otpData.attempts}` 
      };
    }
  }
}

// Device Fingerprinting
export class DeviceManager {
  static async getDeviceFingerprint() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('ProChat Device ID', 2, 2);
    
    const fingerprint = {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      screen: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      canvas: canvas.toDataURL(),
      timestamp: Date.now(),
    };

    return btoa(JSON.stringify(fingerprint));
  }

  static storeDeviceInfo(deviceId, trusted = false) {
    const devices = JSON.parse(localStorage.getItem('prochat_devices') || '[]');
    
    const existingDevice = devices.find(d => d.id === deviceId);
    if (existingDevice) {
      existingDevice.lastSeen = Date.now();
      existingDevice.trusted = trusted;
    } else {
      devices.push({
        id: deviceId,
        trusted,
        firstSeen: Date.now(),
        lastSeen: Date.now(),
      });
    }

    localStorage.setItem('prochat_devices', JSON.stringify(devices));
  }

  static isDeviceTrusted(deviceId) {
    const devices = JSON.parse(localStorage.getItem('prochat_devices') || '[]');
    const device = devices.find(d => d.id === deviceId);
    return device?.trusted || false;
  }
}

// Rate Limiting
export class RateLimiter {
  static checkRateLimit(action, maxAttempts = 5, windowMs = 900000) { // 15 minutes
    const key = `prochat_rate_${action}`;
    const attempts = JSON.parse(localStorage.getItem(key) || '[]');
    const now = Date.now();
    
    // Remove old attempts outside the window
    const validAttempts = attempts.filter(timestamp => now - timestamp < windowMs);
    
    if (validAttempts.length >= maxAttempts) {
      const oldestAttempt = Math.min(...validAttempts);
      const waitTime = Math.ceil((windowMs - (now - oldestAttempt)) / 1000 / 60);
      return {
        allowed: false,
        waitTime,
        message: `Umezidi majaribio. Jaribu tena baada ya dakika ${waitTime}`
      };
    }

    // Add current attempt
    validAttempts.push(now);
    localStorage.setItem(key, JSON.stringify(validAttempts));
    
    return {
      allowed: true,
      remaining: maxAttempts - validAttempts.length
    };
  }

  static clearRateLimit(action) {
    localStorage.removeItem(`prochat_rate_${action}`);
  }
}

// Encryption Utilities
export class EncryptionUtils {
  static async hashPassword(password) {
    const encoder = new TextEncoder();
    const data = encoder.encode(password + 'ProChatSalt2024');
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  static encryptSensitiveData(data, key = 'ProChatKey2024') {
    // Simple XOR encryption for client-side (not for production sensitive data)
    let encrypted = '';
    for (let i = 0; i < data.length; i++) {
      encrypted += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
    }
    return btoa(encrypted);
  }

  static decryptSensitiveData(encryptedData, key = 'ProChatKey2024') {
    try {
      const encrypted = atob(encryptedData);
      let decrypted = '';
      for (let i = 0; i < encrypted.length; i++) {
        decrypted += String.fromCharCode(encrypted.charCodeAt(i) ^ key.charCodeAt(i % key.length));
      }
      return decrypted;
    } catch (error) {
      return null;
    }
  }
}

// Security Event Logger
export class SecurityLogger {
  static logSecurityEvent(event, details = {}) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      details,
      userAgent: navigator.userAgent,
      url: window.location.href,
      sessionId: sessionStorage.getItem('prochat_session_id'),
    };

    // Store locally (in production, send to backend)
    const logs = JSON.parse(localStorage.getItem('prochat_security_logs') || '[]');
    logs.push(logEntry);
    
    // Keep only last 100 logs
    if (logs.length > 100) {
      logs.splice(0, logs.length - 100);
    }
    
    localStorage.setItem('prochat_security_logs', JSON.stringify(logs));
    
    // In production, also send to backend
    if (ENV === 'production') {
      // Send to backend security monitoring
      console.log('Security Event:', logEntry);
    }
  }
}

export default {
  securityConfig,
  API_KEYS,
  SECURITY_HEADERS,
  TokenManager,
  OTPManager,
  DeviceManager,
  RateLimiter,
  EncryptionUtils,
  SecurityLogger,
};
