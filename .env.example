# ProChat Environment Configuration
# Copy this file to .env and update with your actual values

# ================================
# DATABASE CONFIGURATION
# ================================
MYSQL_ROOT_PASSWORD=Ram$0101
MYSQL_DATABASE=prochat_db
MYSQL_USER=prochat_user
MYSQL_PASSWORD=Ram$0101
MYSQL_HOST=localhost
MYSQL_PORT=3306

# ================================
# AWS S3 CONFIGURATION
# ================================
AWS_ACCESS_KEY=your_aws_access_key_here
AWS_SECRET_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1
AWS_S3_BUCKET=prochat-media-storage
AWS_S3_URL=https://prochat-media-storage.s3.amazonaws.com

# ================================
# TWILIO SMS CONFIGURATION
# ================================
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_FROM_NUMBER=your_twilio_phone_number

# ================================
# FIREBASE PUSH NOTIFICATIONS
# ================================
FIREBASE_SERVER_KEY=your_firebase_server_key
FIREBASE_PROJECT_ID=your_firebase_project_id

# ================================
# EMAIL CONFIGURATION
# ================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=Ram$0101
EMAIL_FROM=<EMAIL>

# ================================
# JWT SECURITY
# ================================
JWT_SECRET=138a6ffe9eaadc0fae5368ececa8a52dbc4b31b7e36bbcd5d9b27e49e6d081c5c898375073ff68d40a554175bc5c9b143cd04a18c9a2992e1136f7b907ff05a3791395ecff50ac87a422300c8a2d62605e7a93d4019cef460bb7bbf89cb4ba9a5b09416bcbf301f94e547f32e27963b38f8aa550d061833c93144968235b5d1656103b8151c2dae299e107abc60983ba9189017e90125e96d9cd9a5f093136674166dfb1ba897fa1aa81566dbb210046c935a5bce277179f2de06d0f1f53fcc3dd4f01c968cb9b5588736a2de3e449e036800966be4950746f2619b808f5203d0ca8f4f73774b4d73c595890b9b622d3c3ccf7e69e6aad424e478d98e54d98f3
JWT_EXPIRATION=86400000
JWT_REFRESH_EXPIRATION=604800000

# ================================
# PROPAY CONFIGURATION
# ================================
PROPAY_TRANSACTION_FEE=0.025
PROPAY_WITHDRAWAL_FEE=1000
PROPAY_MINIMUM_WITHDRAWAL=10000
PROPAY_MAXIMUM_WITHDRAWAL=5000000
PROPAY_AGENT_COMMISSION=0.01
PROPAY_MERCHANT_COMMISSION=0.015

# ================================
# ADMIN PANEL CONFIGURATION
# ================================
ADMIN_DEFAULT_PASSWORD=ProChat2024!
ADMIN_SESSION_TIMEOUT=3600
ADMIN_MAX_LOGIN_ATTEMPTS=5
ADMIN_LOCKOUT_DURATION=1800

# ================================
# API CONFIGURATION
# ================================
API_RATE_LIMIT=100
API_TIMEOUT=30000
API_MAX_FILE_SIZE=50MB

# ================================
# CONTENT MODERATION
# ================================
AI_MODERATION_ENABLED=true
AUTO_APPROVE_THRESHOLD=80
AUTO_REJECT_THRESHOLD=30

# ================================
# EVENT MANAGEMENT
# ================================
MAX_TICKETS_PER_USER=10
TICKET_EXPIRY_HOURS=24
QR_CODE_EXPIRY_MINUTES=15

# ================================
# JOB MANAGEMENT
# ================================
MAX_APPLICATIONS_PER_JOB=1000
APPLICATION_EXPIRY_DAYS=30
AUTO_RANKING_ENABLED=true

# ================================
# DIGITAL INVITATIONS
# ================================
SMS_COST_PER_MESSAGE=50
MAX_RECIPIENTS_PER_INVITATION=500

# ================================
# SYSTEM MAINTENANCE
# ================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 0 2 * * ?
CLEANUP_OLD_LOGS_DAYS=30
CLEANUP_OLD_FILES_DAYS=90

# ================================
# ENVIRONMENT SETTINGS
# ================================
NODE_ENV=production
SPRING_PROFILES_ACTIVE=production
LOG_LEVEL=INFO

# ================================
# DOMAIN CONFIGURATION
# ================================
MAIN_DOMAIN=prochat.co.tz
ADMIN_DOMAIN=admin.prochat.co.tz
API_DOMAIN=api.prochat.co.tz

# ================================
# SSL CONFIGURATION
# ================================
SSL_ENABLED=true
SSL_CERT_PATH=/etc/nginx/ssl/
SSL_KEY_PATH=/etc/nginx/ssl/

# ================================
# REDIS CONFIGURATION
# ================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0

# ================================
# MONITORING & ANALYTICS
# ================================
ANALYTICS_ENABLED=true
MONITORING_ENABLED=true
HEALTH_CHECK_INTERVAL=60

# ================================
# THIRD PARTY INTEGRATIONS
# ================================
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
GOOGLE_TRANSLATE_API_KEY=your_google_translate_api_key
FACEBOOK_APP_ID=your_facebook_app_id
TWITTER_API_KEY=your_twitter_api_key

# ================================
# MOBILE APP CONFIGURATION
# ================================
MOBILE_APP_VERSION=1.0.0
MOBILE_FORCE_UPDATE=false
MOBILE_MAINTENANCE_MODE=false

# ================================
# PAYMENT GATEWAY CONFIGURATION
# ================================
MPESA_CONSUMER_KEY=your_mpesa_consumer_key
MPESA_CONSUMER_SECRET=your_mpesa_consumer_secret
MPESA_SHORTCODE=your_mpesa_shortcode
MPESA_PASSKEY=your_mpesa_passkey

TIGOPESA_USERNAME=your_tigopesa_username
TIGOPESA_PASSWORD=your_tigopesa_password
TIGOPESA_ACCOUNT=your_tigopesa_account

AIRTEL_MONEY_CLIENT_ID=your_airtel_money_client_id
AIRTEL_MONEY_CLIENT_SECRET=your_airtel_money_client_secret

# ================================
# SECURITY SETTINGS
# ================================
CORS_ALLOWED_ORIGINS=https://prochat.co.tz,https://admin.prochat.co.tz
RATE_LIMIT_REQUESTS_PER_MINUTE=100
SESSION_TIMEOUT_MINUTES=60
PASSWORD_MIN_LENGTH=8
TWO_FACTOR_REQUIRED=false

# ================================
# NOTIFICATION SETTINGS
# ================================
EMAIL_NOTIFICATIONS_ENABLED=true
SMS_NOTIFICATIONS_ENABLED=true
PUSH_NOTIFICATIONS_ENABLED=true

# ================================
# DEVELOPMENT SETTINGS (for local development only)
# ================================
# Uncomment these for local development
# MYSQL_HOST=localhost
# REDIS_HOST=localhost
# NODE_ENV=development
# SPRING_PROFILES_ACTIVE=development
# LOG_LEVEL=DEBUG
