import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  Typography,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  IconButton,
  Badge,
  Avatar,
  Divider,
  Paper,
  Chip,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Home as HomeIcon,
  Explore as ExploreIcon,
  Chat as ChatIcon,
  Person as PersonIcon,
  Event as EventIcon,
  Work as WorkIcon,
  Article as NewsIcon,
  VideoCall as LiveIcon,
  AccountBalance as ProPayIcon,
  Store as ProZoneIcon,
  Notifications as NotificationsIcon,
  Search as SearchIcon,
  Settings as SettingsIcon,
  Menu as MenuIcon,
} from '@mui/icons-material';

// Hooks
import { useAuth } from '../../contexts/AuthContext';

const drawerWidth = 280;

const DesktopLayout = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [mobileOpen, setMobileOpen] = useState(false);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const menuItems = [
    {
      section: 'Kuu',
      items: [
        { text: 'Dashboard', icon: <DashboardIcon />, path: '/dashboard', badge: null },
        { text: 'Mazungumzo', icon: <ChatIcon />, path: '/chats', badge: 12 },
        { text: 'Nyumbani', icon: <HomeIcon />, path: '/home', badge: null },
        { text: 'Gundua', icon: <ExploreIcon />, path: '/discover', badge: null },
        { text: 'Mimi', icon: <PersonIcon />, path: '/me', badge: null },
      ]
    },
    {
      section: 'ProPay & ProZone',
      items: [
        { text: 'ProPay Wallet', icon: <ProPayIcon />, path: '/propay', badge: null },
        { text: 'ProZone', icon: <ProZoneIcon />, path: '/prozone', badge: 'Beta' },
        { text: 'Wallet', icon: <AccountBalance />, path: '/wallet', badge: null },
        { text: 'Tuma Pesa', icon: <AccountBalance />, path: '/send-money', badge: null },
      ]
    },
    {
      section: 'Huduma za Kijamii',
      items: [
        { text: 'Zawadi', icon: <EventIcon />, path: '/gift', badge: null },
        { text: 'Michango', icon: <WorkIcon />, path: '/donation', badge: null },
        { text: 'Kazi', icon: <WorkIcon />, path: '/tasks', badge: 'Mpya' },
      ]
    },
    {
      section: 'Mipangilio',
      items: [
        { text: 'Wasifu', icon: <PersonIcon />, path: '/profile', badge: null },
        { text: 'Mipangilio', icon: <SettingsIcon />, path: '/settings', badge: null },
        { text: 'Arifa', icon: <Notifications />, path: '/notifications', badge: 3 },
        { text: 'Alika Marafiki', icon: <PersonIcon />, path: '/invite-friends', badge: null },
        { text: 'Msaada', icon: <SettingsIcon />, path: '/help-support', badge: null },
      ]
    }
  ];

  const isActive = (path) => {
    return location.pathname === path;
  };

  const renderBadge = (badge) => {
    if (!badge) return null;
    
    if (typeof badge === 'number') {
      return (
        <Chip 
          label={badge} 
          size="small" 
          color="error" 
          sx={{ height: 20, fontSize: '0.75rem' }}
        />
      );
    }
    
    if (badge === 'LIVE') {
      return (
        <Chip 
          label={badge} 
          size="small" 
          color="error" 
          variant="filled"
          sx={{ 
            height: 20, 
            fontSize: '0.75rem',
            animation: 'pulse 2s infinite',
            '@keyframes pulse': {
              '0%': { opacity: 1 },
              '50%': { opacity: 0.5 },
              '100%': { opacity: 1 },
            }
          }}
        />
      );
    }
    
    return (
      <Chip 
        label={badge} 
        size="small" 
        color="primary" 
        variant="outlined"
        sx={{ height: 20, fontSize: '0.75rem' }}
      />
    );
  };

  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo/Brand */}
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          ProChat
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Tanzania's Premier Platform
        </Typography>
      </Box>

      <Divider />

      {/* Navigation Menu */}
      <Box sx={{ flexGrow: 1, overflow: 'auto', py: 1 }}>
        {menuItems.map((section, sectionIndex) => (
          <Box key={sectionIndex} sx={{ mb: 2 }}>
            <Typography 
              variant="overline" 
              sx={{ 
                px: 3, 
                py: 1, 
                display: 'block',
                color: 'text.secondary',
                fontWeight: 'bold',
                fontSize: '0.75rem',
              }}
            >
              {section.section}
            </Typography>
            <List sx={{ px: 1 }}>
              {section.items.map((item, index) => (
                <ListItem key={index} disablePadding>
                  <ListItemButton
                    onClick={() => navigate(item.path)}
                    selected={isActive(item.path)}
                    sx={{
                      borderRadius: 2,
                      mx: 1,
                      mb: 0.5,
                      '&.Mui-selected': {
                        bgcolor: 'primary.main',
                        color: 'white',
                        '&:hover': {
                          bgcolor: 'primary.dark',
                        },
                        '& .MuiListItemIcon-root': {
                          color: 'white',
                        },
                      },
                      '&:hover': {
                        bgcolor: 'action.hover',
                      },
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      {item.icon}
                    </ListItemIcon>
                    <ListItemText 
                      primary={item.text}
                      primaryTypographyProps={{
                        fontSize: '0.9rem',
                        fontWeight: isActive(item.path) ? 'bold' : 'normal',
                      }}
                    />
                    {renderBadge(item.badge)}
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          </Box>
        ))}
      </Box>

      <Divider />

      {/* User Profile Section */}
      <Box sx={{ p: 2 }}>
        <Paper 
          elevation={1} 
          sx={{ 
            p: 2, 
            borderRadius: 2,
            bgcolor: 'background.paper',
            border: '1px solid',
            borderColor: 'divider',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar 
              src={user?.profilePicture} 
              alt={user?.name}
              sx={{ width: 40, height: 40 }}
            >
              {user?.name?.charAt(0)}
            </Avatar>
            <Box sx={{ flexGrow: 1, minWidth: 0 }}>
              <Typography variant="subtitle2" noWrap>
                {user?.name || 'Mtumiaji'}
              </Typography>
              <Typography variant="caption" color="text.secondary" noWrap>
                {user?.email || '<EMAIL>'}
              </Typography>
            </Box>
          </Box>
        </Paper>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
          bgcolor: 'background.paper',
          color: 'text.primary',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          borderBottom: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {location.pathname === '/dashboard' ? 'Dashboard' :
             location.pathname === '/chats' ? 'Mazungumzo' :
             location.pathname === '/home' ? 'Nyumbani' :
             location.pathname === '/discover' ? 'Gundua' :
             location.pathname === '/me' ? 'Mimi' :
             location.pathname.startsWith('/chat') ? 'Mazungumzo' :
             location.pathname === '/propay' ? 'ProPay' :
             location.pathname === '/prozone' ? 'ProZone' :
             location.pathname === '/wallet' ? 'Wallet' :
             location.pathname === '/send-money' ? 'Tuma Pesa' :
             location.pathname === '/gift' ? 'Zawadi' :
             location.pathname === '/donation' ? 'Michango' :
             location.pathname === '/tasks' ? 'Kazi' :
             location.pathname === '/profile' ? 'Wasifu' :
             location.pathname === '/settings' ? 'Mipangilio' :
             location.pathname === '/notifications' ? 'Arifa' :
             location.pathname === '/invite-friends' ? 'Alika Marafiki' :
             location.pathname === '/help-support' ? 'Msaada' : 'ProChat'}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <IconButton color="inherit">
              <SearchIcon />
            </IconButton>
            <IconButton color="inherit">
              <Badge badgeContent={7} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box', 
              width: drawerWidth,
              borderRight: '1px solid',
              borderColor: 'divider',
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          height: '100vh',
          overflow: 'auto',
          bgcolor: 'background.default',
        }}
      >
        <Toolbar />
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default DesktopLayout;
