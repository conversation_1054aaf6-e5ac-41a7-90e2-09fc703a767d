import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { io } from 'socket.io-client';
import { useAuth } from './AuthContext';
import toast from 'react-hot-toast';

// Socket Context
const SocketContext = createContext();

// Socket Provider Component
export const SocketProvider = ({ children }) => {
  const { user, isAuthenticated, token } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState([]);
  const socketRef = useRef(null);

  // Initialize socket connection
  useEffect(() => {
    if (isAuthenticated && token && user) {
      connectSocket();
    } else {
      disconnectSocket();
    }

    return () => {
      disconnectSocket();
    };
  }, [isAuthenticated, token, user]);

  const connectSocket = () => {
    if (socketRef.current?.connected) {
      return;
    }

    console.log('🔌 Connecting to socket server...');

    socketRef.current = io(process.env.REACT_APP_SOCKET_URL || 'http://localhost:8080', {
      auth: {
        token: token,
        userId: user?.id,
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true,
    });

    // Connection events
    socketRef.current.on('connect', () => {
      console.log('✅ Socket connected:', socketRef.current.id);
      setIsConnected(true);
      
      // Join user room
      socketRef.current.emit('join-user-room', user.id);
    });

    socketRef.current.on('disconnect', (reason) => {
      console.log('❌ Socket disconnected:', reason);
      setIsConnected(false);
    });

    socketRef.current.on('connect_error', (error) => {
      console.error('🔥 Socket connection error:', error);
      setIsConnected(false);
    });

    // User presence events
    socketRef.current.on('user-online', (userId) => {
      setOnlineUsers(prev => [...new Set([...prev, userId])]);
    });

    socketRef.current.on('user-offline', (userId) => {
      setOnlineUsers(prev => prev.filter(id => id !== userId));
    });

    socketRef.current.on('online-users', (users) => {
      setOnlineUsers(users);
    });

    // Chat events
    socketRef.current.on('new-message', (message) => {
      console.log('💬 New message received:', message);
      
      // Show notification if not in chat page
      if (!window.location.pathname.includes('/chat')) {
        toast.success(`Ujumbe mpya kutoka ${message.sender.name}`);
      }
      
      // Trigger custom event for chat components
      window.dispatchEvent(new CustomEvent('new-message', { detail: message }));
    });

    socketRef.current.on('message-read', (data) => {
      console.log('👁️ Message read:', data);
      window.dispatchEvent(new CustomEvent('message-read', { detail: data }));
    });

    socketRef.current.on('typing-start', (data) => {
      window.dispatchEvent(new CustomEvent('typing-start', { detail: data }));
    });

    socketRef.current.on('typing-stop', (data) => {
      window.dispatchEvent(new CustomEvent('typing-stop', { detail: data }));
    });

    // Post events
    socketRef.current.on('new-post', (post) => {
      console.log('📝 New post:', post);
      window.dispatchEvent(new CustomEvent('new-post', { detail: post }));
    });

    socketRef.current.on('post-liked', (data) => {
      window.dispatchEvent(new CustomEvent('post-liked', { detail: data }));
    });

    socketRef.current.on('post-commented', (data) => {
      window.dispatchEvent(new CustomEvent('post-commented', { detail: data }));
    });

    // Notification events
    socketRef.current.on('notification', (notification) => {
      console.log('🔔 New notification:', notification);
      
      // Show toast notification
      toast.success(notification.message);
      
      // Trigger custom event
      window.dispatchEvent(new CustomEvent('new-notification', { detail: notification }));
    });

    // Live stream events
    socketRef.current.on('stream-started', (stream) => {
      console.log('📺 Stream started:', stream);
      window.dispatchEvent(new CustomEvent('stream-started', { detail: stream }));
    });

    socketRef.current.on('stream-ended', (streamId) => {
      console.log('📺 Stream ended:', streamId);
      window.dispatchEvent(new CustomEvent('stream-ended', { detail: { streamId } }));
    });

    // Event updates
    socketRef.current.on('event-updated', (event) => {
      console.log('📅 Event updated:', event);
      window.dispatchEvent(new CustomEvent('event-updated', { detail: event }));
    });

    // Job updates
    socketRef.current.on('job-posted', (job) => {
      console.log('💼 New job posted:', job);
      window.dispatchEvent(new CustomEvent('job-posted', { detail: job }));
    });
  };

  const disconnectSocket = () => {
    if (socketRef.current) {
      console.log('🔌 Disconnecting socket...');
      socketRef.current.disconnect();
      socketRef.current = null;
      setIsConnected(false);
      setOnlineUsers([]);
    }
  };

  // Socket utility functions
  const emit = (event, data) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data);
    } else {
      console.warn('Socket not connected, cannot emit:', event);
    }
  };

  const joinRoom = (roomId) => {
    emit('join-room', roomId);
  };

  const leaveRoom = (roomId) => {
    emit('leave-room', roomId);
  };

  // Chat functions
  const sendMessage = (conversationId, message) => {
    emit('send-message', {
      conversationId,
      message,
      timestamp: new Date().toISOString(),
    });
  };

  const markMessageAsRead = (messageId, conversationId) => {
    emit('mark-message-read', { messageId, conversationId });
  };

  const startTyping = (conversationId) => {
    emit('typing-start', { conversationId, userId: user?.id });
  };

  const stopTyping = (conversationId) => {
    emit('typing-stop', { conversationId, userId: user?.id });
  };

  // Live stream functions
  const joinStream = (streamId) => {
    emit('join-stream', streamId);
  };

  const leaveStream = (streamId) => {
    emit('leave-stream', streamId);
  };

  const sendStreamComment = (streamId, comment) => {
    emit('stream-comment', { streamId, comment });
  };

  // Utility functions
  const isUserOnline = (userId) => {
    return onlineUsers.includes(userId);
  };

  const getOnlineUsersCount = () => {
    return onlineUsers.length;
  };

  // Context value
  const value = {
    socket: socketRef.current,
    isConnected,
    onlineUsers,
    emit,
    joinRoom,
    leaveRoom,
    sendMessage,
    markMessageAsRead,
    startTyping,
    stopTyping,
    joinStream,
    leaveStream,
    sendStreamComment,
    isUserOnline,
    getOnlineUsersCount,
    connectSocket,
    disconnectSocket,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};

// Custom hook to use socket context
export const useSocket = () => {
  const context = useContext(SocketContext);
  
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  
  return context;
};
