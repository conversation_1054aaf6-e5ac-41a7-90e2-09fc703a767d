import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Avatar,
  IconButton,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
  Button,
} from '@mui/material';
import {
  TrendingUp,
  People,
  Event,
  Work,
  AccountBalance,
  Notifications,
  MoreVert,
  Add,
  ArrowUpward,
  ArrowDownward,
  Circle,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useSocket } from '../../contexts/SocketContext';

const DashboardPage = () => {
  const { user } = useAuth();
  const { onlineUsers, isConnected } = useSocket();
  const [stats, setStats] = useState({
    totalPosts: 0,
    totalFollowers: 0,
    totalEvents: 0,
    walletBalance: 0,
  });

  const [recentActivities, setRecentActivities] = useState([]);
  const [upcomingEvents, setUpcomingEvents] = useState([]);
  const [notifications, setNotifications] = useState([]);

  useEffect(() => {
    // Fetch dashboard data
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    // Mock data - replace with actual API calls
    setStats({
      totalPosts: 45,
      totalFollowers: 1250,
      totalEvents: 8,
      walletBalance: 125000,
    });

    setRecentActivities([
      {
        id: 1,
        type: 'post',
        message: 'John Doe aliongeza chapisho jipya',
        time: '2 dakika zilizopita',
        avatar: '/avatars/john.jpg',
      },
      {
        id: 2,
        type: 'event',
        message: 'Tukio la "Tech Meetup" limeanza',
        time: '5 dakika zilizopita',
        avatar: '/avatars/event.jpg',
      },
      {
        id: 3,
        type: 'payment',
        message: 'Umepokea TSh 50,000 kutoka Mary',
        time: '10 dakika zilizopita',
        avatar: '/avatars/mary.jpg',
      },
    ]);

    setUpcomingEvents([
      {
        id: 1,
        title: 'Mkutano wa Biashara',
        date: '2024-01-15',
        time: '14:00',
        attendees: 45,
      },
      {
        id: 2,
        title: 'Semina ya Teknolojia',
        date: '2024-01-18',
        time: '09:00',
        attendees: 120,
      },
    ]);

    setNotifications([
      {
        id: 1,
        message: 'Una ujumbe mpya kutoka Sarah',
        time: '1 dakika iliyopita',
        read: false,
      },
      {
        id: 2,
        message: 'Tukio lako "Workshop" limeanza',
        time: '5 dakika zilizopita',
        read: false,
      },
    ]);
  };

  const StatCard = ({ title, value, icon, color, change }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar sx={{ bgcolor: color, mr: 2 }}>
            {icon}
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
              {value.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
          </Box>
        </Box>
        {change && (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {change > 0 ? (
              <ArrowUpward sx={{ color: 'success.main', fontSize: 16 }} />
            ) : (
              <ArrowDownward sx={{ color: 'error.main', fontSize: 16 }} />
            )}
            <Typography
              variant="caption"
              sx={{
                color: change > 0 ? 'success.main' : 'error.main',
                ml: 0.5,
              }}
            >
              {Math.abs(change)}% wiki hii
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );

  const QuickActionCard = ({ title, description, icon, color, onClick }) => (
    <Card 
      sx={{ 
        height: '100%', 
        cursor: 'pointer',
        '&:hover': { transform: 'translateY(-2px)' },
        transition: 'transform 0.2s',
      }}
      onClick={onClick}
    >
      <CardContent sx={{ textAlign: 'center', py: 3 }}>
        <Avatar sx={{ bgcolor: color, mx: 'auto', mb: 2, width: 56, height: 56 }}>
          {icon}
        </Avatar>
        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
          {title}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {description}
        </Typography>
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Welcome Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          Karibu, {user?.name || 'Mtumiaji'}! 👋
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Hapa kuna muhtasari wa shughuli zako za leo
        </Typography>
        
        {/* Connection Status */}
        <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
          <Circle 
            sx={{ 
              color: isConnected ? 'success.main' : 'error.main',
              fontSize: 12,
              mr: 1,
            }} 
          />
          <Typography variant="caption" color="text.secondary">
            {isConnected ? 'Umeunganishwa' : 'Haujaungana'} • 
            {onlineUsers.length} watumiaji wako mtandaoni
          </Typography>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Statistics Cards */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Machapisho"
            value={stats.totalPosts}
            icon={<TrendingUp />}
            color="primary.main"
            change={12}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Wafuasi"
            value={stats.totalFollowers}
            icon={<People />}
            color="secondary.main"
            change={8}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Matukio"
            value={stats.totalEvents}
            icon={<Event />}
            color="warning.main"
            change={-2}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Salio (TSh)"
            value={stats.walletBalance}
            icon={<AccountBalance />}
            color="success.main"
            change={15}
          />
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
            Vitendo vya Haraka
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <QuickActionCard
                title="Chapisho Jipya"
                description="Shiriki kitu kipya na marafiki"
                icon={<Add />}
                color="primary.main"
                onClick={() => console.log('Create post')}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <QuickActionCard
                title="Tukio Jipya"
                description="Panga tukio la umma"
                icon={<Event />}
                color="secondary.main"
                onClick={() => console.log('Create event')}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <QuickActionCard
                title="Tuma Pesa"
                description="Tuma pesa kwa haraka"
                icon={<AccountBalance />}
                color="success.main"
                onClick={() => console.log('Send money')}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <QuickActionCard
                title="Tafuta Kazi"
                description="Ona nafasi za kazi mpya"
                icon={<Work />}
                color="warning.main"
                onClick={() => console.log('Browse jobs')}
              />
            </Grid>
          </Grid>
        </Grid>

        {/* Recent Activities */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '400px' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                Shughuli za Hivi Karibuni
              </Typography>
              <IconButton size="small">
                <MoreVert />
              </IconButton>
            </Box>
            <List>
              {recentActivities.map((activity, index) => (
                <React.Fragment key={activity.id}>
                  <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar src={activity.avatar} />
                    </ListItemAvatar>
                    <ListItemText
                      primary={activity.message}
                      secondary={activity.time}
                      primaryTypographyProps={{ variant: 'body2' }}
                      secondaryTypographyProps={{ variant: 'caption' }}
                    />
                  </ListItem>
                  {index < recentActivities.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
            <Button fullWidth variant="outlined" sx={{ mt: 2 }}>
              Ona Zote
            </Button>
          </Paper>
        </Grid>

        {/* Upcoming Events & Notifications */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={2} sx={{ height: '400px' }}>
            {/* Upcoming Events */}
            <Grid item xs={12}>
              <Paper sx={{ p: 3, height: '190px' }}>
                <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                  Matukio Yanayokuja
                </Typography>
                {upcomingEvents.map((event) => (
                  <Box key={event.id} sx={{ mb: 2 }}>
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      {event.title}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {event.date} saa {event.time} • {event.attendees} watahudhuria
                    </Typography>
                  </Box>
                ))}
                <Button size="small" variant="text">
                  Ona Matukio Yote
                </Button>
              </Paper>
            </Grid>

            {/* Notifications */}
            <Grid item xs={12}>
              <Paper sx={{ p: 3, height: '190px' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                    Arifa
                  </Typography>
                  <Chip 
                    label={notifications.filter(n => !n.read).length} 
                    size="small" 
                    color="error" 
                  />
                </Box>
                {notifications.map((notification) => (
                  <Box key={notification.id} sx={{ mb: 1 }}>
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        fontWeight: notification.read ? 'normal' : 'bold',
                        opacity: notification.read ? 0.7 : 1,
                      }}
                    >
                      {notification.message}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {notification.time}
                    </Typography>
                  </Box>
                ))}
                <Button size="small" variant="text">
                  Ona Arifa Zote
                </Button>
              </Paper>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
