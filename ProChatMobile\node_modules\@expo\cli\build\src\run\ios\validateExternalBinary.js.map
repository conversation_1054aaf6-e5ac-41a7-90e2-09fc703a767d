{"version": 3, "sources": ["../../../../src/run/ios/validateExternalBinary.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport fs from 'fs';\nimport { glob as globAsync } from 'glob';\nimport path from 'path';\n\nimport { createTempDirectoryPath } from '../../utils/createTempPath';\nimport { CommandError } from '../../utils/errors';\nimport { parsePlistAsync } from '../../utils/plist';\n\nconst debug = require('debug')('expo:run:ios:binary');\n\nexport async function getValidBinaryPathAsync(input: string, props: { isSimulator: boolean }) {\n  const resolved = path.resolve(input);\n\n  if (!fs.existsSync(resolved)) {\n    throw new CommandError(`The path to the iOS binary does not exist: ${resolved}`);\n  }\n\n  // If the file is an ipa then move it to a temp directory and extract the app binary.\n  if (resolved.endsWith('.ipa')) {\n    const outputPath = createTempDirectoryPath();\n    debug('Extracting IPA:', resolved, outputPath);\n    const appDir = await extractIpaAsync(resolved, outputPath);\n\n    if (props.isSimulator) {\n      assertProvisionedForSimulator(appDir);\n    } else {\n      // TODO: Assert provisioned for devices in the future (this is difficult).\n    }\n    return appDir;\n  }\n  return resolved;\n}\n\nasync function extractIpaAsync(ipaPath: string, outputPath: string): Promise<string> {\n  // Create the output directory if it does not exist\n  if (!fs.existsSync(outputPath)) {\n    fs.mkdirSync(outputPath, { recursive: true });\n  }\n\n  // Use the unzip command to extract the IPA file\n  try {\n    await spawnAsync('unzip', ['-o', ipaPath, '-d', outputPath]);\n  } catch (error: any) {\n    throw new Error(`Error extracting IPA: ${error.message}`);\n  }\n\n  const appBinPaths = await globAsync('Payload/*.app', {\n    cwd: outputPath,\n    absolute: true,\n    maxDepth: 2,\n  });\n\n  if (appBinPaths.length === 0) {\n    throw new Error('No .app directory found in the IPA');\n  }\n\n  return appBinPaths[0];\n}\n\nasync function assertProvisionedForSimulator(appPath: string) {\n  const provisionPath = path.join(appPath, 'embedded.mobileprovision');\n\n  if (!fs.existsSync(provisionPath)) {\n    // This can often result in false positives.\n    debug('No embedded.mobileprovision file found. Likely provisioned for simulator.');\n    return;\n  }\n\n  const provisionData = fs.readFileSync(provisionPath, 'utf8');\n  const start = provisionData.indexOf('<?xml');\n  const end = provisionData.indexOf('</plist>') + 8;\n  const plistData = provisionData.substring(start, end);\n  const parsedData = await parsePlistAsync(plistData);\n\n  const platforms = parsedData['ProvisionsAllDevices'];\n  if (platforms) {\n    throw new CommandError(\n      'The app binary is provisioned for devices, and cannot be run on simulators.'\n    );\n  }\n}\n"], "names": ["getValidBinaryPathAsync", "debug", "require", "input", "props", "resolved", "path", "resolve", "fs", "existsSync", "CommandError", "endsWith", "outputPath", "createTempDirectoryPath", "appDir", "extractIpaAsync", "isSimulator", "assertProvisionedForSimulator", "ipaPath", "mkdirSync", "recursive", "spawnAsync", "error", "Error", "message", "appBinPaths", "globAsync", "cwd", "absolute", "max<PERSON><PERSON><PERSON>", "length", "appPath", "provisionPath", "join", "provisionData", "readFileSync", "start", "indexOf", "end", "plistData", "substring", "parsedData", "parsePlistAsync", "platforms"], "mappings": ";;;;+BAWsBA;;;eAAAA;;;;gEAXC;;;;;;;gEACR;;;;;;;yBACmB;;;;;;;gEACjB;;;;;;gCAEuB;wBACX;uBACG;;;;;;AAEhC,MAAMC,QAAQC,QAAQ,SAAS;AAExB,eAAeF,wBAAwBG,KAAa,EAAEC,KAA+B;IAC1F,MAAMC,WAAWC,eAAI,CAACC,OAAO,CAACJ;IAE9B,IAAI,CAACK,aAAE,CAACC,UAAU,CAACJ,WAAW;QAC5B,MAAM,IAAIK,oBAAY,CAAC,CAAC,2CAA2C,EAAEL,UAAU;IACjF;IAEA,qFAAqF;IACrF,IAAIA,SAASM,QAAQ,CAAC,SAAS;QAC7B,MAAMC,aAAaC,IAAAA,uCAAuB;QAC1CZ,MAAM,mBAAmBI,UAAUO;QACnC,MAAME,SAAS,MAAMC,gBAAgBV,UAAUO;QAE/C,IAAIR,MAAMY,WAAW,EAAE;YACrBC,8BAA8BH;QAChC,OAAO;QACL,0EAA0E;QAC5E;QACA,OAAOA;IACT;IACA,OAAOT;AACT;AAEA,eAAeU,gBAAgBG,OAAe,EAAEN,UAAkB;IAChE,mDAAmD;IACnD,IAAI,CAACJ,aAAE,CAACC,UAAU,CAACG,aAAa;QAC9BJ,aAAE,CAACW,SAAS,CAACP,YAAY;YAAEQ,WAAW;QAAK;IAC7C;IAEA,gDAAgD;IAChD,IAAI;QACF,MAAMC,IAAAA,qBAAU,EAAC,SAAS;YAAC;YAAMH;YAAS;YAAMN;SAAW;IAC7D,EAAE,OAAOU,OAAY;QACnB,MAAM,IAAIC,MAAM,CAAC,sBAAsB,EAAED,MAAME,OAAO,EAAE;IAC1D;IAEA,MAAMC,cAAc,MAAMC,IAAAA,YAAS,EAAC,iBAAiB;QACnDC,KAAKf;QACLgB,UAAU;QACVC,UAAU;IACZ;IAEA,IAAIJ,YAAYK,MAAM,KAAK,GAAG;QAC5B,MAAM,IAAIP,MAAM;IAClB;IAEA,OAAOE,WAAW,CAAC,EAAE;AACvB;AAEA,eAAeR,8BAA8Bc,OAAe;IAC1D,MAAMC,gBAAgB1B,eAAI,CAAC2B,IAAI,CAACF,SAAS;IAEzC,IAAI,CAACvB,aAAE,CAACC,UAAU,CAACuB,gBAAgB;QACjC,4CAA4C;QAC5C/B,MAAM;QACN;IACF;IAEA,MAAMiC,gBAAgB1B,aAAE,CAAC2B,YAAY,CAACH,eAAe;IACrD,MAAMI,QAAQF,cAAcG,OAAO,CAAC;IACpC,MAAMC,MAAMJ,cAAcG,OAAO,CAAC,cAAc;IAChD,MAAME,YAAYL,cAAcM,SAAS,CAACJ,OAAOE;IACjD,MAAMG,aAAa,MAAMC,IAAAA,sBAAe,EAACH;IAEzC,MAAMI,YAAYF,UAAU,CAAC,uBAAuB;IACpD,IAAIE,WAAW;QACb,MAAM,IAAIjC,oBAAY,CACpB;IAEJ;AACF"}