# ProChat Production Environment Configuration

# API Configuration
API_BASE_URL=https://api.prochat.co.tz/api
WS_BASE_URL=wss://api.prochat.co.tz

# App Configuration
APP_NAME=ProChat
APP_VERSION=1.0.0
APP_BUILD_NUMBER=1

# AWS S3 Configuration
AWS_S3_BUCKET=prochat-storage
AWS_REGION=us-east-1
AWS_S3_BASE_URL=https://prochat-storage.s3.amazonaws.com

# Firebase Configuration
FIREBASE_API_KEY=AIzaSyCAQvYg0-s-obt067u0nFdv5I_wwYHnO4o
FIREBASE_PROJECT_ID=prochat-7ff37
FIREBASE_MESSAGING_SENDER_ID=725026416309
FIREBASE_APP_ID=1:725026416309:android:546b7b3b7b75ebb7de80fc

# Payment Configuration
PAYMENT_GATEWAY_URL=https://payments.prochat.co.tz
PROPAY_API_URL=https://propay.prochat.co.tz

# Feature Flags
ENABLE_LIVE_STREAMING=true
ENABLE_PROPAY_WALLET=true
ENABLE_PROZONE=true
ENABLE_EVENTS=true
ENABLE_JOBS=true
ENABLE_NEWS=true

# Analytics
ANALYTICS_ENABLED=true
CRASH_REPORTING_ENABLED=true

# Security
ENABLE_BIOMETRIC_AUTH=true
ENABLE_PIN_LOCK=true
SESSION_TIMEOUT=3600000

# Social Features
ENABLE_STORIES=true
ENABLE_LIVE_CHAT=true
ENABLE_VIDEO_CALLS=true
ENABLE_VOICE_MESSAGES=true

# Business Features
ENABLE_DIGITAL_INVITATIONS=true
ENABLE_GIFT_SYSTEM=true
ENABLE_TASK_REWARDS=true
ENABLE_DONATIONS=true
