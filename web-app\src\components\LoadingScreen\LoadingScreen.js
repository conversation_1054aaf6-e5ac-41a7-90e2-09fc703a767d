import React from 'react';
import { Box, CircularProgress, Typography, LinearProgress } from '@mui/material';
import { keyframes } from '@emotion/react';

// Animation for logo pulse
const pulse = keyframes`
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
`;

// Animation for text fade
const fadeInOut = keyframes`
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
`;

const LoadingScreen = ({ message = 'ProChat inapakia...', showProgress = false, progress = 0 }) => {
  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'primary.main',
        background: 'linear-gradient(135deg, #007AFF 0%, #4DA3FF 100%)',
        zIndex: 9999,
        color: 'white',
      }}
    >
      {/* Logo/Brand */}
      <Box
        sx={{
          mb: 4,
          animation: `${pulse} 2s ease-in-out infinite`,
        }}
      >
        <Typography
          variant="h2"
          sx={{
            fontWeight: 'bold',
            fontSize: { xs: '3rem', md: '4rem' },
            textAlign: 'center',
            textShadow: '0 2px 4px rgba(0,0,0,0.3)',
          }}
        >
          ProChat
        </Typography>
        <Typography
          variant="subtitle1"
          sx={{
            textAlign: 'center',
            opacity: 0.9,
            fontSize: { xs: '0.9rem', md: '1.1rem' },
            mt: 1,
          }}
        >
          Tanzania's Premier Platform
        </Typography>
      </Box>

      {/* Loading Spinner */}
      <Box sx={{ mb: 3 }}>
        <CircularProgress
          size={60}
          thickness={4}
          sx={{
            color: 'white',
            '& .MuiCircularProgress-circle': {
              strokeLinecap: 'round',
            },
          }}
        />
      </Box>

      {/* Loading Message */}
      <Typography
        variant="body1"
        sx={{
          textAlign: 'center',
          animation: `${fadeInOut} 2s ease-in-out infinite`,
          fontSize: { xs: '1rem', md: '1.2rem' },
          mb: showProgress ? 3 : 0,
        }}
      >
        {message}
      </Typography>

      {/* Progress Bar (optional) */}
      {showProgress && (
        <Box sx={{ width: '80%', maxWidth: 400 }}>
          <LinearProgress
            variant="determinate"
            value={progress}
            sx={{
              height: 8,
              borderRadius: 4,
              bgcolor: 'rgba(255,255,255,0.3)',
              '& .MuiLinearProgress-bar': {
                bgcolor: 'white',
                borderRadius: 4,
              },
            }}
          />
          <Typography
            variant="caption"
            sx={{
              display: 'block',
              textAlign: 'center',
              mt: 1,
              opacity: 0.8,
            }}
          >
            {Math.round(progress)}%
          </Typography>
        </Box>
      )}

      {/* Loading Dots */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 60,
          display: 'flex',
          gap: 1,
        }}
      >
        {[0, 1, 2].map((index) => (
          <Box
            key={index}
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              bgcolor: 'white',
              opacity: 0.6,
              animation: `${fadeInOut} 1.5s ease-in-out infinite`,
              animationDelay: `${index * 0.3}s`,
            }}
          />
        ))}
      </Box>

      {/* Version Info */}
      <Typography
        variant="caption"
        sx={{
          position: 'absolute',
          bottom: 20,
          opacity: 0.7,
          fontSize: '0.75rem',
        }}
      >
        Toleo 1.0.0
      </Typography>
    </Box>
  );
};

export default LoadingScreen;
